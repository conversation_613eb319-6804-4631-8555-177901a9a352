package com.z3rd0.common.model;

import jakarta.persistence.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.LocalDateTime;

/**
 * 任务执行详情实体类
 * 记录每次任务执行的详细统计信息，用于分析任务执行效果和调整搜索策略
 */
@Data
@Entity
@Table(name = "task_execution_details",
       indexes = {
           @Index(name = "idx_task_id", columnList = "task_id"),
           @Index(name = "idx_task_name", columnList = "task_name"),
           @Index(name = "idx_start_time", columnList = "start_time"),
           @Index(name = "idx_end_time", columnList = "end_time"),
           @Index(name = "idx_created_at", columnList = "created_at")
       })
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskExecutionDetail {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // 任务基本信息
    @Column(name = "task_id")
    private Long taskId;
    
    @Column(name = "task_name", length = 255, nullable = false)
    private String taskName;
    
    @Column(name = "search_rule", columnDefinition = "TEXT")
    private String searchRule;
    
    // 时间信息
    @Column(name = "start_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Column(name = "end_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    @Column(name = "execution_duration_seconds")
    private Long executionDurationSeconds;
    
    // 结果统计
    @Column(name = "total_results", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer totalResults = 0;
    
    @Column(name = "valid_results", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer validResults = 0;
    
    @Column(name = "duplicate_results", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer duplicateResults = 0;
    
    @Column(name = "out_of_range_results", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer outOfRangeResults = 0;
    
    // 最后资产信息
    @Column(name = "last_asset_ip", length = 45)
    private String lastAssetIp;
    
    @Column(name = "last_asset_port", length = 10)
    private String lastAssetPort;
    
    @Column(name = "last_asset_domain", length = 500)
    private String lastAssetDomain;
    
    @Column(name = "last_asset_discovery_time", length = 30)
    private String lastAssetDiscoveryTime;
    
    // 执行状态
    @Column(name = "execution_status", length = 20, nullable = false)
    private String executionStatus = "COMPLETED";

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    // 重试相关信息
    @Column(name = "retry_count", columnDefinition = "INT DEFAULT 0")
    private Integer retryCount = 0;

    @Column(name = "retry_strategy", length = 50)
    private String retryStrategy;

    @Column(name = "total_retry_delay_ms", columnDefinition = "BIGINT DEFAULT 0")
    private Long totalRetryDelayMs = 0L;

    // 资源使用信息
    @Column(name = "peak_memory_usage_mb", columnDefinition = "BIGINT DEFAULT 0")
    private Long peakMemoryUsageMb = 0L;

    @Column(name = "avg_memory_usage_mb", columnDefinition = "BIGINT DEFAULT 0")
    private Long avgMemoryUsageMb = 0L;

    @Column(name = "browser_restart_count", columnDefinition = "INT DEFAULT 0")
    private Integer browserRestartCount = 0;
    
    // 创建时间
    @Column(name = "created_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (totalResults == null) {
            totalResults = 0;
        }
        if (validResults == null) {
            validResults = 0;
        }
        if (duplicateResults == null) {
            duplicateResults = 0;
        }
        if (outOfRangeResults == null) {
            outOfRangeResults = 0;
        }
        if (executionStatus == null) {
            executionStatus = "COMPLETED";
        }
        if (retryCount == null) {
            retryCount = 0;
        }
        if (totalRetryDelayMs == null) {
            totalRetryDelayMs = 0L;
        }
        if (peakMemoryUsageMb == null) {
            peakMemoryUsageMb = 0L;
        }
        if (avgMemoryUsageMb == null) {
            avgMemoryUsageMb = 0L;
        }
        if (browserRestartCount == null) {
            browserRestartCount = 0;
        }
    }
    
    /**
     * 计算执行耗时（秒）
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.executionDurationSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
        }
    }
    
    /**
     * 设置任务执行失败状态
     * @param errorMsg 错误信息
     */
    public void markAsFailed(String errorMsg) {
        this.executionStatus = "FAILED";
        this.errorMessage = errorMsg;
        if (endTime == null) {
            this.endTime = LocalDateTime.now();
        }
        calculateDuration();
    }
    
    /**
     * 设置任务执行成功状态
     */
    public void markAsCompleted() {
        this.executionStatus = "COMPLETED";
        if (endTime == null) {
            this.endTime = LocalDateTime.now();
        }
        calculateDuration();
    }

    // 新增字段的Getter和Setter方法
    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getRetryStrategy() {
        return retryStrategy;
    }

    public void setRetryStrategy(String retryStrategy) {
        this.retryStrategy = retryStrategy;
    }

    public Long getTotalRetryDelayMs() {
        return totalRetryDelayMs;
    }

    public void setTotalRetryDelayMs(Long totalRetryDelayMs) {
        this.totalRetryDelayMs = totalRetryDelayMs;
    }

    public Long getPeakMemoryUsageMb() {
        return peakMemoryUsageMb;
    }

    public void setPeakMemoryUsageMb(Long peakMemoryUsageMb) {
        this.peakMemoryUsageMb = peakMemoryUsageMb;
    }

    public Long getAvgMemoryUsageMb() {
        return avgMemoryUsageMb;
    }

    public void setAvgMemoryUsageMb(Long avgMemoryUsageMb) {
        this.avgMemoryUsageMb = avgMemoryUsageMb;
    }

    public Integer getBrowserRestartCount() {
        return browserRestartCount;
    }

    public void setBrowserRestartCount(Integer browserRestartCount) {
        this.browserRestartCount = browserRestartCount;
    }
}
