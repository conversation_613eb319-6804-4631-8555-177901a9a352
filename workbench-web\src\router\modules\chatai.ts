import { chatai } from "@/router/enums";

export default {
  path: "/chatai",
  redirect: "/chatai/index",
  meta: {
    icon: "ri/chat-search-line",
    title: "chat-ai",
    rank: chatai,
    showLink: false // 隐藏原有菜单，已归集到示例展示分组
  },
  children: [
    {
      path: "/chatai/index",
      name: "ChatA<PERSON>",
      component: () => import("@/views/chatai/index.vue"),
      meta: {
        title: "chat-ai",
        extraIcon: "IF-pure-iconfont-new svg"
      }
    }
  ]
} satisfies RouteConfigsTable;
