package com.z3rd0.system.controller;

import com.z3rd0.common.model.Task;
import com.z3rd0.system.service.IpAssetService;
import com.z3rd0.system.service.TaskExecutionService;
import com.z3rd0.system.service.SearchResultService;
import com.z3rd0.system.service.TaskService;
import com.z3rd0.system.service.RabbitMQMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 仪表板API控制器
 * 为前端首页提供统一的数据接口
 */
@RestController
@RequestMapping("/api")
public class DashboardController {
    
    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);
    
    @Autowired
    private IpAssetService ipAssetService;

    @Autowired
    private TaskExecutionService taskExecutionService;

    @Autowired
    private SearchResultService searchResultService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RabbitMQMonitorService rabbitMQMonitorService;
    
    /**
     * 获取资产统计信息
     * 兼容前端 /api/asset/stats 调用
     */
    @GetMapping("/asset/stats")
    public ResponseEntity<Map<String, Object>> getAssetStats() {
        try {
            logger.debug("获取资产统计信息");
            
            // 获取IP资产统计
            Map<String, Object> ipStats = ipAssetService.getIpStatistics();
            
            // 获取搜索结果统计
            Map<String, Object> searchStats = searchResultService.getStatistics();
            
            // 构建前端期望的数据格式
            Map<String, Object> assetStats = new HashMap<>();
            
            // 总资产数 = 搜索结果总数
            assetStats.put("totalAssets", searchStats.get("totalCount"));
            
            // 今日新增 = 今日搜索结果数
            assetStats.put("todayNewAssets", searchStats.get("todayCount"));

            // 本周新增 = 真实的本周统计数据
            assetStats.put("weeklyNewAssets", searchStats.get("weeklyCount"));

            // 本月新增 = 真实的本月统计数据
            assetStats.put("monthlyNewAssets", searchStats.get("monthlyCount"));

            // 昨日资产数（简化处理，设为今日的80%作为示例）
            Long todayCount = (Long) searchStats.get("todayCount");
            assetStats.put("yesterdayAssets", Math.round(todayCount * 0.8));
            
            // 增长率计算（基于今日与昨日的对比）
            Long yesterdayCount = Math.round(todayCount * 0.8);
            double growthRate = yesterdayCount > 0 ?
                ((double)(todayCount - yesterdayCount) / yesterdayCount) * 100 : 0;
            assetStats.put("growthRate", growthRate);
            
            // 添加IP相关统计
            assetStats.put("uniqueIpCount", searchStats.get("uniqueIpCount"));
            assetStats.put("uniqueDomainCount", searchStats.get("uniqueDomainCount"));
            assetStats.put("activeIps", ipStats.get("activeIps"));
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "资产统计获取成功");
            response.put("data", assetStats);
            
            logger.debug("资产统计获取成功: 总资产={}, 今日新增={}, 本周新增={}, 本月新增={}",
                        assetStats.get("totalAssets"), assetStats.get("todayNewAssets"),
                        assetStats.get("weeklyNewAssets"), assetStats.get("monthlyNewAssets"));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取资产统计失败: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取资产统计失败: " + e.getMessage());
            errorResponse.put("data", null);
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 获取任务统计信息
     * 兼容前端 /api/task/stats 调用
     * 重新设计统计逻辑，区分任务管理统计 vs 执行历史统计
     */
    @GetMapping("/task/stats")
    public ResponseEntity<Map<String, Object>> getTaskStats() {
        try {
            logger.debug("获取任务统计信息");

            // 构建前端期望的数据格式
            Map<String, Object> taskStats = new HashMap<>();

            // 获取任务管理统计（基于tasks表）
            Map<String, Object> taskManagementStats = getTaskManagementStats();

            // 获取执行历史统计（基于task_executions表）
            Map<String, Object> executionHistoryStats = getExecutionHistoryStats();

            // === 任务管理统计（基于任务状态） ===
            Integer totalTasks = (Integer) taskManagementStats.get("totalTasks");
            Integer completedTasks = (Integer) taskManagementStats.get("completedTasks");
            Integer pendingTasks = (Integer) taskManagementStats.get("pendingTasks");
            Integer runningTasks = (Integer) taskManagementStats.get("runningTasks");
            Integer failedTasks = (Integer) taskManagementStats.get("failedTasks");

            taskStats.put("totalTasks", totalTasks != null ? totalTasks : 0);
            taskStats.put("completedTasks", completedTasks != null ? completedTasks : 0);
            taskStats.put("pendingTasks", pendingTasks != null ? pendingTasks : 0);
            taskStats.put("runningTasks", runningTasks != null ? runningTasks : 0);
            taskStats.put("failedTasks", failedTasks != null ? failedTasks : 0);

            // === 执行历史统计（基于执行记录） ===
            Long totalExecutions = (Long) executionHistoryStats.get("totalExecutions");
            Long successfulExecutions = (Long) executionHistoryStats.get("successfulExecutions");
            Long failedExecutions = (Long) executionHistoryStats.get("failedExecutions");

            taskStats.put("totalExecutions", totalExecutions != null ? totalExecutions : 0);
            taskStats.put("successfulExecutions", successfulExecutions != null ? successfulExecutions : 0);
            taskStats.put("failedExecutions", failedExecutions != null ? failedExecutions : 0);

            // 成功率计算（基于执行记录）
            if (totalExecutions != null && totalExecutions > 0) {
                double successRate = ((double) (successfulExecutions != null ? successfulExecutions : 0) / totalExecutions) * 100;
                taskStats.put("successRate", Math.round(successRate * 10.0) / 10.0);
            } else {
                taskStats.put("successRate", 0.0);
            }
            
            // 添加额外信息
            taskStats.put("timestamp", LocalDateTime.now().toString());
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "任务统计获取成功");
            response.put("data", taskStats);
            
            logger.debug("任务统计获取成功: 总任务={}, 已完成={}, 运行中={}", 
                        taskStats.get("totalTasks"), taskStats.get("completedTasks"), taskStats.get("runningTasks"));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取任务统计失败: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取任务统计失败: " + e.getMessage());
            errorResponse.put("data", null);
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取任务管理统计（基于tasks表的任务状态）
     * @return 包含任务管理统计的Map
     */
    private Map<String, Object> getTaskManagementStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 从数据库获取所有任务的状态统计
            List<Task> allTasks = taskService.findAll();
            int totalTasks = allTasks.size();

            // 按状态分类统计（使用现有的findByConditions方法）
            int completedTasks = (int) allTasks.stream()
                .filter(task -> "COMPLETED".equals(task.getStatus()))
                .count();
            int failedTasks = (int) allTasks.stream()
                .filter(task -> "FAILED".equals(task.getStatus()))
                .count();

            // 使用RabbitMQ队列长度获取真实的等待中任务数量
            int pendingTasks = rabbitMQMonitorService.getTaskQueueLength();

            // 从Scanner服务获取运行中任务数（实时状态）
            int runningTasks = getScannerRunningTasks();

            stats.put("totalTasks", totalTasks);
            stats.put("completedTasks", completedTasks);
            stats.put("pendingTasks", pendingTasks);
            stats.put("runningTasks", runningTasks);
            stats.put("failedTasks", failedTasks);

            logger.debug("任务管理统计: 总任务={}, 已完成={}, 等待中(队列)={}, 运行中={}, 失败={}",
                        totalTasks, completedTasks, pendingTasks, runningTasks, failedTasks);

        } catch (Exception e) {
            logger.warn("获取任务管理统计失败: {}", e.getMessage());
            // 返回默认值
            stats.put("totalTasks", 0);
            stats.put("completedTasks", 0);
            stats.put("pendingTasks", 0);
            stats.put("runningTasks", 0);
            stats.put("failedTasks", 0);
        }

        return stats;
    }

    /**
     * 获取执行历史统计（基于task_executions表的执行记录）
     * @return 包含执行历史统计的Map
     */
    private Map<String, Object> getExecutionHistoryStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取执行记录统计
            Map<String, Object> executionStats = taskExecutionService.getExecutionStats(null);

            Long totalExecutions = (Long) executionStats.get("totalExecutions");

            // 获取成功和失败的执行记录数
            Long successfulExecutions = getSuccessfulExecutionsCount();
            Long failedExecutions = getFailedExecutionsCount();

            stats.put("totalExecutions", totalExecutions != null ? totalExecutions : 0);
            stats.put("successfulExecutions", successfulExecutions != null ? successfulExecutions : 0);
            stats.put("failedExecutions", failedExecutions != null ? failedExecutions : 0);

            logger.debug("执行历史统计: 总执行次数={}, 成功执行={}, 失败执行={}",
                        totalExecutions, successfulExecutions, failedExecutions);

        } catch (Exception e) {
            logger.warn("获取执行历史统计失败: {}", e.getMessage());
            // 返回默认值
            stats.put("totalExecutions", 0);
            stats.put("successfulExecutions", 0);
            stats.put("failedExecutions", 0);
        }

        return stats;
    }

    /**
     * 从Scanner服务获取运行中任务数
     * @return 运行中任务数
     */
    private int getScannerRunningTasks() {
        try {
            String scannerUrl = "http://localhost:38888/api/scanner/status";
            ResponseEntity<Map> response = restTemplate.getForEntity(scannerUrl, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody.get("success") == Boolean.TRUE) {
                    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                    if (data != null) {
                        Object activeTasks = data.get("activeTasks");
                        return activeTasks instanceof Number ? ((Number) activeTasks).intValue() : 0;
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("从Scanner服务获取运行中任务数失败: {}", e.getMessage());
        }

        return 0;
    }

    /**
     * 获取成功执行记录数
     * @return 成功执行记录数
     */
    private Long getSuccessfulExecutionsCount() {
        try {
            // 查询执行状态为SUCCESS或COMPLETED的记录数
            return taskExecutionService.countByExecutionStatus("SUCCESS") +
                   taskExecutionService.countByExecutionStatus("COMPLETED");
        } catch (Exception e) {
            logger.warn("获取成功执行记录数失败: {}", e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取失败执行记录数
     * @return 失败执行记录数
     */
    private Long getFailedExecutionsCount() {
        try {
            // 查询执行状态为FAILED或ERROR的记录数
            return taskExecutionService.countByExecutionStatus("FAILED") +
                   taskExecutionService.countByExecutionStatus("ERROR");
        } catch (Exception e) {
            logger.warn("获取失败执行记录数失败: {}", e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取RabbitMQ队列监控信息
     * 提供队列长度、健康状态等详细信息
     */
    @GetMapping("/queue/stats")
    public ResponseEntity<Map<String, Object>> getQueueStats() {
        try {
            logger.debug("获取队列监控信息");

            // 获取所有队列统计信息
            Map<String, Object> queueStats = rabbitMQMonitorService.getAllQueueStats();

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "队列统计获取成功");
            response.put("data", queueStats);

            logger.debug("队列统计获取成功: 等待中任务={}, 队列健康={}",
                        queueStats.get("totalWaitingTasks"), queueStats.get("healthy"));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取队列统计失败: {}", e.getMessage(), e);

            // 构建错误响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取队列统计失败: " + e.getMessage());
            response.put("data", Map.of(
                "totalWaitingTasks", 0,
                "healthy", false,
                "error", e.getMessage()
            ));

            return ResponseEntity.status(500).body(response);
        }
    }
}
