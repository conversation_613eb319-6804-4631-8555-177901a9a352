server:
  port: 38888

spring:
  application:
    name: workbench-scanner
  
  # 数据库配置
  datasource:
    url: ${DB_URL:**************************************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:Z3ride@0322}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
    open-in-view: false
  
  # RabbitMQ配置
  rabbitmq:
    host: ${RABBITMQ_HOST:**********}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:Z3rd0@0322!}
    listener:
      simple:
        concurrency: 1
        max-concurrency: 1
        prefetch: 1
        retry:
          enabled: true
          initial-interval: 5000
          max-attempts: 3
          multiplier: 1.5

# 日志配置
logging:
  level:
    root: INFO
    com.z3rd0.workbench: DEBUG
    org.hibernate.SQL: DEBUG
  file:
    name: logs/workbench-scanner.log
  charset:
    file: UTF-8
    console: UTF-8

# 爬虫引擎配置
crawler:
  account:
    # 登录凭据 - 使用环境变量保护敏感信息
    username: ${CRAWLER_USERNAME:mrmy0519}
    password: ${CRAWLER_PASSWORD:Zhou051988...}
    # 备用账号配置（如需要）
    # username: ${CRAWLER_USERNAME_BACKUP:}
    # password: ${CRAWLER_PASSWORD_BACKUP:}
    # 账号等级: normal(普通)或vip
    level: normal
    # 数据量限制
    limit:
      # 普通账号每任务最多抓取数据量
      normal: 500
      # VIP账号每任务最多抓取数据量
      vip: 10000
  
  # 任务处理配置
  task:
    # 连续超出时间范围的数据量阈值，超过后停止处理
    out-of-range-threshold: 9

  # 资源监控配置
  resource:
    memory:
      # 内存使用警告阈值 (0.0-1.0)
      warning-threshold: 0.8
      # 内存使用严重阈值 (0.0-1.0)
      critical-threshold: 0.9
    browser:
      # 浏览器最大内存使用量 (MB)
      max-memory-mb: 1024
      # 浏览器最大运行时间 (分钟)
      max-runtime-minutes: 120
  
  # 浏览器配置
  browser:
    # 浏览器空闲超时时间(毫秒)，超过此时间将自动关闭浏览器
    idle-timeout: 1800000
    # 夜间模式开始时间(24小时制，0-23)，此时间段内浏览器将自动关闭以节省资源
    night-mode-start-hour: 0
    # 夜间模式结束时间(24小时制，0-23)
    night-mode-end-hour: 6
    # 网络超时配置(毫秒)
    page-timeout: 120000
    navigation-timeout: 90000
    # 登录重试配置
    login-retry-count: 3
    login-retry-delay: 10000
    # 启动配置
    startup:
      # 是否在应用启动时预初始化浏览器
      pre-initialize: true
      # 启动时浏览器初始化失败是否阻塞应用启动
      fail-fast: false
      # 启动时浏览器初始化超时时间(秒)
      init-timeout: 120
      # 启动时浏览器初始化重试次数
      init-retry-count: 2
      # 启动时浏览器初始化重试间隔(毫秒)
      init-retry-delay: 15000

# 应用配置
workbench:
  # 验证码配置
  captcha:
    enabled: true                    # 是否启用验证码检测
    wait-timeout: 300                # 验证码等待超时时间（秒）
    check-interval: 2                # 检查间隔时间（秒）
    show-progress: true              # 是否显示等待进度
    progress-interval: 10            # 进度显示间隔（秒）
    use-request-detection: true      # 是否使用网络请求检测验证码
    captcha-request-keyword: "captcha.bpd.360.cn"  # 验证码请求URL关键词
    captcha-verify-keyword: "captcha.bpd.360.cn/v1/verify"  # 验证码验证完成请求关键词
    request-detection-delay: 5000    # 点击登录后等待网络请求的时间（毫秒）
    custom-selectors:                # 自定义验证码选择器（DOM检测备用）
      - ".custom-captcha"
      - "#custom-verify"