package com.z3rd0.workbench.event;

/**
 * 任务进度事件
 * 用于通知任务进度更新
 */
public class TaskProgressEvent extends TaskEvent {
    private final String progress;
    private final int processedItems;
    
    public TaskProgressEvent(String taskName, String progress, int processedItems) {
        super(taskName);
        this.progress = progress;
        this.processedItems = processedItems;
    }
    
    public String getProgress() {
        return progress;
    }
    
    public int getProcessedItems() {
        return processedItems;
    }
} 