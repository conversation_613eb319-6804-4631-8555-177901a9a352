-- 为任务执行详情表添加重试相关字段
-- 用于记录智能重试策略的执行情况和统计信息

ALTER TABLE task_execution_details 
ADD COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数';

ALTER TABLE task_execution_details 
ADD COLUMN retry_strategy VARCHAR(50) COMMENT '使用的重试策略';

ALTER TABLE task_execution_details 
ADD COLUMN total_retry_delay_ms BIGINT DEFAULT 0 COMMENT '总重试延迟时间(毫秒)';

-- 为现有记录设置默认值
UPDATE task_execution_details SET retry_count = 0 WHERE retry_count IS NULL;
UPDATE task_execution_details SET total_retry_delay_ms = 0 WHERE total_retry_delay_ms IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_execution_details_retry_count ON task_execution_details(retry_count);
CREATE INDEX idx_execution_details_retry_strategy ON task_execution_details(retry_strategy);
