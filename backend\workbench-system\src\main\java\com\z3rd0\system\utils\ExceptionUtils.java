package com.z3rd0.system.utils;

import com.z3rd0.system.exception.BusinessException;
import com.z3rd0.system.exception.DataAccessException;
import com.z3rd0.system.exception.TaskException;

/**
 * 异常工具类
 * 提供便捷的异常创建和处理方法
 */
public final class ExceptionUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private ExceptionUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    // ==================== 任务异常工厂方法 ====================

    /**
     * 创建任务不存在异常
     */
    public static TaskException.TaskNotFoundException taskNotFound(Long taskId) {
        return new TaskException.TaskNotFoundException(taskId);
    }

    /**
     * 创建任务状态异常
     */
    public static TaskException.InvalidTaskStatusException invalidTaskStatus(String currentStatus, String expectedStatus) {
        return new TaskException.InvalidTaskStatusException(currentStatus, expectedStatus);
    }

    /**
     * 创建任务参数异常
     */
    public static TaskException.InvalidTaskParameterException invalidTaskParameter(String parameterName, String reason) {
        return new TaskException.InvalidTaskParameterException(parameterName, reason);
    }

    /**
     * 创建任务执行异常
     */
    public static TaskException.TaskExecutionException taskExecutionFailed(Long taskId, String reason, Throwable cause) {
        return new TaskException.TaskExecutionException(taskId, reason, cause);
    }

    /**
     * 创建任务重复异常
     */
    public static TaskException.DuplicateTaskException duplicateTask(String taskName) {
        return new TaskException.DuplicateTaskException(taskName);
    }

    // ==================== 数据访问异常工厂方法 ====================

    /**
     * 创建数据不存在异常
     */
    public static DataAccessException.DataNotFoundException dataNotFound(String entityType, Object id) {
        return new DataAccessException.DataNotFoundException(entityType, id);
    }

    /**
     * 创建数据重复异常
     */
    public static DataAccessException.DuplicateDataException duplicateData(String entityType, String field, Object value) {
        return new DataAccessException.DuplicateDataException(entityType, field, value);
    }

    /**
     * 创建数据完整性异常
     */
    public static DataAccessException.DataIntegrityException dataIntegrityViolation(String reason, Throwable cause) {
        return new DataAccessException.DataIntegrityException(reason, cause);
    }

    /**
     * 创建数据库连接异常
     */
    public static DataAccessException.DatabaseConnectionException databaseConnectionFailed(String reason, Throwable cause) {
        return new DataAccessException.DatabaseConnectionException(reason, cause);
    }

    /**
     * 创建查询超时异常
     */
    public static DataAccessException.QueryTimeoutException queryTimeout(String query, long timeoutMs) {
        return new DataAccessException.QueryTimeoutException(query, timeoutMs);
    }

    // ==================== 通用业务异常工厂方法 ====================

    /**
     * 创建业务异常
     */
    public static BusinessException businessError(String message) {
        return new BusinessException(message);
    }

    /**
     * 创建带错误代码的业务异常
     */
    public static BusinessException businessError(String message, String errorCode) {
        return new BusinessException(message, errorCode);
    }

    /**
     * 创建带详细信息的业务异常
     */
    public static BusinessException businessError(String message, String errorCode, Object details) {
        return new BusinessException(message, errorCode, details);
    }

    /**
     * 创建带原因的业务异常
     */
    public static BusinessException businessError(String message, String errorCode, Throwable cause) {
        return new BusinessException(message, errorCode, cause);
    }

    // ==================== 异常检查和转换方法 ====================

    /**
     * 检查参数不为空，否则抛出异常
     */
    public static void requireNonNull(Object obj, String parameterName) {
        if (obj == null) {
            throw invalidTaskParameter(parameterName, "参数不能为空");
        }
    }

    /**
     * 检查字符串不为空，否则抛出异常
     */
    public static void requireNonEmpty(String str, String parameterName) {
        if (str == null || str.trim().isEmpty()) {
            throw invalidTaskParameter(parameterName, "参数不能为空或空白");
        }
    }

    /**
     * 检查条件为真，否则抛出异常
     */
    public static void requireTrue(boolean condition, String message) {
        if (!condition) {
            throw businessError(message, "CONDITION_NOT_MET");
        }
    }

    /**
     * 检查条件为真，否则抛出指定异常
     */
    public static void requireTrue(boolean condition, RuntimeException exception) {
        if (!condition) {
            throw exception;
        }
    }

    /**
     * 检查实体存在，否则抛出数据不存在异常
     */
    public static <T> T requireExists(T entity, String entityType, Object id) {
        if (entity == null) {
            throw dataNotFound(entityType, id);
        }
        return entity;
    }

    /**
     * 安全地获取异常的根本原因
     */
    public static Throwable getRootCause(Throwable throwable) {
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        return rootCause;
    }

    /**
     * 获取异常的简短描述
     */
    public static String getShortDescription(Throwable throwable) {
        if (throwable == null) {
            return "Unknown error";
        }
        
        String className = throwable.getClass().getSimpleName();
        String message = throwable.getMessage();
        
        if (message != null && !message.isEmpty()) {
            return className + ": " + message;
        } else {
            return className;
        }
    }

    /**
     * 检查异常是否为业务异常
     */
    public static boolean isBusinessException(Throwable throwable) {
        return throwable instanceof BusinessException;
    }

    /**
     * 检查异常是否为系统异常
     */
    public static boolean isSystemException(Throwable throwable) {
        return !isBusinessException(throwable);
    }

    /**
     * 将异常转换为业务异常
     */
    public static BusinessException toBusinessException(Throwable throwable, String defaultMessage) {
        if (throwable instanceof BusinessException) {
            return (BusinessException) throwable;
        }
        
        return new BusinessException(
            defaultMessage != null ? defaultMessage : throwable.getMessage(),
            "SYSTEM_ERROR",
            throwable
        );
    }
}
