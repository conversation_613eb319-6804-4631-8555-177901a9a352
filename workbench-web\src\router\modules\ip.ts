import { $t } from "@/plugins/i18n";
import { ip } from "@/router/enums";
import type { RouteConfigsTable } from "~/types/router";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/ip",
  name: "IpList",
  component: Layout,
  redirect: "/ip/index",
  meta: {
    icon: "ep:connection",
    title: "IP列表",
    rank: ip
  },
  children: [
    {
      path: "/ip/index",
      name: "IpManagement",
      component: () => import("@/views/ip/index.vue"),
      meta: {
        title: "IP列表"
      }
    },
    {
      path: "/ip/:ip",
      name: "IpDetail",
      component: () => import("@/views/ip/detail.vue"),
      meta: {
        title: $t("menus.hsIpDetail"),
        activePath: "/ip/index",
        showLink: false  // 隐藏在侧边栏菜单中，只能通过编程方式访问
      },
      beforeEnter: (to, from, next) => {
        // 增强的IP参数验证
        const ipParam = to.params.ip;

        // 检查参数是否存在且有效
        if (!ipParam ||
            typeof ipParam !== 'string' ||
            ipParam.trim() === '' ||
            ipParam === 'undefined' ||
            ipParam === 'null') {

          console.error('Missing or invalid required param "ip":', ipParam);

          // 显示用户友好的错误提示
          if (typeof window !== 'undefined' && window.ElMessage) {
            window.ElMessage.error('IP地址参数无效，正在返回列表页面');
          }

          // 重定向到IP列表页面
          next({ path: '/ip/index', replace: true });
          return;
        }

        // 参数有效，继续导航
        next();
      }
    }
  ]
} satisfies RouteConfigsTable;
