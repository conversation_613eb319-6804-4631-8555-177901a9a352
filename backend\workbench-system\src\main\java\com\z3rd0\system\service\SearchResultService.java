package com.z3rd0.system.service;

import com.z3rd0.common.model.SearchResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface SearchResultService {
    Page<SearchResult> findByIpPaged(String ip, int page, int size);
    Page<SearchResult> findByConditions(String ip, String domain, String title, String org, String statusCode, String locationCountry, String asn, Integer isRead, Integer isExcluded, String startTime, String endTime, Pageable pageable);
    Map<String, Object> getStatistics();
    void exportToExcel(String ip, String domain, String title, String org, String statusCode, String locationCountry, String asn, Integer isRead, Integer isExcluded, String startTime, String endTime, HttpServletResponse response) throws IOException;
    void batchOperation(List<Long> ids, String operation, Integer value);
    void batchDelete(List<Long> ids);
}