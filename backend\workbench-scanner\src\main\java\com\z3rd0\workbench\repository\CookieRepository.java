package com.z3rd0.workbench.repository;

import com.z3rd0.common.model.CookieRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Cookie存储库，用于对cookie_records表进行操作
 */
@Repository
public interface CookieRepository extends JpaRepository<CookieRecord, Long> {

    /**
     * 查找最新的cookie记录（按更新时间排序）
     * @return 最新的cookie记录
     */
    @Query(value = "SELECT * FROM cookie_records ORDER BY update_time DESC LIMIT 1", nativeQuery = true)
    Optional<CookieRecord> findLatestCookie();
    
    /**
     * 清空所有cookie记录
     */
    @Query("DELETE FROM CookieRecord")
    void deleteAllCookies();
} 