package com.z3rd0.workbench.processor;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.z3rd0.workbench.utils.PlaywrightUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 360Quake设备处理器
 * 主要用于管理设备的添加、移除等操作
 */
@Component
public class ProcessorQuake360 {
    private static final Logger logger = LoggerFactory.getLogger(ProcessorQuake360.class);
    
    @Autowired
    private PlaywrightUtils playwrightUtils;
    
    /**
     * 线程休眠辅助方法
     */
    private void sleep(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("线程休眠被中断: {}", e.getMessage());
        }
    }

/**
 * 移除已添加的设备
 * 基于Element UI表格的固定列结构精确定位移除按钮
 */
public boolean removeDevices(Page page, List<String> ids) {
    boolean result = false;
    try {
        logger.info("准备移除设备: {}", ids);
        
        // 点击"我的资产"菜单
        Locator assetsMenu = page.locator("text='我的资产'");
        assetsMenu.click();
        sleep(2000);
        
        // 点击"已添加设备"标签
        Locator addedDeviceTab = page.locator("div[role='tab']:has-text('已添加设备')");
        addedDeviceTab.click();
        sleep(2000);

        // 等待表格加载完成
        page.waitForSelector(".el-table__fixed-right", new Page.WaitForSelectorOptions().setTimeout(5000));
        logger.info("表格已加载完成");
        
        // 获取分页信息，了解总行数
        String paginationText = page.locator(".el-pagination__total").textContent();
        logger.info("分页信息: {}", paginationText);
        
        // 根据新的HTML结构，直接定位固定列中的表格行（这是实际可见的行）
        Locator fixedRows = page.locator(".el-table__fixed-right .el-table__fixed-body-wrapper tr.el-table__row");
        int rowCount = fixedRows.count();
        logger.info("固定列中找到 {} 行数据", rowCount);
        
        // 创建设备ID与行索引的映射
        Map<String, Integer> deviceRowMap = new HashMap<>();
        
        // 遍历所有行，获取设备ID和对应的行索引
        for (int i = 0; i < rowCount; i++) {
            // 使用JavaScript获取行对应的设备ID
            // 注意：设备ID在主表格中，我们需要通过行索引关联
            String deviceId = (String) page.evaluate(
                "rowIndex => {" +
                "  // 获取主表格中对应行的UUID列内容" +
                "  const mainRows = document.querySelectorAll('.el-table__body-wrapper tbody tr');" +
                "  if (rowIndex < mainRows.length) {" +
                "    const idCell = mainRows[rowIndex].querySelector('.el-table_1_column_2 .cell');" +
                "    return idCell ? idCell.textContent.trim() : '';" +
                "  }" +
                "  return '';" +
                "}", i
            );
            
            if (!deviceId.isEmpty()) {
                deviceRowMap.put(deviceId, i);
                logger.info("设备行 {}: ID='{}'", i, deviceId);
            }
        }

        // 为每个需要移除的设备ID执行操作
        for (String deviceId : ids) {
            logger.info("处理设备 ID: {}", deviceId);
            
            // 在映射中查找设备ID对应的行索引
            Integer rowIndex = deviceRowMap.get(deviceId);
            if (rowIndex == null) {
                logger.warn("未找到设备ID为 {} 的行", deviceId);
                continue;
            }
            
            try {
                // 关键改进：直接定位固定列中的移除按钮（这些是实际可见且可点击的按钮）
                // 固定列中的按钮有特定的data-v-5522e23e属性和类名
                Locator removeButton = fixedRows.nth(rowIndex)
                    .locator("td.el-table_1_column_5 button[data-v-5522e23e].el-button--text");
                
                if (removeButton.count() > 0) {
                    logger.info("找到第 {} 行的移除按钮，准备点击", rowIndex);
                    
                    // 点击按钮
                    removeButton.click();
                    
                    // 处理确认对话框
                    playwrightUtils.handleConfirmDialog();
                    result = true;
                } else {
                    logger.warn("在固定列的第 {} 行未找到移除按钮，尝试备用方案", rowIndex);
                    
                    // 备用方案1：使用更精确的选择器定位按钮文本
                    Locator textButton = fixedRows.nth(rowIndex)
                        .locator("span[data-v-5522e23e]:has-text('移除')");
                    
                    if (textButton.count() > 0) {
                        logger.info("使用文本定位到移除按钮，准备点击");
                        textButton.click();
                        playwrightUtils.handleConfirmDialog();
                        result = true;
                    } else {
                        // 备用方案2：使用JavaScript直接定位并点击固定列中的按钮
                        logger.info("尝试使用JavaScript定位并点击固定列中的移除按钮");
                        boolean jsClicked = (boolean) page.evaluate(
                            "rowIndex => {" +
                            "  try {" +
                            "    // 获取固定列中的行" +
                            "    const fixedRows = document.querySelectorAll('.el-table__fixed-right .el-table__fixed-body-wrapper tr.el-table__row');" +
                            "    if (rowIndex >= fixedRows.length) return false;" +
                            "    " +
                            "    // 获取当前行" +
                            "    const row = fixedRows[rowIndex];" +
                            "    " +
                            "    // 获取操作列" +
                            "    const operationCell = row.querySelector('td.el-table_1_column_5');" +
                            "    if (!operationCell) return false;" +
                            "    " +
                            "    // 获取移除按钮" +
                            "    const button = operationCell.querySelector('button[data-v-5522e23e]');" +
                            "    if (!button) return false;" +
                            "    " +
                            "    // 检查按钮是否包含'移除'文本" +
                            "    if (!button.textContent.includes('移除')) return false;" +
                            "    " +
                            "    // 确保按钮可见" +
                            "    if (button.offsetParent === null) return false;" +
                            "    " +
                            "    // 记录按钮信息以便调试" +
                            "    console.log('找到移除按钮:', {" +
                            "      text: button.textContent.trim()," +
                            "      visible: button.offsetParent !== null," +
                            "      classes: button.className" +
                            "    });" +
                            "    " +
                            "    // 点击按钮" +
                            "    button.click();" +
                            "    console.log('通过JS成功点击了移除按钮');" +
                            "    return true;" +
                            "  } catch (error) {" +
                            "    console.error('JS点击出错:', error);" +
                            "    return false;" +
                            "  }" +
                            "}", rowIndex
                        );
                        
                        if (jsClicked) {
                            logger.info("通过JavaScript成功点击了第 {} 行的移除按钮", rowIndex);
                            playwrightUtils.handleConfirmDialog();
                            result = true;
                        } else {
                            logger.error("无法通过任何方式点击第 {} 行的移除按钮", rowIndex);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("移除设备 {} 时出错: {}", deviceId, e.getMessage(), e);
            }
            
            // 等待页面刷新
            sleep(3000);
        }
    } catch (Exception e) {
        logger.error("移除设备过程中发生错误: {}", e.getMessage(), e);
    }
    
    return result;
}

} 