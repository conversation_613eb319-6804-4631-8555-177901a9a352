import { $t } from "@/plugins/i18n";
import { codemirror } from "@/router/enums";

export default {
  path: "/codemirror",
  redirect: "/codemirror/index",
  meta: {
    icon: "ri/code-box-line",
    title: $t("menus.pureCodeMirror"),
    rank: codemirror,
    showLink: false // 隐藏原有菜单，已归集到示例展示分组
  },
  children: [
    {
      path: "/codemirror/index",
      name: "CodeMirror",
      component: () => import("@/views/codemirror/index.vue"),
      meta: {
        title: $t("menus.pureCodeMirror"),
        extraIcon: "IF-pure-iconfont-new svg"
      }
    }
  ]
} satisfies RouteConfigsTable;
