package com.z3rd0.system.dto;

import lombok.Data;
import java.util.List;

/**
 * 批量创建任务请求DTO
 */
@Data
public class BatchTaskCreateRequest {
    
    /**
     * 关键字列表，每个关键字将生成一个任务
     */
    private List<String> keywords;
    
    /**
     * 任务名称前缀（可选），最终任务名称为：前缀 + 关键字
     */
    private String namePrefix;
    
    /**
     * 查询模板，包含 {关键字} 占位符
     * 默认模板：title: "{关键字}" AND country: "China" AND -province_cn: "香港" AND -title: "首页" AND -favicon: "de84ef1e9248d5ce5847f451ea07d3c0" AND -favicon: "4214c76ef244357607f92bc658a03d65" AND -favicon: "de84ef1e9248d5ce5847f451ea07d3c0" AND -favicon: "0f86df4b41fc0ff3f9cede3a01316e81"
     */
    private String queryTemplate;
    
    /**
     * 任务优先级（可选），默认为5
     */
    private Integer priority;
    
    /**
     * 获取默认查询模板
     */
    public String getQueryTemplate() {
        if (queryTemplate == null || queryTemplate.trim().isEmpty()) {
            return "title: \"{关键字}\" AND country: \"China\" AND -province_cn: \"香港\" AND -title: \"首页\" AND -favicon: \"de84ef1e9248d5ce5847f451ea07d3c0\" AND -favicon: \"4214c76ef244357607f92bc658a03d65\" AND -favicon: \"de84ef1e9248d5ce5847f451ea07d3c0\" AND -favicon: \"0f86df4b41fc0ff3f9cede3a01316e81\"";
        }
        return queryTemplate;
    }
    
    /**
     * 获取任务优先级，默认为5
     */
    public Integer getPriority() {
        return priority != null ? priority : 5;
    }
    
    /**
     * 获取任务名称前缀，默认为空字符串
     */
    public String getNamePrefix() {
        return namePrefix != null ? namePrefix : "";
    }
}
