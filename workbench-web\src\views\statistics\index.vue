<template>
  <div class="statistics-container">
    <div class="page-header">
      <h2>数据统计分析</h2>
      <p>搜索结果的可视化统计分析</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalCount }}</div>
            <div class="stat-label">总记录数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ uniqueIpCount }}</div>
            <div class="stat-label">唯一IP数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ uniqueDomainCount }}</div>
            <div class="stat-label">唯一域名数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ todayCount }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 状态码分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>状态码分布</span>
          </template>
          <div ref="statusCodeChart" class="chart-container" />
        </el-card>
      </el-col>

      <!-- 地理位置分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>地理位置分布 (Top 10)</span>
          </template>
          <div ref="locationChart" class="chart-container" />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <!-- 时间趋势 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>时间趋势 (最近7天)</span>
          </template>
          <div ref="timeChart" class="chart-container" />
        </el-card>
      </el-col>

      <!-- 端口分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>常见端口分布</span>
          </template>
          <div ref="portChart" class="chart-container" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 技术栈统计 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="24">
        <el-card class="chart-card">
          <template #header>
            <span>技术栈统计 (Top 20)</span>
          </template>
          <div ref="techChart" class="chart-container tech-chart" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import * as echarts from "echarts";

// 图表引用
const statusCodeChart = ref<HTMLElement>();
const locationChart = ref<HTMLElement>();
const timeChart = ref<HTMLElement>();
const portChart = ref<HTMLElement>();
const techChart = ref<HTMLElement>();

// 统计数据
const totalCount = ref(0);
const uniqueIpCount = ref(0);
const uniqueDomainCount = ref(0);
const todayCount = ref(0);

// 图表实例
let statusChart: echarts.ECharts;
let locChart: echarts.ECharts;
let trendChart: echarts.ECharts;
let pChart: echarts.ECharts;
let tChart: echarts.ECharts;

onMounted(async () => {
  await loadStatistics();
  await nextTick();
  initCharts();
});

// 加载统计数据
async function loadStatistics() {
  try {
    const response = await fetch("/api/search/statistics");
    const result = await response.json();

    if (result.success) {
      const data = result.data;
      totalCount.value = data.totalCount || 0;
      uniqueIpCount.value = data.uniqueIpCount || 0;
      uniqueDomainCount.value = data.uniqueDomainCount || 0;
      todayCount.value = data.todayCount || 0;

      // 初始化图表数据
      initChartsData(data);
    }
  } catch (error) {
    console.error("加载统计数据失败:", error);
  }
}

// 初始化图表
function initCharts() {
  if (statusCodeChart.value) {
    statusChart = echarts.init(statusCodeChart.value);
  }
  if (locationChart.value) {
    locChart = echarts.init(locationChart.value);
  }
  if (timeChart.value) {
    trendChart = echarts.init(timeChart.value);
  }
  if (portChart.value) {
    pChart = echarts.init(portChart.value);
  }
  if (techChart.value) {
    tChart = echarts.init(techChart.value);
  }
}

// 初始化图表数据
function initChartsData(data: any) {
  // 状态码分布饼图
  if (statusChart && data.statusCodeStats) {
    const statusOption = {
      tooltip: { trigger: "item" },
      series: [
        {
          type: "pie",
          radius: "70%",
          data: data.statusCodeStats.map((item: any) => ({
            name: item.statusCode,
            value: item.count
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)"
            }
          }
        }
      ]
    };
    statusChart.setOption(statusOption);
  }

  // 地理位置分布柱状图
  if (locChart && data.locationStats) {
    const locationOption = {
      tooltip: { trigger: "axis" },
      xAxis: {
        type: "category",
        data: data.locationStats.map((item: any) => item.location),
        axisLabel: { rotate: 45 }
      },
      yAxis: { type: "value" },
      series: [
        {
          type: "bar",
          data: data.locationStats.map((item: any) => item.count),
          itemStyle: { color: "#5470c6" }
        }
      ]
    };
    locChart.setOption(locationOption);
  }

  // 时间趋势线图
  if (trendChart && data.timeStats) {
    const timeOption = {
      tooltip: { trigger: "axis" },
      xAxis: {
        type: "category",
        data: data.timeStats.map((item: any) => item.date)
      },
      yAxis: { type: "value" },
      series: [
        {
          type: "line",
          data: data.timeStats.map((item: any) => item.count),
          smooth: true,
          itemStyle: { color: "#91cc75" }
        }
      ]
    };
    trendChart.setOption(timeOption);
  }

  // 端口分布柱状图
  if (pChart && data.portStats) {
    const portOption = {
      tooltip: { trigger: "axis" },
      xAxis: {
        type: "category",
        data: data.portStats.map((item: any) => item.port)
      },
      yAxis: { type: "value" },
      series: [
        {
          type: "bar",
          data: data.portStats.map((item: any) => item.count),
          itemStyle: { color: "#fac858" }
        }
      ]
    };
    pChart.setOption(portOption);
  }

  // 技术栈统计柱状图
  if (tChart && data.techStats) {
    const techOption = {
      tooltip: { trigger: "axis" },
      xAxis: {
        type: "category",
        data: data.techStats.map((item: any) => item.tech),
        axisLabel: { rotate: 45 }
      },
      yAxis: { type: "value" },
      series: [
        {
          type: "bar",
          data: data.techStats.map((item: any) => item.count),
          itemStyle: { color: "#ee6666" }
        }
      ]
    };
    tChart.setOption(techOption);
  }
}
</script>

<style scoped>
.statistics-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 10px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.tech-chart {
  height: 320px;
}
</style>
