import { http } from "@/utils/http";

// 统一的API响应格式
type ApiResponse<T = any> = {
  success: boolean;
  message: string;
  data?: T;
  errorCode?: string;
  timestamp: string;
};

// 分页响应格式
type PageResponse<T = any> = {
  list: Array<T>;
  total: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
  isFirst: boolean;
  isLast: boolean;
};

// 兼容旧格式的类型定义
type Result = ApiResponse<any>;
type ResultTable = ApiResponse<PageResponse<any>>;

// 任务启动请求
type TaskStartRequest = {
  timeRange: string;
};

// 任务启动结果
type TaskStartResult = {
  taskId: number;
  taskName: string;
  timeRange: string;
  message: string;
};

// 批量启动结果
type BatchStartResult = {
  totalTasks: number;
  successCount: number;
  failCount: number;
  timeRange: string;
  successRate: number;
};

/** 获取系统管理-用户管理列表 */
export const getUserList = (data?: object) => {
  return http.request<ResultTable>("post", "/user", { data });
};

/** 系统管理-用户管理-获取所有角色列表 */
export const getAllRoleList = () => {
  return http.request<Result>("get", "/list-all-role");
};

/** 系统管理-用户管理-根据userId，获取对应角色id列表（userId：用户id） */
export const getRoleIds = (data?: object) => {
  return http.request<Result>("post", "/list-role-ids", { data });
};

/** 任务管理-获取任务列表 */
export const getTaskList = (params?: object) => {
  return http.request<ResultTable>("get", "/tasks", { params });
};

/** 任务管理-创建任务 */
export const createTask = (data: object) => {
  return http.request<Result>("post", "/tasks", { data });
};

/** 任务管理-更新任务 */
export const updateTask = (id: number, data: object) => {
  return http.request<Result>("put", `/tasks/${id}`, { data });
};

/** 任务管理-删除任务 */
export const deleteTask = (id: number) => {
  return http.request<Result>("delete", `/tasks/${id}`);
};

/** 任务管理-启动单个任务 */
export const startTask = (id: number, data: TaskStartRequest) => {
  return http.request<ApiResponse<TaskStartResult>>(
    "post",
    `/tasks/${id}/start`,
    { data }
  );
};

/** 任务管理-批量启动所有任务 */
export const startAllTasks = (data: TaskStartRequest) => {
  return http.request<ApiResponse<BatchStartResult>>(
    "post",
    "/tasks/start-all",
    { data }
  );
};

/** 获取系统管理-角色管理列表 */
export const getRoleList = (data?: object) => {
  return http.request<ResultTable>("post", "/role", { data });
};

/** 获取系统管理-菜单管理列表 */
export const getMenuList = (data?: object) => {
  return http.request<Result>("post", "/menu", { data });
};

/** 获取系统管理-部门管理列表 */
export const getDeptList = (data?: object) => {
  return http.request<Result>("post", "/dept", { data });
};

/** 获取系统监控-在线用户列表 */
export const getOnlineLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/online-logs", { data });
};

/** 获取系统监控-登录日志列表 */
export const getLoginLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/login-logs", { data });
};

/** 获取系统监控-操作日志列表 */
export const getOperationLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/operation-logs", { data });
};

/** 获取系统监控-系统日志列表 */
export const getSystemLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/system-logs", { data });
};

/** 获取系统监控-系统日志-根据 id 查日志详情 */
export const getSystemLogsDetail = (data?: object) => {
  return http.request<Result>("post", "/system-logs-detail", { data });
};

/** 获取角色管理-权限-菜单权限 */
export const getRoleMenu = (data?: object) => {
  return http.request<Result>("post", "/role-menu", { data });
};

/** 获取角色管理-权限-菜单权限-根据角色 id 查对应菜单 */
export const getRoleMenuIds = (data?: object) => {
  return http.request<Result>("post", "/role-menu-ids", { data });
};
