-- 增强search_results表，添加更多Quake数据字段
-- 版本: V4
-- 描述: 添加网络扩展信息、技术栈、ICP备案、网站元信息等字段

-- 添加网络基础信息字段
ALTER TABLE search_results 
ADD COLUMN hostname VARCHAR(200) COMMENT '主机名',
ADD COLUMN transport VARCHAR(20) COMMENT '传输协议',
ADD COLUMN org VARCHAR(200) COMMENT '组织信息';

-- 添加网络扩展信息字段
ALTER TABLE search_results 
ADD COLUMN asn INT COMMENT '自治系统号',
ADD COLUMN is_ipv6 BOOLEAN DEFAULT FALSE COMMENT '是否支持IPv6',
ADD COLUMN sys_tag VARCHAR(500) COMMENT '系统标签';

-- 扩展现有字段长度
ALTER TABLE search_results 
MODIFY COLUMN domain VARCHAR(500) COMMENT '域名',
MODIFY COLUMN title VARCHAR(1000) COMMENT '网站标题',
MODIFY COLUMN url VARCHAR(2000) COMMENT '完整URL',
MODIFY COLUMN content LONGTEXT COMMENT '网页内容';

-- 添加网站信息字段
ALTER TABLE search_results 
ADD COLUMN host VARCHAR(200) COMMENT 'HTTP主机',
ADD COLUMN server VARCHAR(100) COMMENT '服务器信息',
ADD COLUMN x_powered_by VARCHAR(100) COMMENT 'X-Powered-By头',
ADD COLUMN status_code INT COMMENT 'HTTP状态码';

-- 添加位置扩展信息字段
ALTER TABLE search_results 
ADD COLUMN location_country VARCHAR(100) COMMENT '国家',
ADD COLUMN location_province VARCHAR(100) COMMENT '省份',
ADD COLUMN location_city VARCHAR(100) COMMENT '城市';

-- 添加技术栈信息字段
ALTER TABLE search_results 
ADD COLUMN tech_stack TEXT COMMENT '技术栈信息(JSON格式)';

-- 添加ICP备案信息字段
ALTER TABLE search_results 
ADD COLUMN icp_licence VARCHAR(100) COMMENT 'ICP备案号',
ADD COLUMN icp_unit VARCHAR(200) COMMENT 'ICP备案单位',
ADD COLUMN icp_nature VARCHAR(50) COMMENT 'ICP备案性质';

-- 添加网站元信息字段
ALTER TABLE search_results 
ADD COLUMN meta_keywords TEXT COMMENT '网站关键词',
ADD COLUMN favicon_hash VARCHAR(100) COMMENT '网站图标哈希',
ADD COLUMN favicon_url VARCHAR(500) COMMENT '网站图标URL';

-- 添加索引以提高查询性能
CREATE INDEX idx_search_results_asn ON search_results(asn);
CREATE INDEX idx_search_results_status_code ON search_results(status_code);
CREATE INDEX idx_search_results_server ON search_results(server);
CREATE INDEX idx_search_results_icp_licence ON search_results(icp_licence);
CREATE INDEX idx_search_results_location_country ON search_results(location_country);
CREATE INDEX idx_search_results_location_province ON search_results(location_province);
CREATE INDEX idx_search_results_location_city ON search_results(location_city);
