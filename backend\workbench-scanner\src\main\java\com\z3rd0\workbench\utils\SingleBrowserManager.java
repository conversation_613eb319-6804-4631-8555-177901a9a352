package com.z3rd0.workbench.utils;

import com.z3rd0.workbench.config.CrawlerConfig;
import com.z3rd0.workbench.config.CaptchaConfig;
import com.z3rd0.workbench.service.CookieService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;

/**
 * 单例浏览器管理类
 * 确保系统中只有一个浏览器实例
 */
@Component
public class SingleBrowserManager {
    private static final Logger logger = LoggerFactory.getLogger(SingleBrowserManager.class);
    
    @Autowired
    private CookieService cookieService;

    @Autowired
    private CrawlerConfig crawlerConfig;

    @Autowired
    private CaptchaConfig captchaConfig;
    
    // 单个浏览器实例
    private PlaywrightUtils browserInstance;
    
    // 全局锁，用于初始化和关闭
    private final ReentrantLock browserLock = new ReentrantLock();
    
    // 浏览器是否正在使用中
    private final AtomicBoolean inUse = new AtomicBoolean(false);
    
    // 浏览器是否已初始化
    private final AtomicBoolean initialized = new AtomicBoolean(false);

    // 异步初始化执行器
    private final ExecutorService initExecutor = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "browser-init-thread");
        t.setDaemon(true);
        return t;
    });

    // 异步初始化状态
    private volatile CompletableFuture<Void> initFuture;
    
    @PostConstruct
    public void init() {
        logger.info("初始化单例浏览器管理器...");

        // 设置静态账号凭据并验证配置完整性
        if (crawlerConfig != null && crawlerConfig.getAccount() != null) {
            String username = crawlerConfig.getUsername();
            String password = crawlerConfig.getPassword();

            // 验证配置完整性
            if (username == null || username.trim().isEmpty()) {
                String errorMsg = "配置错误：crawler.account.username 未配置或为空，请检查application.yml配置文件";
                logger.error(errorMsg);
                throw new IllegalStateException(errorMsg);
            }

            if (password == null || password.trim().isEmpty()) {
                String errorMsg = "配置错误：crawler.account.password 未配置或为空，请检查application.yml配置文件";
                logger.error(errorMsg);
                throw new IllegalStateException(errorMsg);
            }

            // 同时设置两个类的静态凭据，确保配置同步
            BrowserConfigManager.setStaticCredentials(username.trim(), password.trim());
            PlaywrightUtils.setStaticCredentials(username.trim(), password.trim());
            logger.info("已从配置文件设置静态账号凭据到BrowserConfigManager和PlaywrightUtils，用户名: {}", username.trim());
        } else {
            String errorMsg = "配置错误：crawler.account 配置节点不存在，请检查application.yml配置文件";
            logger.error(errorMsg);
            throw new IllegalStateException(errorMsg);
        }

        // 设置CookieService到PlaywrightUtils和CookieManager
        CookieManager.setCookieService(cookieService);
        PlaywrightUtils.setCookieService(cookieService);

        // 设置CaptchaConfig到PlaywrightUtils
        if (captchaConfig != null) {
            PlaywrightUtils.setCaptchaConfig(captchaConfig);
            logger.info("已设置验证码配置");
        }

        // 异步预创建浏览器实例，不阻塞应用启动
        logger.info("开始异步预创建浏览器实例...");
        asyncInitializeBrowser();
    }

    /**
     * 异步初始化浏览器实例
     */
    private void asyncInitializeBrowser() {
        initFuture = CompletableFuture.runAsync(() -> {
            try {
                logger.info("异步浏览器初始化开始...");
                createBrowserInstance();
                logger.info("异步浏览器初始化成功完成");
            } catch (Exception e) {
                logger.error("异步浏览器初始化失败，但不影响应用启动: {}", e.getMessage());
                // 不重新抛出异常，避免影响应用启动
            }
        }, initExecutor);
    }
    
    @PreDestroy
    public void cleanup() {
        logger.info("关闭单例浏览器实例");

        // 取消异步初始化任务
        if (initFuture != null && !initFuture.isDone()) {
            logger.info("取消正在进行的异步浏览器初始化");
            initFuture.cancel(true);
        }

        // 关闭执行器
        if (!initExecutor.isShutdown()) {
            logger.info("关闭浏览器初始化执行器");
            initExecutor.shutdown();
        }

        browserLock.lock();
        try {
            if (browserInstance != null) {
                try {
                    logger.info("关闭浏览器实例");
                    browserInstance.close();
                } catch (Exception e) {
                    logger.error("关闭浏览器实例出错: {}", e.getMessage());
                }
                browserInstance = null;
            }
            initialized.set(false);
            inUse.set(false);
        } finally {
            browserLock.unlock();
        }
    }
    
    /**
     * 创建新的浏览器实例
     */
    private void createBrowserInstance() {
        browserLock.lock();
        try {
            if (browserInstance != null) {
                logger.warn("浏览器实例已存在，先关闭现有实例");
                try {
                    browserInstance.close();
                } catch (Exception e) {
                    logger.error("关闭现有浏览器实例出错: {}", e.getMessage());
                }
            }

            logger.info("创建新的浏览器实例");

            // 确保CookieService已设置
            if (cookieService != null) {
                PlaywrightUtils.setCookieService(cookieService);
                logger.info("已为新浏览器实例设置CookieService");
            }

            // 创建浏览器实例，增加重试机制
            int maxRetries = 3;
            int retryDelay = 10000; // 10秒
            Exception lastException = null;

            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    logger.info("浏览器创建尝试 {}/{}", attempt, maxRetries);

                    browserInstance = new PlaywrightUtils();
                    browserInstance.initialize();
                    boolean loginSuccess = browserInstance.login();

                    if (loginSuccess) {
                        initialized.set(true);
                        inUse.set(false);
                        logger.info("成功创建浏览器实例 (尝试 {}/{})", attempt, maxRetries);
                        return;
                    } else {
                        logger.warn("浏览器登录失败 (尝试 {}/{})", attempt, maxRetries);
                        if (browserInstance != null) {
                            try {
                                browserInstance.close();
                            } catch (Exception e) {
                                logger.error("关闭失败的浏览器实例出错: {}", e.getMessage());
                            }
                            browserInstance = null;
                        }

                        if (attempt < maxRetries) {
                            logger.info("等待 {}ms 后重试...", retryDelay);
                            Thread.sleep(retryDelay);
                        }
                    }
                } catch (Exception e) {
                    lastException = e;
                    logger.error("浏览器创建尝试 {}/{} 失败: {}", attempt, maxRetries, e.getMessage());

                    if (browserInstance != null) {
                        try {
                            browserInstance.close();
                        } catch (Exception ex) {
                            logger.error("关闭失败的浏览器实例出错: {}", ex.getMessage());
                        }
                        browserInstance = null;
                    }

                    if (attempt < maxRetries) {
                        logger.info("等待 {}ms 后重试...", retryDelay);
                        try {
                            Thread.sleep(retryDelay);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("浏览器创建被中断", ie);
                        }
                    }
                }
            }

            // 所有重试都失败了
            initialized.set(false);
            String errorMsg = "浏览器创建失败，已重试 " + maxRetries + " 次";
            if (lastException != null) {
                errorMsg += ": " + lastException.getMessage();
                throw new RuntimeException(errorMsg, lastException);
            } else {
                throw new RuntimeException(errorMsg);
            }

        } finally {
            browserLock.unlock();
        }
    }
    
    /**
     * 获取浏览器资源 - 增强版本，支持页面对象保护
     */
    public BrowserResource acquireBrowser() {
        // 如果异步初始化正在进行，等待其完成
        if (initFuture != null && !initFuture.isDone()) {
            logger.info("等待异步浏览器初始化完成...");
            try {
                initFuture.get(); // 等待异步初始化完成
                logger.info("异步浏览器初始化已完成");
            } catch (Exception e) {
                logger.warn("异步浏览器初始化失败，将尝试重新创建: {}", e.getMessage());
            }
        }

        browserLock.lock();
        try {
            // 检查是否已有实例在使用中
            if (inUse.get()) {
                throw new RuntimeException("浏览器实例正在使用中，请稍后重试");
            }

            // 检查浏览器实例是否存在且可用
            if (browserInstance == null || !initialized.get()) {
                logger.info("浏览器实例不存在，创建新实例");
                createBrowserInstance();
            } else {
                // 深度检查浏览器状态，包括页面对象
                if (!validateBrowserState()) {
                    logger.warn("浏览器实例状态异常，重新创建");
                    createBrowserInstance();
                }
            }

            // 标记为使用中
            inUse.set(true);
            logger.info("获取浏览器实例成功");

            // 返回浏览器资源包装类
            return new BrowserResource(browserInstance, this);
        } catch (Exception e) {
            logger.error("获取浏览器实例失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取浏览器失败: " + e.getMessage(), e);
        } finally {
            browserLock.unlock();
        }
    }

    /**
     * 深度验证浏览器状态，包括页面对象
     */
    private boolean validateBrowserState() {
        try {
            if (browserInstance == null) {
                logger.debug("浏览器实例为null");
                return false;
            }

            if (!browserInstance.isInitialized()) {
                logger.debug("浏览器未初始化");
                return false;
            }

            if (!browserInstance.isConnected()) {
                logger.debug("浏览器连接已断开");
                return false;
            }

            // 检查页面对象是否有效
            if (!browserInstance.isPageValid()) {
                logger.warn("浏览器页面对象无效");
                return false;
            }

            logger.debug("浏览器状态验证通过");
            return true;

        } catch (Exception e) {
            logger.warn("浏览器状态验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 释放浏览器资源
     */
    public void releaseBrowser() {
        logger.info("释放浏览器实例");
        inUse.set(false);
    }
    
    /**
     * 检查浏览器是否已初始化
     */
    public boolean isBrowserInitialized() {
        return initialized.get() && browserInstance != null;
    }
    
    /**
     * 检查是否有任务正在处理中
     */
    public boolean isProcessingTask() {
        return inUse.get();
    }
    
    /**
     * 关闭浏览器
     */
    public void closeBrowser() {
        logger.info("执行关闭浏览器操作");
        browserLock.lock();
        try {
            if (browserInstance != null) {
                try {
                    browserInstance.close();
                } catch (Exception e) {
                    logger.error("关闭浏览器实例出错: {}", e.getMessage());
                }
                browserInstance = null;
            }
            initialized.set(false);
            inUse.set(false);
            logger.info("浏览器实例已关闭");
        } finally {
            browserLock.unlock();
        }
    }
    
    /**
     * 获取浏览器状态
     */
    public String getBrowserStatus() {
        if (!initialized.get()) {
            return "未初始化";
        } else if (inUse.get()) {
            return "使用中";
        } else {
            return "空闲";
        }
    }
    
    /**
     * 浏览器资源包装类
     * 实现AutoCloseable接口，支持try-with-resources语法
     */
    public static class BrowserResource implements AutoCloseable {
        private final PlaywrightUtils browser;
        private final SingleBrowserManager manager;
        private boolean closed = false;
        
        public BrowserResource(PlaywrightUtils browser, SingleBrowserManager manager) {
            this.browser = browser;
            this.manager = manager;
        }
        
        public PlaywrightUtils getBrowser() {
            return browser;
        }
        
        /**
         * 关闭资源，释放浏览器
         */
        @Override
        public void close() {
            if (!closed) {
                manager.releaseBrowser();
                closed = true;
            }
        }
    }
}
