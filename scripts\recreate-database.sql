-- 研发阶段数据库重建脚本
-- 警告：此脚本会删除所有数据，仅用于开发环境

-- 删除原表
DROP TABLE IF EXISTS search_results;
DROP TABLE IF EXISTS tasks;

-- 重新创建tasks表
CREATE TABLE tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    rule TEXT NOT NULL COMMENT '搜索规则',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '任务状态',
    filter_range VARCHAR(200) COMMENT '时间过滤范围',
    executed_at TIMESTAMP NULL COMMENT '执行时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 重新创建search_results表
CREATE TABLE search_results (
    -- 主键和基础标识
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    original_id VARCHAR(100) NOT NULL COMMENT '原始数据ID',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    rule TEXT COMMENT '搜索规则',
    
    -- 网络基础信息
    ip VARCHAR(45) NOT NULL COMMENT 'IP地址(支持IPv6)',
    port VARCHAR(10) COMMENT '端口号',
    domain VARCHAR(500) COMMENT '域名',
    hostname VARCHAR(200) COMMENT '主机名',
    transport VARCHAR(20) COMMENT '传输协议',
    org VARCHAR(200) COMMENT '组织信息',
    
    -- 网络扩展信息
    asn INT COMMENT '自治系统号',
    is_ipv6 BOOLEAN DEFAULT FALSE COMMENT '是否IPv6',
    sys_tag VARCHAR(500) COMMENT '系统标签',
    
    -- 网站基础信息
    title VARCHAR(1000) COMMENT '网站标题',
    url VARCHAR(2000) COMMENT '完整URL',
    path VARCHAR(1000) COMMENT 'URL路径',
    host VARCHAR(200) COMMENT 'HTTP主机头',
    server VARCHAR(100) COMMENT '服务器软件',
    x_powered_by VARCHAR(100) COMMENT 'X-Powered-By头',
    status_code INT COMMENT 'HTTP状态码',
    
    -- 地理位置信息
    location_isp VARCHAR(100) COMMENT '网络运营商',
    location_scene VARCHAR(100) COMMENT '网络场景',
    location_country VARCHAR(100) COMMENT '国家',
    location_province VARCHAR(100) COMMENT '省份',
    location_city VARCHAR(100) COMMENT '城市',
    
    -- 保留的原有字段
    http_load_url VARCHAR(1000) COMMENT 'HTTP加载URL',
    page_type VARCHAR(100) COMMENT '页面类型',
    
    -- 技术栈和备案信息
    tech_stack JSON COMMENT '技术栈信息(JSON格式)',
    icp_licence VARCHAR(100) COMMENT 'ICP备案号',
    icp_unit VARCHAR(200) COMMENT 'ICP备案单位',
    icp_nature VARCHAR(50) COMMENT 'ICP备案性质',
    
    -- 网站元信息
    meta_keywords TEXT COMMENT '网站关键词',
    favicon_hash VARCHAR(64) COMMENT '网站图标MD5哈希',
    favicon_url VARCHAR(500) COMMENT '网站图标URL',
    
    -- 时间信息
    data_time VARCHAR(30) COMMENT '数据采集时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    in_time_range BOOLEAN DEFAULT FALSE COMMENT '是否在时间范围内',
    
    -- 内容信息
    content LONGTEXT COMMENT '网页完整内容',
    summary TEXT COMMENT '内容摘要',
    
    -- 管理字段
    is_read TINYINT DEFAULT 0 COMMENT '是否已读(0:未读,1:已读)',
    note TEXT COMMENT '备注信息',
    is_excluded TINYINT DEFAULT 0 COMMENT '是否排除(0:未排除,1:已排除)',
    
    -- 创建唯一约束
    UNIQUE KEY uk_original_title_path (original_id, title(100), path(100)),
    
    -- 创建索引
    INDEX idx_task_name (task_name),
    INDEX idx_ip (ip),
    INDEX idx_domain (domain(100)),
    INDEX idx_title (title(100)),
    INDEX idx_asn (asn),
    INDEX idx_status_code (status_code),
    INDEX idx_server (server),
    INDEX idx_icp_licence (icp_licence),
    INDEX idx_location_country (location_country),
    INDEX idx_location_province (location_province),
    INDEX idx_location_city (location_city),
    INDEX idx_created_at (created_at),
    INDEX idx_data_time (data_time),
    INDEX idx_is_read (is_read),
    INDEX idx_is_excluded (is_excluded),
    INDEX idx_in_time_range (in_time_range)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索结果表';

-- 插入测试数据（可选）
INSERT INTO tasks (name, rule, status) VALUES 
('测试任务1', 'app:"Apache"', 'PENDING'),
('测试任务2', 'title:"管理系统"', 'PENDING');

SELECT 'Database recreated successfully!' as message;
