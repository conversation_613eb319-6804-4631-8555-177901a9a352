import os
import subprocess
import threading
import ttkbootstrap as tb
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import ScrolledText
from tkinter import messagebox
import signal
import psutil
import time
import platform
import sys
import shutil

class BuildRunApp(tb.Window):
    def __init__(self):
        super().__init__(themename="litera")
        self.title("模块构建与启动管理工具")
        self.minsize(900, 700)
        self.rowconfigure(1, weight=1)
        self.columnconfigure(0, weight=1)
        
        # 存储进程对象
        self.running_processes = {
            "scanner": None,
            "system": None,
            "frontend": None
        }
        
        # 存储按钮状态
        self.button_states = {
            "scanner": "start",
            "system": "start",
            "frontend": "start"
        }
        
        # 日志缓冲区
        self.log_buffers = {
            "scanner": [],
            "system": [],
            "frontend": [],
            "build": [],
            "clean": []
        }

        # --- 按钮区 ---
        btn_frame = tb.Frame(self)
        btn_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        for i in range(3):
            btn_frame.rowconfigure(i, weight=0)
        for i in range(3):
            btn_frame.columnconfigure(i, weight=1)

        # 第一行：构建
        tb.Button(btn_frame, text="构建 Scanner 模块", bootstyle="info-outline", width=20,
                  command=lambda: self.run_task("build_scanner")).grid(row=0, column=0, padx=6, pady=4, sticky="ew")
        tb.Button(btn_frame, text="构建 System 模块", bootstyle="info-outline", width=20,
                  command=lambda: self.run_task("build_system")).grid(row=0, column=1, padx=6, pady=4, sticky="ew")
        tb.Button(btn_frame, text="一键构建所有模块", bootstyle="info", width=20,
                  command=lambda: self.run_task("build_all")).grid(row=0, column=2, padx=6, pady=4, sticky="ew")

        # 第二行：启动/停止
        self.btn_scanner = tb.Button(btn_frame, text="启动 Scanner 模块", bootstyle="success-outline", width=20,
                  command=lambda: self.toggle_module("scanner"))
        self.btn_scanner.grid(row=1, column=0, padx=6, pady=4, sticky="ew")
        
        self.btn_system = tb.Button(btn_frame, text="启动 System 模块", bootstyle="success-outline", width=20,
                  command=lambda: self.toggle_module("system"))
        self.btn_system.grid(row=1, column=1, padx=6, pady=4, sticky="ew")
        
        self.btn_frontend = tb.Button(btn_frame, text="启动前端模块", bootstyle="success-outline", width=20,
                  command=lambda: self.toggle_module("frontend"))
        self.btn_frontend.grid(row=1, column=2, padx=6, pady=4, sticky="ew")

        # 第三行：清理
        tb.Button(btn_frame, text="清理所有构建文件", bootstyle="danger", width=62,
                  command=lambda: self.run_task("clean_all")).grid(row=2, column=0, columnspan=3, padx=6, pady=8, sticky="ew")

        # --- 日志标签页 ---
        self.tab_control = tb.Notebook(self)
        self.tab_control.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        
        # 创建各个日志标签页(使用中文标签)
        self.log_tabs = {}
        self.tab_config = {
            "scanner": "扫描器",
            "system": "系统",
            "frontend": "前端",
            "build": "构建",
            "clean": "清理"
        }
        
        for module, label in self.tab_config.items():
            tab_frame = tb.Frame(self.tab_control)
            self.tab_control.add(tab_frame, text=label)
            
            # 创建带滚动条的文本框
            txt_log = ScrolledText(tab_frame, height=20, font=("微软雅黑", 10))
            txt_log.pack(fill="both", expand=True, padx=5, pady=5)
            txt_log.text.configure(state='disabled')
            
            # 存储文本框引用
            self.log_tabs[module] = txt_log

        # --- 项目根目录校验 ---
        if not os.path.exists("backend/pom.xml"):
            self.after(0, lambda: messagebox.showerror("错误", "请在项目根目录运行本程序（需包含 backend/pom.xml）"))
            self.destroy()
            
        # 绑定窗口关闭事件
        self.protocol("WM_DELETE_WINDOW", self.on_close)

    def on_close(self):
        """窗口关闭事件处理"""
        # 检查是否有正在运行的进程
        running_processes = [k for k, v in self.running_processes.items() if v is not None]
        
        if running_processes:
            # 使用实例变量 self.tab_config
            process_names = ", ".join([self.tab_config.get(p, p) for p in running_processes])
            if messagebox.askyesno("确认关闭", 
                                  f"以下模块仍在运行：{process_names}\n关闭管理程序将终止这些进程。\n是否继续？"):
                # 终止所有进程
                for module in running_processes:
                    self.terminate_process(module)
                self.destroy()
            else:
                return  # 取消关闭
        else:
            self.destroy()

    def terminate_process(self, module_type):
        """终止指定模块的进程"""
        process = self.running_processes[module_type]
        if process and process.poll() is None:
            try:
                if os.name == 'nt':  # Windows
                    subprocess.call(['taskkill', '/F', '/T', '/PID', str(process.pid)], 
                                   creationflags=subprocess.CREATE_NO_WINDOW)
                else:  # Linux/MacOS
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                process.wait(timeout=2)
            except Exception as e:
                print(f"终止进程失败: {e}")
            finally:
                self.running_processes[module_type] = None
                self.update_button_state(module_type, "start")

    def clear_log(self, module):
        """清空指定模块的日志"""
        def do_clear():
            txt_widget = self.log_tabs[module].text
            txt_widget.configure(state='normal')
            txt_widget.delete(1.0, tb.END)
            txt_widget.configure(state='disabled')
            self.log_buffers[module] = []
        self.after(0, do_clear)

    def log(self, message, module="build"):
        """记录日志到指定模块的缓冲区"""
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        formatted_msg = f"[{timestamp}] {message}"
        self.log_buffers[module].append(formatted_msg)
        
        # 更新UI
        def do_log():
            txt_widget = self.log_tabs[module].text
            txt_widget.configure(state='normal')
            txt_widget.insert(tb.END, formatted_msg + "\n")
            txt_widget.see(tb.END)
            txt_widget.configure(state='disabled')
        self.after(0, do_log)

    def run_task(self, task_name):
        threading.Thread(target=self._run_task_thread, args=(task_name,), daemon=True).start()

    def toggle_module(self, module_type):
        """切换模块状态（启动/停止）"""
        if self.button_states[module_type] == "start":
            # 启动前清空日志
            self.clear_log(module_type)
            # 启动模块
            self.run_task(f"run_{module_type}")
        else:
            # 停止模块
            self.run_task(f"stop_{module_type}")

    def update_button_state(self, module_type, state):
        """更新按钮状态和文本"""
        self.button_states[module_type] = state
        
        button_map = {
            "scanner": self.btn_scanner,
            "system": self.btn_system,
            "frontend": self.btn_frontend
        }
        
        button = button_map[module_type]
        if state == "start":
            button.configure(text=f"启动 {module_type.capitalize()} 模块", bootstyle="success-outline")
        else:
            button.configure(text=f"停止 {module_type.capitalize()} 模块", bootstyle="danger-outline")

    def _run_task_thread(self, task_name):
        try:
            if task_name == "build_scanner":
                self.build_module("workbench-scanner")
            elif task_name == "build_system":
                self.build_module("workbench-system")
            elif task_name == "build_all":
                self.build_all()
            elif task_name == "run_scanner":
                self.run_jar("scanner", "workbench-scanner")
            elif task_name == "run_system":
                self.run_jar("system", "workbench-system")
            elif task_name == "run_frontend":
                self.run_frontend()
            elif task_name == "stop_scanner":
                self.stop_module("scanner")
            elif task_name == "stop_system":
                self.stop_module("system")
            elif task_name == "stop_frontend":
                self.stop_module("frontend")
            elif task_name == "clean_all":
                self.clean_all()
        except Exception as e:
            self.log(f"执行异常: {e}")

    def build_module(self, module_name):
        module_type = "scanner" if "scanner" in module_name else "system"
        self.log(f"开始构建模块：{module_name}", module="build")
        backend_dir = os.path.join(os.getcwd(), "backend")
        if not os.path.exists(os.path.join(backend_dir, "pom.xml")):
            self.log("错误：未检测到 backend/pom.xml，请确认当前目录为项目根目录", module="build")
            return
        cmd = f'mvn clean package -DskipTests -pl {module_name} -am'
        self.log(f"执行命令：{cmd}", module="build")
        process = subprocess.Popen(cmd, shell=True, cwd=backend_dir,
                                   stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
        for line in iter(process.stdout.readline, ''):
            if line:
                self.log(line.rstrip(), module=module_type)
        process.stdout.close()
        retcode = process.wait()
        if retcode == 0:
            self.log(f"{module_name} 模块构建成功！", module="build")
        else:
            self.log(f"{module_name} 模块构建失败，错误码：{retcode}", module="build")

    def build_all(self):
        self.log("开始一键构建所有模块", module="build")
        backend_dir = os.path.join(os.getcwd(), "backend")
        if not os.path.exists(os.path.join(backend_dir, "pom.xml")):
            self.log("错误：未检测到 backend/pom.xml，请确认当前目录为项目根目录", module="build")
            return
        cmd = 'mvn clean package -DskipTests'
        self.log(f"执行命令：{cmd}", module="build")
        process = subprocess.Popen(cmd, shell=True, cwd=backend_dir,
                                   stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
        for line in iter(process.stdout.readline, ''):
            if line:
                self.log(line.rstrip(), module="build")
        process.stdout.close()
        retcode = process.wait()
        if retcode == 0:
            self.log("所有模块构建成功！", module="build")
        else:
            self.log(f"构建失败，错误码：{retcode}", module="build")

    def run_jar(self, module_type, module_name):
        self.update_button_state(module_type, "stop")
        
        jar_path = os.path.join(os.getcwd(), "backend", module_name, "target", f"{module_name}-0.0.1-SNAPSHOT.jar")
        if not os.path.exists(jar_path):
            self.log(f"{module_name} 模块的 jar 包未找到，请先构建模块。路径：{jar_path}", module=module_type)
            self.update_button_state(module_type, "start")
            return
            
        self.log(f"启动模块：{module_name}", module=module_type)
        try:
            # 启动Java进程(不显示黑窗口)
            process = subprocess.Popen(
                ['java', '-jar', jar_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                bufsize=1,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            self.running_processes[module_type] = process
            self.log(f"{module_name} 模块已启动 (PID: {process.pid})", module=module_type)
            
            # 启动线程读取输出
            threading.Thread(target=self.read_process_output, 
                            args=(process, module_type), 
                            daemon=True).start()
        except Exception as e:
            self.log(f"启动 {module_name} 模块失败: {e}", module=module_type)
            self.update_button_state(module_type, "start")

    def run_frontend(self):
        self.update_button_state("frontend", "stop")
        self.log("启动前端模块...", module="frontend")
        frontend_dir = os.path.join(os.getcwd(), "workbench-web")
        if not os.path.exists(frontend_dir):
            self.log(f"前端目录不存在: {frontend_dir}", module="frontend")
            self.update_button_state("frontend", "start")
            return

        # 增强环境检查
        pnpm_path = shutil.which('pnpm')
        if not pnpm_path:
            self.log("未检测到pnpm命令，请先全局安装pnpm并配置到系统PATH", module="frontend")
            self.log("安装指南: https://pnpm.io/installation", module="frontend")
            self.log(f"当前系统PATH: {os.getenv('PATH')}", module="frontend")
            self.update_button_state("frontend", "start")
            return

        try:
            # 使用绝对路径执行pnpm
            if os.name == 'nt':  # Windows
                process = subprocess.Popen(
                    [pnpm_path, 'dev'],
                    cwd=frontend_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    bufsize=1,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:  # Linux/MacOS
                process = subprocess.Popen(
                    [pnpm_path, 'dev'],
                    cwd=frontend_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    bufsize=1,
                    start_new_session=True
                )
            
            self.running_processes["frontend"] = process
            self.log(f"前端模块已启动 (PID: {process.pid})", module="frontend")
            self.log(f"使用pnpm路径: {pnpm_path}", module="frontend")
            
            # 启动线程读取输出
            threading.Thread(target=self.read_process_output, 
                            args=(process, "frontend"), 
                            daemon=True).start()
        except Exception as e:
            self.log(f"启动前端模块失败: {e}", module="frontend")
            self.log("提示：请确保已安装Node.js和pnpm，并配置到系统PATH", module="frontend")
            self.log(f"系统PATH: {os.getenv('PATH')}", module="frontend")
            self.update_button_state("frontend", "start")

    def read_process_output(self, process, module_type):
        """读取进程输出并记录到日志"""
        while True:
            # 读取字节数据
            byte_line = process.stdout.readline()
            if not byte_line:
                break
                
            try:
                # 尝试UTF-8解码
                line = byte_line.decode('utf-8').rstrip()
            except UnicodeDecodeError:
                try:
                    # 尝试GBK解码
                    line = byte_line.decode('gbk').rstrip()
                except:
                    # 双重失败则使用错误处理
                    line = byte_line.decode('utf-8', errors='replace').rstrip()
            self.log(line, module=module_type)

        # 进程结束时更新按钮状态
        if process.poll() is not None:
            self.log(f"进程已结束 (退出码: {process.returncode})", module=module_type)
            self.update_button_state(module_type, "start")

    def stop_module(self, module_type):
        """停止指定模块的进程"""
        process = self.running_processes[module_type]
        if not process:
            self.log(f"{module_type} 模块未运行", module=module_type)
            return
            
        self.log(f"正在停止 {module_type} 模块...", module=module_type)
        
        try:
            if process.poll() is None:  # 进程仍在运行
                # 跨平台终止进程
                if os.name == 'nt':  # Windows
                    subprocess.call(['taskkill', '/F', '/T', '/PID', str(process.pid)], 
                                   creationflags=subprocess.CREATE_NO_WINDOW)
                else:  # Linux/MacOS
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                
                # 等待进程结束
                process.wait(timeout=5)
                self.log(f"{module_type} 模块已停止", module=module_type)
            else:
                self.log(f"{module_type} 模块已停止", module=module_type)
        except Exception as e:
            self.log(f"停止 {module_type} 模块失败: {e}", module=module_type)
        finally:
            self.running_processes[module_type] = None
            self.update_button_state(module_type, "start")

    def clean_all(self):
        def ask_clean():
            if not messagebox.askyesno("确认", "确定要清理所有构建文件吗？此操作不可恢复。"):
                self.log("清理操作已取消。", module="clean")
                return
            self.log("开始清理所有构建文件", module="clean")
            backend_dir = os.path.join(os.getcwd(), "backend")
            if not os.path.exists(os.path.join(backend_dir, "pom.xml")):
                self.log("错误：未检测到 backend/pom.xml，请确认当前目录为项目根目录", module="clean")
                return
            cmd = "mvn clean"
            self.log(f"执行命令：{cmd}", module="clean")
            process = subprocess.Popen(cmd, shell=True, cwd=backend_dir,
                                       stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
            for line in iter(process.stdout.readline, ''):
                if line:
                    self.log(line.rstrip(), module="clean")
            process.stdout.close()
            retcode = process.wait()
            if retcode == 0:
                self.log("清理成功！", module="clean")
            else:
                self.log(f"清理失败，错误码：{retcode}", module="clean")
        self.after(0, ask_clean)

if __name__ == "__main__":
    app = BuildRunApp()
    app.mainloop()
