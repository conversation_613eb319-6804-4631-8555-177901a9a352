-- 创建IP资产表
CREATE TABLE IF NOT EXISTS ip_assets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'IP资产ID',
    ip_address VARCHAR(45) NOT NULL UNIQUE COMMENT 'IP地址（支持IPv6）',
    first_discovered TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '首次发现时间',
    last_seen TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后发现时间',
    asset_count INT DEFAULT 0 COMMENT '关联资产数量',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT 'IP状态：ACTIVE(活跃)、INACTIVE(非活跃)',
    location_country VARCHAR(100) COMMENT '国家',
    location_province VARCHAR(100) COMMENT '省份',
    location_city VARCHAR(100) COMMENT '城市',
    isp VARCHAR(200) COMMENT 'ISP信息',
    asn INT COMMENT 'ASN信息',
    organization VARCHAR(200) COMMENT '组织信息',
    is_ipv6 BOOLEAN DEFAULT FALSE COMMENT '是否为IPv6地址',
    common_ports JSON COMMENT '常见端口列表（JSON格式）',
    associated_domains JSON COMMENT '关联域名列表（JSON格式）',
    notes TEXT COMMENT '备注信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP资产表';

-- 创建索引
CREATE UNIQUE INDEX uk_ip_address ON ip_assets(ip_address);
CREATE INDEX idx_status ON ip_assets(status);
CREATE INDEX idx_first_discovered ON ip_assets(first_discovered);
CREATE INDEX idx_last_seen ON ip_assets(last_seen);
CREATE INDEX idx_asset_count ON ip_assets(asset_count);
CREATE INDEX idx_location_country ON ip_assets(location_country);
CREATE INDEX idx_isp ON ip_assets(isp);
CREATE INDEX idx_asn ON ip_assets(asn);

-- 为search_results表添加ip_asset_id字段
ALTER TABLE search_results 
ADD COLUMN ip_asset_id BIGINT COMMENT '关联的IP资产ID',
ADD INDEX idx_ip_asset_id (ip_asset_id);

-- 添加外键约束（可选，根据需要决定是否启用）
-- ALTER TABLE search_results
-- ADD CONSTRAINT fk_search_results_ip_asset
-- FOREIGN KEY (ip_asset_id) REFERENCES ip_assets(id) ON DELETE SET NULL;

-- 注意：存储过程和触发器已移除，IP资产同步将通过应用层服务处理
