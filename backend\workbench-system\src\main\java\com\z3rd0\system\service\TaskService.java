package com.z3rd0.system.service;

import com.z3rd0.common.model.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 任务服务接口
 */
public interface TaskService {
    
    /**
     * 根据条件分页查询任务
     * @param name 任务名称（模糊查询）
     * @param status 任务状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 任务分页结果
     */
    Page<Task> findByConditions(String name, String status, String startTime, String endTime, Pageable pageable);
    
    /**
     * 根据ID查找任务
     * @param id 任务ID
     * @return 任务（可能不存在）
     */
    Optional<Task> findById(Long id);
    
    /**
     * 保存任务
     * @param task 任务对象
     * @return 保存后的任务
     */
    Task save(Task task);
    
    /**
     * 检查任务是否存在
     * @param id 任务ID
     * @return 是否存在
     */
    boolean existsById(Long id);
    
    /**
     * 根据ID删除任务
     * @param id 任务ID
     */
    void deleteById(Long id);
    
    /**
     * 查找所有任务
     * @return 所有任务列表
     */
    List<Task> findAll();

    /**
     * 批量查找任务
     * @param ids 任务ID列表
     * @return 任务列表
     */
    List<Task> findByIds(List<Long> ids);

    /**
     * 批量删除任务
     * @param ids 任务ID列表
     */
    void deleteByIds(List<Long> ids);

    /**
     * 批量保存任务
     * @param tasks 任务列表
     * @return 保存后的任务列表
     */
    List<Task> saveAll(List<Task> tasks);
}
