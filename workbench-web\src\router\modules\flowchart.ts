import { $t } from "@/plugins/i18n";
import { flowchart } from "@/router/enums";

export default {
  path: "/flow-chart",
  redirect: "/flow-chart/index",
  meta: {
    icon: "ep/set-up",
    title: $t("menus.pureFlowChart"),
    rank: flowchart,
    showLink: false // 隐藏原有菜单，已归集到示例展示分组
  },
  children: [
    {
      path: "/flow-chart/index",
      name: "Flow<PERSON>hart",
      component: () => import("@/views/flow-chart/index.vue"),
      meta: {
        title: $t("menus.pureFlowChart")
      }
    }
  ]
} satisfies RouteConfigsTable;
