package com.z3rd0.system.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应数据包装类
 * 专门用于分页查询结果的标准化响应格式
 * 
 * @param <T> 分页数据类型
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResponse<T> {
    
    /**
     * 分页数据列表
     */
    private List<T> list;
    
    /**
     * 总记录数
     */
    private long total;
    
    /**
     * 当前页码（从1开始）
     */
    private int currentPage;
    
    /**
     * 每页大小
     */
    private int pageSize;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 是否为第一页
     */
    private boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private boolean isLast;
    
    /**
     * 私有构造函数
     */
    private PageResponse(List<T> list, long total, int currentPage, int pageSize, 
                        int totalPages, boolean hasNext, boolean hasPrevious, 
                        boolean isFirst, boolean isLast) {
        this.list = list;
        this.total = total;
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalPages = totalPages;
        this.hasNext = hasNext;
        this.hasPrevious = hasPrevious;
        this.isFirst = isFirst;
        this.isLast = isLast;
    }
    
    /**
     * 从Spring Data的Page对象创建PageResponse
     * 
     * @param page Spring Data Page对象
     * @param <T> 数据类型
     * @return PageResponse对象
     */
    public static <T> PageResponse<T> of(Page<T> page) {
        return new PageResponse<>(
            page.getContent(),
            page.getTotalElements(),
            page.getNumber() + 1, // Spring Data页码从0开始，转换为从1开始
            page.getSize(),
            page.getTotalPages(),
            page.hasNext(),
            page.hasPrevious(),
            page.isFirst(),
            page.isLast()
        );
    }
    
    /**
     * 创建空的分页响应
     * 
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 空的PageResponse对象
     */
    public static <T> PageResponse<T> empty(int pageSize) {
        return new PageResponse<>(
            List.of(),
            0L,
            1,
            pageSize,
            0,
            false,
            false,
            true,
            true
        );
    }
    
    /**
     * 手动创建分页响应（用于自定义分页逻辑）
     * 
     * @param list 数据列表
     * @param total 总记录数
     * @param currentPage 当前页码（从1开始）
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return PageResponse对象
     */
    public static <T> PageResponse<T> of(List<T> list, long total, int currentPage, int pageSize) {
        int totalPages = (int) Math.ceil((double) total / pageSize);
        boolean hasNext = currentPage < totalPages;
        boolean hasPrevious = currentPage > 1;
        boolean isFirst = currentPage == 1;
        boolean isLast = currentPage == totalPages || totalPages == 0;
        
        return new PageResponse<>(
            list,
            total,
            currentPage,
            pageSize,
            totalPages,
            hasNext,
            hasPrevious,
            isFirst,
            isLast
        );
    }
}
