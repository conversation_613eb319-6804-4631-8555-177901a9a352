@import "./iconfont/iconfont.css";

.splitter-pane-resizer {
  box-sizing: border-box;
  background: #000;
  position: absolute;
  opacity: 0.2;
  z-index: 1;
  background-clip: padding;
  background-clip: padding-box;
}

.splitter-pane-resizer.horizontal {
  height: 6px;
  width: 100%;
  background: #e5e6eb;
  cursor: row-resize;
}

.splitter-pane-resizer.horizontal:before {
  content: "\eda3";
  font-family: "iconfont";
  font-size: 13px;
  color: #000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.splitter-pane-resizer.vertical {
  width: 6px;
  height: 100%;
  background: #e5e6eb;
  cursor: col-resize;
}

.splitter-pane-resizer.vertical:before {
  content: "\e647";
  font-family: "iconfont";
  font-size: 13px;
  color: #000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
