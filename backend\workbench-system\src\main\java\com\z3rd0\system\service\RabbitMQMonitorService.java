package com.z3rd0.system.service;

import com.z3rd0.system.constants.RabbitMQConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ队列监控服务
 * 提供队列长度查询和队列状态监控功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RabbitMQMonitorService {
    
    private final RabbitTemplate rabbitTemplate;
    
    /**
     * 获取主任务队列的消息数量
     * @return 队列中等待处理的消息数量
     */
    public int getTaskQueueLength() {
        return getQueueMessageCount(RabbitMQConstants.TASK_QUEUE_NAME);
    }
    
    /**
     * 获取任务更新队列的消息数量
     * @return 队列中等待处理的消息数量
     */
    public int getTaskUpdateQueueLength() {
        return getQueueMessageCount(RabbitMQConstants.TASK_UPDATE_QUEUE_NAME);
    }
    
    /**
     * 获取死信队列的消息数量
     * @return 队列中等待处理的消息数量
     */
    public int getDeadLetterQueueLength() {
        return getQueueMessageCount(RabbitMQConstants.DEAD_LETTER_QUEUE_NAME);
    }
    
    /**
     * 获取指定队列的消息数量
     * @param queueName 队列名称
     * @return 队列中等待处理的消息数量
     */
    public int getQueueMessageCount(String queueName) {
        try {
            Integer messageCount = rabbitTemplate.execute(channel -> {
                try {
                    // 使用queueDeclarePassive获取队列信息，不会创建队列
                    com.rabbitmq.client.AMQP.Queue.DeclareOk declareOk = 
                        channel.queueDeclarePassive(queueName);
                    return declareOk.getMessageCount();
                } catch (Exception e) {
                    log.warn("获取队列 {} 信息失败: {}", queueName, e.getMessage());
                    return null;
                }
            });
            
            return messageCount != null ? messageCount : 0;
        } catch (Exception e) {
            log.error("获取队列 {} 消息数量失败: {}", queueName, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取队列的详细信息
     * @param queueName 队列名称
     * @return 队列详细信息
     */
    public Map<String, Object> getQueueInfo(String queueName) {
        Map<String, Object> queueInfo = new HashMap<>();
        
        try {
            Map<String, Object> info = rabbitTemplate.execute(channel -> {
                try {
                    com.rabbitmq.client.AMQP.Queue.DeclareOk declareOk = 
                        channel.queueDeclarePassive(queueName);
                    
                    Map<String, Object> result = new HashMap<>();
                    result.put("messageCount", declareOk.getMessageCount());
                    result.put("consumerCount", declareOk.getConsumerCount());
                    result.put("queueName", declareOk.getQueue());
                    return result;
                } catch (Exception e) {
                    log.warn("获取队列 {} 详细信息失败: {}", queueName, e.getMessage());
                    return null;
                }
            });
            
            if (info != null) {
                queueInfo.putAll(info);
                queueInfo.put("available", true);
            } else {
                queueInfo.put("available", false);
                queueInfo.put("messageCount", 0);
                queueInfo.put("consumerCount", 0);
            }
        } catch (Exception e) {
            log.error("获取队列 {} 详细信息失败: {}", queueName, e.getMessage(), e);
            queueInfo.put("available", false);
            queueInfo.put("messageCount", 0);
            queueInfo.put("consumerCount", 0);
            queueInfo.put("error", e.getMessage());
        }
        
        queueInfo.put("queueName", queueName);
        return queueInfo;
    }
    
    /**
     * 获取所有相关队列的统计信息
     * @return 所有队列的统计信息
     */
    public Map<String, Object> getAllQueueStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 主任务队列
        Map<String, Object> taskQueueInfo = getQueueInfo(RabbitMQConstants.TASK_QUEUE_NAME);
        stats.put("taskQueue", taskQueueInfo);
        
        // 任务更新队列
        Map<String, Object> updateQueueInfo = getQueueInfo(RabbitMQConstants.TASK_UPDATE_QUEUE_NAME);
        stats.put("updateQueue", updateQueueInfo);
        
        // 死信队列
        Map<String, Object> deadLetterQueueInfo = getQueueInfo(RabbitMQConstants.DEAD_LETTER_QUEUE_NAME);
        stats.put("deadLetterQueue", deadLetterQueueInfo);
        
        // 总计等待中的任务数量（主要是主任务队列）
        int totalWaitingTasks = (Integer) taskQueueInfo.get("messageCount");
        stats.put("totalWaitingTasks", totalWaitingTasks);
        
        // 队列健康状态
        boolean allQueuesHealthy = (Boolean) taskQueueInfo.get("available") && 
                                  (Boolean) updateQueueInfo.get("available");
        stats.put("healthy", allQueuesHealthy);
        
        log.debug("队列统计信息: 等待中任务={}, 队列健康={}", totalWaitingTasks, allQueuesHealthy);
        
        return stats;
    }
    
    /**
     * 检查RabbitMQ连接状态
     * @return 连接是否正常
     */
    public boolean isRabbitMQHealthy() {
        try {
            // 尝试获取主任务队列信息来检查连接
            getQueueMessageCount(RabbitMQConstants.TASK_QUEUE_NAME);
            return true;
        } catch (Exception e) {
            log.warn("RabbitMQ连接检查失败: {}", e.getMessage());
            return false;
        }
    }
}
