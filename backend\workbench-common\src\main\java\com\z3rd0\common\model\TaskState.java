package com.z3rd0.common.model;

public enum TaskState {
    PENDING {
        @Override
        public TaskState next() {
            return QUEUED;
        }
        @Override
        public String getDisplayName() {
            return "未扫描";
        }
    },
    QUEUED {
        @Override
        public TaskState next() {
            return PROCESSING;
        }
        @Override
        public String getDisplayName() {
            return "待扫描";
        }
    },
    PROCESSING {
        @Override
        public TaskState next() {
            return COMPLETED;
        }
        @Override
        public String getDisplayName() {
            return "扫描中";
        }
    },
    COMPLETED {
        @Override
        public TaskState next() {
            return this;
        }
        @Override
        public String getDisplayName() {
            return "已扫描";
        }
    },
    FAILED {
        @Override
        public TaskState next() {
            return PENDING;
        }
        @Override
        public String getDisplayName() {
            return "扫描出错";
        }
    },
    SKIPPED {
        @Override
        public TaskState next() {
            return this;
        }
        @Override
        public String getDisplayName() {
            return "已跳过";
        }
    };
    public abstract TaskState next();
    public abstract String getDisplayName();
    public static TaskState fromString(String state) {
        if (state == null) {
            return PENDING;
        }
        try {
            return TaskState.valueOf(state.toUpperCase());
        } catch (IllegalArgumentException e) {
            return PENDING;
        }
    }
} 