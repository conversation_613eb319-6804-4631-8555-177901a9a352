/**
 * 路由清理工具
 * 用于清理本地存储中可能存在的无效路由数据
 */

import { storageLocal } from "@pureadmin/utils";
import { responsiveStorageNameSpace } from "@/config";

/**
 * 验证路由参数是否有效
 */
function isValidRouteParam(value: any): boolean {
  if (value === undefined || value === null) return false;
  if (typeof value === 'string') {
    return value.trim() !== '' && value !== 'undefined' && value !== 'null';
  }
  return true;
}

/**
 * 验证路由对象是否有效
 */
function isValidRoute(route: any): boolean {
  if (!route || typeof route !== 'object') return false;

  // 检查路径
  if (route.path && typeof route.path === 'string') {
    if (route.path.includes('undefined') || route.path.includes('null')) {
      return false;
    }
  }

  // 检查参数
  if (route.params && typeof route.params === 'object') {
    for (const [key, value] of Object.entries(route.params)) {
      if (!isValidRouteParam(value)) {
        return false;
      }
    }
  }

  return true;
}

/**
 * 清理多标签页存储中的无效路由
 */
export function cleanMultiTagsStorage(): void {
  try {
    const tagsKey = `${responsiveStorageNameSpace()}tags`;
    const storedTags = storageLocal().getItem(tagsKey);

    if (Array.isArray(storedTags)) {
      const validTags = storedTags.filter(isValidRoute);

      if (validTags.length !== storedTags.length) {
        console.log(`Cleaned ${storedTags.length - validTags.length} invalid routes from multiTags storage`);
        storageLocal().setItem(tagsKey, validTags);
      }
    }
  } catch (error) {
    console.error('Error cleaning multiTags storage:', error);
  }
}

/**
 * 清理所有可能的路由相关存储
 */
export function cleanAllRouteStorage(): void {
  cleanMultiTagsStorage();

  // 可以在这里添加其他路由相关存储的清理
  console.log('Route storage cleanup completed');
}

/**
 * 在应用启动时调用的初始化清理函数
 */
export function initRouteCleanup(): void {
  // 延迟执行，确保存储系统已经初始化
  setTimeout(() => {
    cleanAllRouteStorage();
  }, 100);
}
