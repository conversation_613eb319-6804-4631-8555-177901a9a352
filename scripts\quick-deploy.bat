@echo off
chcp 65001 >nul
echo ========================================
echo    Workbench 快速部署脚本 (研发版)
echo ========================================
echo.

echo [1/4] 停止现有服务...
taskkill /f /im java.exe 2>nul
timeout /t 2 >nul

echo [2/4] 重建数据库...
echo 请确保MySQL服务正在运行...
echo 执行数据库重建脚本...
mysql -u root -p workbench < scripts/recreate-database.sql
if %errorlevel% neq 0 (
    echo 数据库重建失败！请检查MySQL连接。
    pause
    exit /b 1
)
echo 数据库重建完成！

echo [3/4] 编译后端项目...
cd backend
call mvn clean compile -DskipTests -q
if %errorlevel% neq 0 (
    echo 后端编译失败！
    pause
    exit /b 1
)
echo 后端编译完成！

echo [4/4] 启动服务...
echo 启动 workbench-system 服务...
start "Workbench-System" cmd /k "cd workbench-system && mvn spring-boot:run -Dspring-boot.run.profiles=dev"

timeout /t 5 >nul

echo 启动 workbench-scanner 服务...
start "Workbench-Scanner" cmd /k "cd workbench-scanner && mvn spring-boot:run -Dspring-boot.run.profiles=dev"

cd ..

echo.
echo ========================================
echo           部署完成！
echo ========================================
echo.
echo 服务地址:
echo   - System API: http://localhost:8080
echo   - Web界面:    http://localhost:8848
echo.
echo 等待服务启动完成...
timeout /t 10 >nul

echo 启动前端开发服务器...
cd workbench-web
start "Workbench-Web" cmd /k "npm run dev"

echo.
echo 所有服务已启动！
echo 请等待前端编译完成后访问: http://localhost:8848
echo.
pause
