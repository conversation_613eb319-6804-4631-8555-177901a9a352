package com.z3rd0.workbench.controller;

import com.z3rd0.common.model.TaskExecutionDetail;
import com.z3rd0.workbench.event.TaskEventPublisher;
import com.z3rd0.workbench.repository.TaskExecutionDetailRepository;
import com.z3rd0.workbench.service.TaskConsumer;
import com.z3rd0.workbench.service.TaskPublisher;
import com.z3rd0.workbench.service.TaskService;
import com.z3rd0.workbench.service.TaskStateManager;
import com.z3rd0.workbench.utils.SingleBrowserManager;
import com.z3rd0.workbench.config.CrawlerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.PostConstruct;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 任务测试控制器
 * 用于任务管理和状态查询
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(originPatterns = "*", maxAge = 3600, allowCredentials = "true")
public class TaskTestController {
    
    private static final Logger logger = LoggerFactory.getLogger(TaskTestController.class);
    
    // 静态变量，用于跟踪当前任务信息
    private static String currentTaskName;
    private static LocalDateTime taskStartTime;
    private static TaskEventPublisher staticEventPublisher;
    
    @Autowired
    private TaskPublisher taskPublisher;
    
    @Autowired
    private SingleBrowserManager singleBrowserManager;
    
    @Autowired
    private TaskConsumer taskConsumer;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private TaskStateManager taskStateManager;
    
    @Autowired
    private TaskEventPublisher eventPublisher;
    
    @Autowired
    private CrawlerConfig crawlerConfig;

    @Autowired
    private TaskExecutionDetailRepository taskExecutionDetailRepository;

    /**
     * 初始化静态变量
     */
    @PostConstruct
    public void init() {
        staticEventPublisher = eventPublisher;
        logger.info("初始化TaskTestController静态事件发布器");
    }
    
    /**
     * 设置当前任务名称
     * @param taskName 任务名称
     */
    public static synchronized void setCurrentTaskName(String taskName) {
        currentTaskName = taskName;
        logger.debug("设置当前任务名称: {}", taskName);
    }
    
    /**
     * 获取当前任务名称
     * @return 当前任务名称
     */
    public static synchronized String getCurrentTaskName() {
        return currentTaskName;
    }
    
    /**
     * 设置任务开始时间
     * @param startTime 任务开始时间
     */
    public static synchronized void setTaskStartTime(LocalDateTime startTime) {
        taskStartTime = startTime;
        logger.debug("设置任务开始时间: {}", startTime);
    }
    
    /**
     * 获取任务开始时间
     * @return 任务开始时间
     */
    public static synchronized LocalDateTime getTaskStartTime() {
        return taskStartTime;
    }
    
    /**
     * 添加处理日志
     * @param message 日志消息
     * @param type 日志类型（info, warning, error, success）
     */
    public static void addProcessingLog(String message, String type) {
        if (staticEventPublisher == null) {
            logger.warn("静态事件发布器未初始化，无法添加日志: {}", message);
            return;
        }
        
        if (currentTaskName == null || currentTaskName.isEmpty()) {
            logger.warn("当前任务名称为空，无法添加日志: {}", message);
            return;
        }
        
        switch (type.toLowerCase()) {
            case "info":
                staticEventPublisher.publishInfoLog(currentTaskName, message);
                break;
            case "warning":
                staticEventPublisher.publishWarningLog(currentTaskName, message);
                break;
            case "error":
                staticEventPublisher.publishErrorLog(currentTaskName, message);
                break;
            case "success":
                staticEventPublisher.publishSuccessLog(currentTaskName, message);
                break;
            default:
                staticEventPublisher.publishInfoLog(currentTaskName, message);
                break;
        }
    }
    
    /**
     * 创建并发送测试任务
     * @param taskDto 任务参数
     * @return 响应结果
     */
    @PostMapping("/send-task")
    public ResponseEntity<Map<String, Object>> sendTestTask(@RequestBody TaskDto taskDto) {
        logger.info("接收到测试任务请求: {}", taskDto);
        Map<String, Object> response = new HashMap<>();
        
        try {
            String taskName = taskDto.getName();
            
            // 获取任务状态，如果存在则检查是否有活跃任务
            boolean hasActiveTask = false;
            try {
                hasActiveTask = singleBrowserManager.isProcessingTask();
            } catch (Exception e) {
                logger.warn("检查浏览器池状态出错", e);
            }
            
            if (hasActiveTask) {
                logger.info("当前有任务正在进行，将使用新任务名称: {}", taskName);
            } else {
                // 重置任务状态
                taskStateManager.resetTaskState(taskName);
                logger.info("重置任务状态: {}", taskName);
            }
            
            // 发布任务进度事件
            eventPublisher.publishProgressUpdate(taskName, "已提交，等待处理", 0);
            
            // 添加任务处理日志
            eventPublisher.publishInfoLog(taskName, "接收到新任务: " + taskName);
            eventPublisher.publishInfoLog(taskName, "准备浏览器环境...");
            
            // 发布任务到队列
            taskPublisher.publishSearchTask(taskName, taskDto.getRule(), taskDto.getTimeRange());
            logger.info("任务已发送到队列, 名称: {}", taskName);
            
            // 构建响应
            response.put("success", true);
            response.put("message", "任务已成功发送到队列");
            response.put("taskName", taskName);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("发送测试任务失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "发送测试任务失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取爬虫引擎状态
     * @return 爬虫引擎状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        logger.debug("查询爬虫引擎状态");
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从SingleBrowserManager获取浏览器状态
            String browserStatus = singleBrowserManager.getBrowserStatus();
            
            // 获取账号等级信息
            String accountLevel = crawlerConfig.getAccount().getLevel();
            int dataLimit = crawlerConfig.getCurrentAccountLimit();
            
            // 获取当前正在执行的任务名称
            String currentTaskName = taskStateManager.getCurrentExecutingTaskName();
            if (currentTaskName == null) {
                currentTaskName = "";
            }

            logger.debug("当前执行任务: {}", currentTaskName.isEmpty() ? "无" : currentTaskName);
            
            // 任务状态信息
            Map<String, Object> taskInfo = new HashMap<>();
            
            if (!currentTaskName.isEmpty()) {
                TaskStateManager.TaskState state = taskStateManager.getTaskState(currentTaskName);
                
                if (state != null) {
                    taskInfo.put("name", currentTaskName);
                    taskInfo.put("progress", state.getProgress());
                    taskInfo.put("processedItems", state.getProcessedItems());
                    taskInfo.put("currentPage", state.getCurrentPage());
                    taskInfo.put("totalPages", state.getTotalPages());
                    taskInfo.put("itemsInRange", state.getItemsInRange());
                    taskInfo.put("itemsOutOfRange", state.getItemsOutOfRange());
                    taskInfo.put("duplicateItems", state.getDuplicateItems());
                    taskInfo.put("currentUrl", state.getCurrentUrl());
                    taskInfo.put("processingSpeed", state.getProcessingSpeed());
                    taskInfo.put("estimatedRemainingTime", state.getEstimatedRemainingTime());
            
            // 计算任务执行时间
            String taskExecutionTime = "";
                    LocalDateTime startTime = state.getStartTime();
                    
                    if (startTime != null) {
                        Duration duration = Duration.between(startTime, LocalDateTime.now());
                long minutes = duration.toMinutes();
                long seconds = duration.getSeconds() % 60;
                taskExecutionTime = String.format("%d分%d秒", minutes, seconds);
                    } else {
                        taskExecutionTime = "等待计时中...";
                    }
                    
                    taskInfo.put("executionTime", taskExecutionTime);
                
                    // 获取任务日志
                    List<Map<String, Object>> logs = new ArrayList<>();
                    for (TaskStateManager.LogEntry log : state.getLogs()) {
                        Map<String, Object> logEntry = new HashMap<>();
                        logEntry.put("timestamp", log.getTimestamp());
                        logEntry.put("message", log.getMessage());
                        logEntry.put("type", log.getType());
                        logs.add(logEntry);
                    }
                    
                    taskInfo.put("logs", logs);
                }
            }
            
            // 获取任务监听器状态
            String consumerStatus = taskConsumer.getStatusInfo();
            
            // 构建响应
            response.put("browserStatus", browserStatus);
            response.put("accountLevel", accountLevel);
            response.put("dataLimit", dataLimit);
            response.put("consumerStatus", consumerStatus);
            // 获取活跃任务数量
            List<String> activeTaskNames = taskStateManager.getActiveTaskNames();
            response.put("activeTaskCount", activeTaskNames.size());
            response.put("currentTask", taskInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取状态失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "获取状态失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取指定任务的状态
     * @param taskName 任务名称
     * @return 任务状态
     */
    @GetMapping("/task-status/{taskName}")
    public ResponseEntity<Map<String, Object>> getTaskStatus(@PathVariable String taskName) {
        logger.debug("查询任务状态: {}", taskName);
        Map<String, Object> response = new HashMap<>();
        
        try {
            TaskStateManager.TaskState state = taskStateManager.getTaskState(taskName);
            
            if (state == null) {
                response.put("success", false);
                response.put("message", "未找到任务: " + taskName);
                return ResponseEntity.badRequest().body(response);
    }
    
            // 任务信息
            response.put("name", taskName);
            response.put("progress", state.getProgress());
            response.put("processedItems", state.getProcessedItems());
            response.put("currentPage", state.getCurrentPage());
            response.put("totalPages", state.getTotalPages());
            response.put("itemsInRange", state.getItemsInRange());
            response.put("itemsOutOfRange", state.getItemsOutOfRange());
            response.put("duplicateItems", state.getDuplicateItems());
            response.put("currentUrl", state.getCurrentUrl());
            response.put("processingSpeed", state.getProcessingSpeed());
            response.put("estimatedRemainingTime", state.getEstimatedRemainingTime());
            
            // 计算任务执行时间
            String taskExecutionTime = "";
            LocalDateTime startTime = state.getStartTime();
            
            if (startTime != null) {
                Duration duration = Duration.between(startTime, LocalDateTime.now());
                long minutes = duration.toMinutes();
                long seconds = duration.getSeconds() % 60;
                taskExecutionTime = String.format("%d分%d秒", minutes, seconds);
            }
            
            response.put("executionTime", taskExecutionTime);
            
            // 获取任务日志
            List<Map<String, Object>> logs = new ArrayList<>();
            for (TaskStateManager.LogEntry log : state.getLogs()) {
                Map<String, Object> logEntry = new HashMap<>();
                logEntry.put("timestamp", log.getTimestamp());
                logEntry.put("message", log.getMessage());
                logEntry.put("type", log.getType());
                logs.add(logEntry);
            }
            
            response.put("logs", logs);
            response.put("success", true);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取任务状态失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "获取任务状态失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 终止任务
     * @param taskName 任务名称
     * @return 操作结果
     */
    @PostMapping("/terminate-task/{taskName}")
    public ResponseEntity<Map<String, Object>> terminateTask(@PathVariable String taskName) {
        logger.info("请求终止任务: {}", taskName);
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 发布任务完成事件
            eventPublisher.publishProgressUpdate(taskName, "用户手动终止任务", 0);
            eventPublisher.publishWarningLog(taskName, "用户手动终止任务");
        
            // 重置任务状态
            taskStateManager.resetTaskState(taskName);
        
            // 构建响应
            response.put("success", true);
            response.put("message", "任务已终止: " + taskName);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("终止任务失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "终止任务失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取所有活跃任务
     * @return 活跃任务列表
     */
    @GetMapping("/active-tasks")
    public ResponseEntity<Map<String, Object>> getActiveTasks() {
        logger.debug("查询所有活跃任务");
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<String> activeTaskNames = taskStateManager.getActiveTaskNames();
            
            response.put("success", true);
            response.put("activeTasks", activeTaskNames);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取活跃任务失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "获取活跃任务失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 任务参数DTO
     */
    public static class TaskDto {
        private String name;
        private String rule;
        private String timeRange;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getRule() {
            return rule;
        }
        
        public void setRule(String rule) {
            this.rule = rule;
        }
        
        public String getTimeRange() {
            return timeRange;
        }
        
        public void setTimeRange(String timeRange) {
            this.timeRange = timeRange;
        }
        
        @Override
        public String toString() {
            return "TaskDto{" +
                    "name='" + name + '\'' +
                    ", rule='" + rule + '\'' +
                    ", timeRange='" + timeRange + '\'' +
                    '}';
        }
    }

    // ==================== 任务执行详情相关API ====================

    /**
     * 查询任务执行详情列表
     * @param taskName 任务名称（可选）
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
     * @return 执行详情列表
     */
    @GetMapping("/execution-details")
    public ResponseEntity<Map<String, Object>> getExecutionDetails(
            @RequestParam(required = false) String taskName,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        logger.info("查询任务执行详情: 任务名称={}, 开始日期={}, 结束日期={}", taskName, startDate, endDate);
        Map<String, Object> response = new HashMap<>();

        try {
            List<TaskExecutionDetail> details;

            if (taskName != null && !taskName.trim().isEmpty()) {
                if (startDate != null && endDate != null) {
                    // 按任务名称和时间范围查询
                    LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
                    LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
                    details = taskExecutionDetailRepository.findByTaskNameAndStartTimeBetweenOrderByStartTimeDesc(
                            taskName.trim(), start, end);
                } else {
                    // 只按任务名称查询
                    details = taskExecutionDetailRepository.findByTaskNameOrderByStartTimeDesc(taskName.trim());
                }
            } else if (startDate != null && endDate != null) {
                // 只按时间范围查询
                LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
                LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
                details = taskExecutionDetailRepository.findByStartTimeBetweenOrderByStartTimeDesc(start, end);
            } else {
                // 查询所有，限制最近100条
                details = taskExecutionDetailRepository.findRecentExecutions(
                        LocalDateTime.now().minusDays(30));
                if (details.size() > 100) {
                    details = details.subList(0, 100);
                }
            }

            response.put("success", true);
            response.put("data", details);
            response.put("total", details.size());
            response.put("message", "查询成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询任务执行详情失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取任务执行统计信息 - 修复版本，增强异常处理和null值处理
     * @param taskName 任务名称（可选）
     * @return 统计信息
     */
    @GetMapping("/execution-stats")
    public ResponseEntity<Map<String, Object>> getExecutionStats(
            @RequestParam(required = false) String taskName) {

        logger.info("查询任务执行统计: 任务名称={}", taskName);
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> stats = new HashMap<>();

            if (taskName != null && !taskName.trim().isEmpty()) {
                // 特定任务的统计
                logger.debug("查询特定任务统计: {}", taskName.trim());

                // 安全地执行数据库查询，处理可能的null值
                Long executionCount = safeGetExecutionCount(taskName.trim());
                Double avgDuration = safeGetAverageDuration(taskName.trim());
                Long totalValidResults = safeGetTotalValidResults(taskName.trim());

                stats.put("taskName", taskName.trim());
                stats.put("executionCount", executionCount);
                stats.put("averageDurationSeconds", avgDuration != null ? avgDuration.longValue() : 0L);
                stats.put("totalValidResults", totalValidResults);

                // 获取最新执行详情 - 安全处理
                TaskExecutionDetail latestExecution = safeGetLatestExecution(taskName.trim());
                if (latestExecution != null) {
                    // 创建安全的执行详情对象，避免序列化问题
                    Map<String, Object> executionInfo = createSafeExecutionInfo(latestExecution);
                    stats.put("lastExecution", executionInfo);
                }

            } else {
                // 全局统计
                logger.debug("查询全局统计");

                List<String> allTaskNames = safeGetAllTaskNames();
                Long totalExecutions = safeGetTotalExecutions();
                Long recentExecutions = safeGetRecentExecutions();

                stats.put("totalTaskNames", allTaskNames.size());
                stats.put("totalExecutions", totalExecutions);
                stats.put("recentExecutions", recentExecutions);
                stats.put("taskNames", allTaskNames);
            }

            response.put("success", true);
            response.put("data", stats);
            response.put("message", "统计查询成功");

            logger.debug("统计查询成功，返回数据: {}", stats);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询任务执行统计失败: {}", e.getMessage(), e);

            // 提供更详细的错误信息
            String errorDetail = e.getCause() != null ? e.getCause().getMessage() : e.getMessage();
            response.put("success", false);
            response.put("message", "统计查询失败: " + errorDetail);
            response.put("errorType", e.getClass().getSimpleName());

            return ResponseEntity.status(500).body(response);
        }
    }

    // ==================== 安全的数据库查询方法 ====================

    /**
     * 安全地获取执行次数
     */
    private Long safeGetExecutionCount(String taskName) {
        try {
            Long count = taskExecutionDetailRepository.countByTaskName(taskName);
            return count != null ? count : 0L;
        } catch (Exception e) {
            logger.warn("获取执行次数失败: {}", e.getMessage());
            return 0L;
        }
    }

    /**
     * 安全地获取平均执行时长
     */
    private Double safeGetAverageDuration(String taskName) {
        try {
            Double avgDuration = taskExecutionDetailRepository.getAverageExecutionDurationByTaskName(taskName);
            return avgDuration != null ? avgDuration : 0.0;
        } catch (Exception e) {
            logger.warn("获取平均执行时长失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 安全地获取总有效结果数
     */
    private Long safeGetTotalValidResults(String taskName) {
        try {
            Long totalResults = taskExecutionDetailRepository.getTotalValidResultsByTaskName(taskName);
            return totalResults != null ? totalResults : 0L;
        } catch (Exception e) {
            logger.warn("获取总有效结果数失败: {}", e.getMessage());
            return 0L;
        }
    }

    /**
     * 安全地获取最新执行详情
     */
    private TaskExecutionDetail safeGetLatestExecution(String taskName) {
        try {
            Optional<TaskExecutionDetail> latestExecution =
                taskExecutionDetailRepository.findFirstByTaskNameOrderByStartTimeDesc(taskName);
            return latestExecution.orElse(null);
        } catch (Exception e) {
            logger.warn("获取最新执行详情失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 安全地获取所有任务名称
     */
    private List<String> safeGetAllTaskNames() {
        try {
            List<String> taskNames = taskExecutionDetailRepository.findAllDistinctTaskNames();
            return taskNames != null ? taskNames : new ArrayList<>();
        } catch (Exception e) {
            logger.warn("获取所有任务名称失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 安全地获取总执行次数
     */
    private Long safeGetTotalExecutions() {
        try {
            Long total = taskExecutionDetailRepository.count();
            return total != null ? total : 0L;
        } catch (Exception e) {
            logger.warn("获取总执行次数失败: {}", e.getMessage());
            return 0L;
        }
    }

    /**
     * 安全地获取最近执行次数
     */
    private Long safeGetRecentExecutions() {
        try {
            LocalDateTime weekAgo = LocalDateTime.now().minusDays(7);
            LocalDateTime now = LocalDateTime.now();
            Long recent = taskExecutionDetailRepository.countByStartTimeBetween(weekAgo, now);
            return recent != null ? recent : 0L;
        } catch (Exception e) {
            logger.warn("获取最近执行次数失败: {}", e.getMessage());
            return 0L;
        }
    }

    /**
     * 创建安全的执行详情信息，避免序列化问题
     */
    private Map<String, Object> createSafeExecutionInfo(TaskExecutionDetail detail) {
        Map<String, Object> info = new HashMap<>();

        try {
            info.put("id", detail.getId());
            info.put("taskName", detail.getTaskName());
            info.put("startTime", detail.getStartTime());
            info.put("endTime", detail.getEndTime());
            info.put("executionDurationSeconds", detail.getExecutionDurationSeconds());
            info.put("totalResults", detail.getTotalResults());
            info.put("validResults", detail.getValidResults());
            info.put("duplicateResults", detail.getDuplicateResults());
            info.put("outOfRangeResults", detail.getOutOfRangeResults());
            info.put("executionStatus", detail.getExecutionStatus());
            info.put("lastAssetIp", detail.getLastAssetIp());
            info.put("lastAssetPort", detail.getLastAssetPort());
            info.put("lastAssetDomain", detail.getLastAssetDomain());
            info.put("lastAssetDiscoveryTime", detail.getLastAssetDiscoveryTime());
            info.put("createdAt", detail.getCreatedAt());

            // 安全地添加可能为null的字段
            info.put("errorMessage", detail.getErrorMessage());
            info.put("retryCount", detail.getRetryCount() != null ? detail.getRetryCount() : 0);
            info.put("peakMemoryUsageMb", detail.getPeakMemoryUsageMb() != null ? detail.getPeakMemoryUsageMb() : 0L);
            info.put("avgMemoryUsageMb", detail.getAvgMemoryUsageMb() != null ? detail.getAvgMemoryUsageMb() : 0L);
            info.put("browserRestartCount", detail.getBrowserRestartCount() != null ? detail.getBrowserRestartCount() : 0);

        } catch (Exception e) {
            logger.warn("创建安全执行详情信息失败: {}", e.getMessage());
            // 返回基本信息
            info.put("id", detail.getId());
            info.put("taskName", detail.getTaskName());
            info.put("executionStatus", detail.getExecutionStatus());
        }

        return info;
    }
}