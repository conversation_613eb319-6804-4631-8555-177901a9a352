package com.z3rd0.system.controller;

import com.z3rd0.common.model.SearchResult;
import com.z3rd0.system.dto.ApiResponse;
import com.z3rd0.system.dto.PageResponse;
import com.z3rd0.system.service.TaskPublisher;
import com.z3rd0.system.utils.ExceptionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import com.z3rd0.system.service.SearchResultService;
import java.util.Map;
import java.util.HashMap;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.time.LocalDateTime;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * 系统控制器
 * 提供系统级别的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class SystemController {

    private final TaskPublisher taskPublisher;
    private final SearchResultService searchResultService;
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

    // 记录应用启动时间，用于计算真正的服务运行时长
    private static final long APPLICATION_START_TIME = System.currentTimeMillis();

    /**
     * 系统状态检查端点
     * 提供系统运行状态、健康检查和基本监控信息
     * @return 系统状态信息
     */
    @GetMapping("/system/status")
    public ApiResponse<Map<String, Object>> getSystemStatus() {
        try {
            log.debug("获取系统状态信息");

            Map<String, Object> systemStatus = new HashMap<>();

            // 基本系统信息
            systemStatus.put("application", "workbench-system");
            systemStatus.put("status", "running");
            systemStatus.put("timestamp", LocalDateTime.now().toString());

            // 计算真正的应用运行时长（秒）
            long applicationUptimeMillis = System.currentTimeMillis() - APPLICATION_START_TIME;
            long applicationUptimeSeconds = applicationUptimeMillis / 1000;
            systemStatus.put("uptime", applicationUptimeSeconds);

            // 添加调试信息
            log.debug("应用启动时间: {}, 当前时间: {}, 运行时长: {}秒",
                     APPLICATION_START_TIME, System.currentTimeMillis(), applicationUptimeSeconds);

            // JVM信息
            Map<String, Object> jvmInfo = new HashMap<>();
            jvmInfo.put("version", System.getProperty("java.version"));
            jvmInfo.put("vendor", System.getProperty("java.vendor"));
            jvmInfo.put("vmName", System.getProperty("java.vm.name"));
            systemStatus.put("jvm", jvmInfo);

            // 内存使用情况
            MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("heapUsed", heapMemory.getUsed());
            memoryInfo.put("heapMax", heapMemory.getMax());
            memoryInfo.put("heapUsageRatio", (double) heapMemory.getUsed() / heapMemory.getMax());
            systemStatus.put("memory", memoryInfo);

            // 运行时信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> runtimeInfo = new HashMap<>();
            runtimeInfo.put("availableProcessors", runtime.availableProcessors());
            runtimeInfo.put("totalMemory", runtime.totalMemory());
            runtimeInfo.put("freeMemory", runtime.freeMemory());
            systemStatus.put("runtime", runtimeInfo);

            // 健康状态评估
            double memoryUsageRatio = (double) heapMemory.getUsed() / heapMemory.getMax();
            String healthStatus = "healthy";
            if (memoryUsageRatio > 0.9) {
                healthStatus = "critical";
            } else if (memoryUsageRatio > 0.8) {
                healthStatus = "warning";
            }
            systemStatus.put("health", healthStatus);

            log.debug("系统状态获取成功: 健康状态={}, 内存使用率={:.2f}%",
                     healthStatus, memoryUsageRatio * 100);

            return ApiResponse.success(systemStatus, "系统状态获取成功");

        } catch (Exception e) {
            log.error("获取系统状态失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取系统状态失败: " + e.getMessage());
        }
    }

    /**
     * 处理favicon.ico请求
     * 返回204 No Content状态，避免NoResourceFoundException
     * @return 空响应
     */
    @GetMapping("/favicon.ico")
    public ResponseEntity<Void> favicon() {
        log.debug("处理favicon.ico请求");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * 启动任务（已废弃，建议使用TaskController中的相关接口）
     * @param id 任务ID
     * @return 处理结果
     * @deprecated 此接口已废弃，请使用 /api/tasks/{id}/start 接口
     */
    @Deprecated
    @GetMapping("/start")
    public ApiResponse<String> startTask(@RequestParam("id") String id) {
        log.warn("使用了已废弃的启动任务接口，任务ID: {}", id);

        // 参数验证
        ExceptionUtils.requireNonEmpty(id, "taskId");

        // 使用默认参数发布任务
        String taskName = "Task_" + id;
        String rule = "title: \"测试\" AND country: \"China\"";
        String timeRange = "2023-01-01 00:00:00,2023-12-31 23:59:59";

        taskPublisher.publishSearchTask(taskName, rule, timeRange);

        String message = "任务[" + id + "]已提交到队列";
        log.info("任务发送完成，任务ID: {}", id);

        return ApiResponse.success(message, "任务启动成功（建议使用新接口）");
    }

    /**
     * 分页+条件搜索接口，返回统一响应结构，适配Pure Admin表格
     */
    @GetMapping("/search/results")
    public ApiResponse<PageResponse<SearchResult>> getResults(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String ip,
            @RequestParam(required = false) String domain,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String org,
            @RequestParam(required = false) String statusCode,
            @RequestParam(required = false) String locationCountry,
            @RequestParam(required = false) String asn,
            @RequestParam(required = false) Integer isRead,
            @RequestParam(required = false) Integer isExcluded,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime
    ) {
        // 创建按创建时间倒序排序的分页请求
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<SearchResult> resultPage = searchResultService.findByConditions(
            ip, domain, title, org, statusCode, locationCountry, asn,
            isRead, isExcluded, startTime, endTime, pageable);

        PageResponse<SearchResult> pageResponse = PageResponse.of(resultPage);
        return ApiResponse.success(pageResponse, "查询搜索结果成功");
    }

    /**
     * 获取统计数据
     */
    @GetMapping("/search/statistics")
    public ApiResponse<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = searchResultService.getStatistics();
        return ApiResponse.success(statistics, "获取统计数据成功");
    }

    /**
     * 导出Excel
     */
    @GetMapping("/search/export")
    public void exportExcel(
            @RequestParam(required = false) String ip,
            @RequestParam(required = false) String domain,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String org,
            @RequestParam(required = false) String statusCode,
            @RequestParam(required = false) String locationCountry,
            @RequestParam(required = false) String asn,
            @RequestParam(required = false) Integer isRead,
            @RequestParam(required = false) Integer isExcluded,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            HttpServletResponse response
    ) throws IOException {
        searchResultService.exportToExcel(ip, domain, title, org, statusCode,
            locationCountry, asn, isRead, isExcluded, startTime, endTime, response);
    }

    /**
     * 批量操作
     */
    @PostMapping("/search/batch")
    public ApiResponse<String> batchOperation(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        String operation = (String) request.get("operation");
        Integer value = (Integer) request.get("value");

        searchResultService.batchOperation(ids, operation, value);
        return ApiResponse.success(null, "批量操作成功");
    }

    /**
     * 批量删除
     */
    @PostMapping("/search/batch-delete")
    public ApiResponse<String> batchDelete(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");

        searchResultService.batchDelete(ids);
        return ApiResponse.success(null, "批量删除成功");
    }
}