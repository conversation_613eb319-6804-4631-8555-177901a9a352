-- 创建任务执行详情表
-- 用于记录每次任务执行的详细统计信息，分析任务执行效果和调整搜索策略

CREATE TABLE IF NOT EXISTS task_execution_details (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    
    -- 任务基本信息
    task_id BIGINT COMMENT '关联的任务ID',
    task_name VARCHAR(255) NOT NULL COMMENT '任务名称',
    search_rule TEXT COMMENT '搜索规则',
    
    -- 时间信息
    start_time TIMESTAMP NOT NULL COMMENT '任务开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '任务结束时间',
    execution_duration_seconds BIGINT COMMENT '执行耗时（秒）',
    
    -- 结果统计
    total_results INT DEFAULT 0 NOT NULL COMMENT '搜索结果总数',
    valid_results INT DEFAULT 0 NOT NULL COMMENT '有效结果数（时间范围内）',
    duplicate_results INT DEFAULT 0 NOT NULL COMMENT '重复结果数',
    out_of_range_results INT DEFAULT 0 NOT NULL COMMENT '超出时间范围结果数',
    
    -- 最后资产信息
    last_asset_ip VARCHAR(45) COMMENT '最后一条资产的IP地址',
    last_asset_port VARCHAR(10) COMMENT '最后一条资产的端口号',
    last_asset_domain VARCHAR(500) COMMENT '最后一条资产的域名',
    last_asset_discovery_time VARCHAR(30) COMMENT '最后一条资产的发现时间',
    
    -- 执行状态
    execution_status VARCHAR(20) DEFAULT 'COMPLETED' NOT NULL COMMENT '执行状态：COMPLETED, FAILED',
    error_message TEXT COMMENT '错误信息（如果执行失败）',
    
    -- 创建时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    
    -- 添加索引
    INDEX idx_task_id (task_id),
    INDEX idx_task_name (task_name),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_created_at (created_at),
    INDEX idx_execution_status (execution_status),
    INDEX idx_task_name_start_time (task_name, start_time),
    
    -- 外键约束（如果需要的话）
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL
) COMMENT = '任务执行详情表 - 记录每次任务执行的详细统计信息';

-- 添加表注释说明
ALTER TABLE task_execution_details COMMENT = '任务执行详情表 - 用于记录每次任务执行的详细统计信息，包括时间信息、结果统计、最后资产信息等，用于分析任务执行效果和调整搜索策略，评估VIP账号升级的必要性';
