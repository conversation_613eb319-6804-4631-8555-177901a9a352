package com.z3rd0.system.controller;

import com.z3rd0.common.model.Task;
import com.z3rd0.common.model.TaskExecutionDetail;
import com.z3rd0.system.dto.*;
import com.z3rd0.system.service.TaskService;
import com.z3rd0.system.service.TaskExecutionService;
import com.z3rd0.system.service.TaskPublisher;
import com.z3rd0.system.service.BatchTaskService;
import com.z3rd0.system.utils.ExceptionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/tasks")
@RequiredArgsConstructor
public class TaskController {
    
    private final TaskService taskService;
    private final TaskExecutionService taskExecutionService;
    private final TaskPublisher taskPublisher;
    private final BatchTaskService batchTaskService;

    /**
     * 分页查询任务列表，支持条件筛选
     */
    @GetMapping
    public ApiResponse<PageResponse<Task>> getTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Task> taskPage = taskService.findByConditions(name, status, startTime, endTime, pageable);

        PageResponse<Task> pageResponse = PageResponse.of(taskPage);
        return ApiResponse.success(pageResponse, "查询任务列表成功");
    }

    /**
     * 根据ID获取任务详情
     */
    @GetMapping("/{id}")
    public ApiResponse<Task> getTask(@PathVariable Long id) {
        Task task = taskService.findById(id)
                .orElseThrow(() -> ExceptionUtils.taskNotFound(id));
        return ApiResponse.success(task, "获取任务详情成功");
    }

    /**
     * 创建新任务
     */
    @PostMapping
    public ApiResponse<Task> createTask(@RequestBody Task task) {
        // 使用异常工具类进行参数验证
        ExceptionUtils.requireNonNull(task, "task");
        ExceptionUtils.requireNonEmpty(task.getName(), "taskName");
        ExceptionUtils.requireNonEmpty(task.getRule(), "taskRule");

        // 清理输入数据
        task.setName(task.getName().trim());
        task.setRule(task.getRule().trim());

        Task savedTask = taskService.save(task);
        return ApiResponse.success(savedTask, "任务创建成功");
    }

    /**
     * 更新任务
     */
    @PutMapping("/{id}")
    public ApiResponse<Task> updateTask(@PathVariable Long id, @RequestBody Task task) {
        // 使用异常工具类进行参数验证
        ExceptionUtils.requireNonNull(task, "task");
        ExceptionUtils.requireNonEmpty(task.getName(), "taskName");
        ExceptionUtils.requireNonEmpty(task.getRule(), "taskRule");

        // 查找现有任务，如果不存在则抛出异常
        Task existing = taskService.findById(id)
                .orElseThrow(() -> ExceptionUtils.taskNotFound(id));

        // 更新任务信息
        existing.setName(task.getName().trim());
        existing.setRule(task.getRule().trim());
        // 保留其他字段不变，如筛选范围由scanner模块执行后更新

        Task updatedTask = taskService.save(existing);
        log.info("任务更新成功: ID={}, 名称={}", id, updatedTask.getName());

        return ApiResponse.success(updatedTask, "任务更新成功");
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteTask(@PathVariable Long id) {
        // 验证任务是否存在
        if (!taskService.existsById(id)) {
            throw ExceptionUtils.taskNotFound(id);
        }

        taskService.deleteById(id);
        log.info("任务删除成功: ID={}", id);

        return ApiResponse.success(null, "任务删除成功");
    }

    /**
     * 启动单个任务
     */
    @PostMapping("/{id}/start")
    public ApiResponse<TaskStartResult> startTask(
            @PathVariable Long id,
            @RequestBody TaskStartRequest request) {

        // 参数验证
        ExceptionUtils.requireNonNull(request, "request");
        ExceptionUtils.requireNonEmpty(request.getTimeRange(), "timeRange");

        // 查找任务，如果不存在则抛出异常
        Task task = taskService.findById(id)
                .orElseThrow(() -> ExceptionUtils.taskNotFound(id));

        // 发布任务到队列
        taskPublisher.publishSearchTaskWithId(task.getId(), task.getName(), task.getRule(), request.getTimeRange());

        // 构建响应结果
        TaskStartResult result = new TaskStartResult(
                id,
                task.getName(),
                request.getTimeRange(),
                "任务已成功提交到队列"
        );

        log.info("任务启动成功: ID={}, 名称={}, 时间范围={}", id, task.getName(), request.getTimeRange());
        return ApiResponse.success(result, "任务启动成功");
    }

    /**
     * 批量启动所有任务
     */
    @PostMapping("/start-all")
    public ApiResponse<BatchStartResult> startAllTasks(@RequestBody TaskStartRequest request) {
        // 参数验证
        ExceptionUtils.requireNonNull(request, "request");
        ExceptionUtils.requireNonEmpty(request.getTimeRange(), "timeRange");

        List<Task> allTasks = taskService.findAll();
        int successCount = 0;
        int failCount = 0;

        for (Task task : allTasks) {
            try {
                taskPublisher.publishSearchTaskWithId(task.getId(), task.getName(), task.getRule(), request.getTimeRange());
                successCount++;
                log.debug("任务启动成功: ID={}, 名称={}", task.getId(), task.getName());
            } catch (Exception e) {
                failCount++;
                log.error("任务启动失败: ID={}, 名称={}, 错误={}", task.getId(), task.getName(), e.getMessage(), e);
            }
        }

        // 构建响应结果
        BatchStartResult result = new BatchStartResult(
                allTasks.size(),
                successCount,
                failCount,
                request.getTimeRange()
        );

        log.info("批量启动任务完成: 总数={}, 成功={}, 失败={}, 时间范围={}",
                allTasks.size(), successCount, failCount, request.getTimeRange());

        return ApiResponse.success(result, "批量启动任务完成");
    }

    /**
     * 更新任务执行信息（供scanner模块调用）
     */
    @PostMapping("/{id}/update-execution")
    public ApiResponse<TaskExecutionUpdateResult> updateTaskExecution(
            @PathVariable Long id,
            @RequestBody TaskExecutionUpdateRequest request) {

        // 参数验证
        ExceptionUtils.requireNonNull(request, "request");

        // 查找任务，如果不存在则抛出异常
        Task task = taskService.findById(id)
                .orElseThrow(() -> ExceptionUtils.taskNotFound(id));

        // 更新任务执行信息
        task.updateExecutionInfo(request.getFilterRange());
        taskService.save(task);

        // 构建响应结果
        TaskExecutionUpdateResult result = new TaskExecutionUpdateResult(
                id,
                request.getFilterRange()
        );

        log.info("任务执行信息更新成功: ID={}, 筛选范围={}", id, request.getFilterRange());
        return ApiResponse.success(result, "任务执行信息更新成功");
    }

    /**
     * 批量创建任务
     */
    @PostMapping("/batch/create")
    public ApiResponse<BatchOperationResult> batchCreateTasks(@RequestBody BatchTaskCreateRequest request) {
        // 参数验证
        ExceptionUtils.requireNonNull(request, "request");
        if (request.getKeywords() == null || request.getKeywords().isEmpty()) {
            throw new IllegalArgumentException("关键字列表不能为空");
        }

        // 验证关键字数量限制（防止过多任务创建）
        if (request.getKeywords().size() > 100) {
            throw new IllegalArgumentException("单次批量创建任务数量不能超过100个");
        }

        log.info("开始批量创建任务: 关键字数量={}, 名称前缀={}",
                request.getKeywords().size(), request.getNamePrefix());

        // 使用批量任务服务处理
        BatchOperationResult result = batchTaskService.batchCreateTasks(request);

        return ApiResponse.success(result, String.format("批量创建完成，成功%d个，失败%d个",
            result.getSuccessCount(), result.getFailureCount()));
    }

    /**
     * 批量启动任务
     */
    @PostMapping("/batch/start")
    public ApiResponse<BatchOperationResult> batchStartTasks(@RequestBody BatchTaskStartRequest request) {
        // 参数验证
        ExceptionUtils.requireNonNull(request, "request");
        if (request.getTaskIds() == null || request.getTaskIds().isEmpty()) {
            throw new IllegalArgumentException("taskIds不能为空");
        }
        ExceptionUtils.requireNonEmpty(request.getTimeRange(), "timeRange");

        log.info("开始批量启动任务: 任务数量={}, 时间范围={}", request.getTaskIds().size(), request.getTimeRange());

        // 使用批量任务服务处理
        BatchOperationResult result = batchTaskService.batchStartTasks(
            request.getTaskIds(), request.getTimeRange(), request.getForceStart());

        return ApiResponse.success(result, String.format("批量启动完成，成功%d个，失败%d个",
            result.getSuccessCount(), result.getFailureCount()));
    }

    /**
     * 批量删除任务
     */
    @DeleteMapping("/batch")
    public ApiResponse<BatchOperationResult> batchDeleteTasks(@RequestBody BatchTaskDeleteRequest request) {
        // 参数验证
        ExceptionUtils.requireNonNull(request, "request");
        if (request.getTaskIds() == null || request.getTaskIds().isEmpty()) {
            throw new IllegalArgumentException("taskIds不能为空");
        }

        log.info("开始批量删除任务: 任务数量={}", request.getTaskIds().size());

        // 使用批量任务服务处理
        BatchOperationResult result = batchTaskService.batchDeleteTasks(
            request.getTaskIds(), request.getForceDelete());

        return ApiResponse.success(result, String.format("批量删除完成，成功%d个，失败%d个",
            result.getSuccessCount(), result.getFailureCount()));
    }

    /**
     * 批量更新任务状态
     */
    @PutMapping("/batch/status")
    public ApiResponse<BatchOperationResult> batchUpdateTaskStatus(@RequestBody BatchTaskStatusUpdateRequest request) {
        // 参数验证
        ExceptionUtils.requireNonNull(request, "request");
        if (request.getTaskIds() == null || request.getTaskIds().isEmpty()) {
            throw new IllegalArgumentException("taskIds不能为空");
        }
        ExceptionUtils.requireNonEmpty(request.getStatus(), "status");

        log.info("开始批量更新任务状态: 任务数量={}, 目标状态={}", request.getTaskIds().size(), request.getStatus());

        // 使用批量任务服务处理
        BatchOperationResult result = batchTaskService.batchUpdateTaskStatus(
            request.getTaskIds(), request.getStatus(), request.getErrorMessage());

        return ApiResponse.success(result, String.format("批量状态更新完成，成功%d个，失败%d个",
            result.getSuccessCount(), result.getFailureCount()));
    }

    /**
     * 获取批量任务统计信息
     */
    @PostMapping("/batch/stats")
    public ApiResponse<BatchTaskService.BatchTaskStats> getBatchTaskStats(@RequestBody List<Long> taskIds) {
        // 参数验证
        if (taskIds == null || taskIds.isEmpty()) {
            throw new IllegalArgumentException("taskIds不能为空");
        }

        BatchTaskService.BatchTaskStats stats = batchTaskService.getBatchTaskStats(taskIds);
        return ApiResponse.success(stats, "获取批量任务统计成功");
    }

    // ==================== 任务执行详情相关端点 ====================

    /**
     * 查询任务执行详情列表
     * @param taskName 任务名称（可选）
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 执行详情分页结果
     */
    @GetMapping("/execution-details")
    public ApiResponse<Page<TaskExecutionDetail>> getExecutionDetails(
            @RequestParam(required = false) String taskName,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        log.info("查询任务执行详情: 任务名称={}, 开始日期={}, 结束日期={}, 页码={}, 大小={}",
                taskName, startDate, endDate, page, size);

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TaskExecutionDetail> details = taskExecutionService.findByConditions(
                    taskName, startDate, endDate, pageable);

            return ApiResponse.success(details, "查询成功");

        } catch (Exception e) {
            log.error("查询任务执行详情失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务执行统计信息
     * @param taskName 任务名称（可选）
     * @return 统计信息
     */
    @GetMapping("/execution-stats")
    public ApiResponse<Map<String, Object>> getExecutionStats(
            @RequestParam(required = false) String taskName) {

        log.info("查询任务执行统计: 任务名称={}", taskName);

        try {
            Map<String, Object> stats = taskExecutionService.getExecutionStats(taskName);
            return ApiResponse.success(stats, "统计查询成功");

        } catch (Exception e) {
            log.error("查询任务执行统计失败: {}", e.getMessage(), e);
            return ApiResponse.error("统计查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取任务执行详情
     * @param id 执行详情ID
     * @return 执行详情
     */
    @GetMapping("/execution-details/{id}")
    public ApiResponse<TaskExecutionDetail> getExecutionDetail(@PathVariable Long id) {
        TaskExecutionDetail detail = taskExecutionService.findById(id)
                .orElseThrow(() -> new RuntimeException("执行详情不存在: " + id));
        return ApiResponse.success(detail, "获取执行详情成功");
    }

    /**
     * 获取所有任务名称列表
     * @return 任务名称列表
     */
    @GetMapping("/execution-task-names")
    public ApiResponse<List<String>> getExecutionTaskNames() {
        try {
            List<String> taskNames = taskExecutionService.getAllTaskNames();
            return ApiResponse.success(taskNames, "获取任务名称列表成功");
        } catch (Exception e) {
            log.error("获取任务名称列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取任务名称列表失败: " + e.getMessage());
        }
    }
}
