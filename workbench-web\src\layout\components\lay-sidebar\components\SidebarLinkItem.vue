<script setup lang="ts">
import { computed } from "vue";
import { isUrl } from "@pureadmin/utils";
import { menuType } from "@/layout/types";

const props = defineProps<{
  to: menuType;
}>();

const isExternalLink = computed(() => isUrl(props.to.name));

// 验证路由参数的有效性
const validateRouteParams = (params: any): boolean => {
  if (!params || typeof params !== 'object') return true;

  // 特别检查 IP 参数
  if (params.ip !== undefined) {
    const ipParam = params.ip;
    if (!ipParam ||
        typeof ipParam !== 'string' ||
        ipParam.trim() === '' ||
        ipParam === 'undefined' ||
        ipParam === 'null') {
      return false;
    }
  }

  return true;
};

// 检查是否应该渲染链接
const shouldRenderLink = computed(() => {
  // 检查路径是否包含无效参数
  if (props.to.path && typeof props.to.path === 'string') {
    if (props.to.path.includes('undefined') || props.to.path.includes('null')) {
      return false;
    }
  }

  // 检查参数有效性
  if (props.to.params && !validateRouteParams(props.to.params)) {
    return false;
  }

  return true;
});

const getLinkProps = (item: menuType) => {
  if (isExternalLink.value) {
    return {
      href: item.name,
      target: "_blank",
      rel: "noopener"
    };
  }

  // 验证路由参数
  if (item.params && !validateRouteParams(item.params)) {
    console.warn('Invalid route parameters detected in sidebar link, using path instead:', item.params);
    return {
      to: { path: item.path }
    };
  }

  return {
    to: item
  };
};
</script>

<template>
  <component
    v-if="shouldRenderLink"
    :is="isExternalLink ? 'a' : 'router-link'"
    v-bind="getLinkProps(to)"
  >
    <slot />
  </component>
  <!-- 降级渲染：参数无效时显示普通链接 -->
  <span v-else class="invalid-link" style="opacity: 0.5; cursor: not-allowed;">
    <slot />
  </span>
</template>
