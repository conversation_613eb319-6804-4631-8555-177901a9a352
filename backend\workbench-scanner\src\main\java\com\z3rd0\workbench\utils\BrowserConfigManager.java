package com.z3rd0.workbench.utils;

import com.z3rd0.workbench.config.CrawlerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 浏览器配置管理类
 * 负责管理浏览器相关的配置信息
 */
@Component
public class BrowserConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(BrowserConfigManager.class);
    
    // 静态配置字段
    private static String staticUsername;
    private static String staticPassword;
    private static boolean staticIsVipAccount = false;
    private static int staticNormalDeviceThreshold = 1; // 普通账号默认阈值
    private static int staticVipDeviceThreshold = 4;    // VIP账号默认阈值
    
    // 实例配置参数
    private int defaultDelay = 1000; // 默认步骤间延时1秒
    private int typeDelay = 150; // 输入文字时的延时
    private boolean slowMode = true; // 是否启用慢速模式
    private int pageLoadTimeout = 60000; // 页面加载超时时间（毫秒）
    
    /**
     * 设置静态账号信息
     */
    public static void setStaticCredentials(String username, String password) {
        staticUsername = username;
        staticPassword = password;
        
        // 同时更新VIP状态
        updateVipStatus();
        
        logger.info("静态账号凭据已设置，账号类型: {}", staticIsVipAccount ? "VIP" : "普通");
    }
    
    /**
     * 更新账号VIP状态（根据用户名判断）
     */
    private static void updateVipStatus() {
        // 根据静态用户名判断是否为VIP账号
        staticIsVipAccount = (staticUsername != null && staticUsername.toLowerCase().contains("vip"));
    }
    
    /**
     * 判断当前账号是否为VIP账号
     */
    public static boolean isVipAccount() {
        return staticIsVipAccount;
    }
    
    /**
     * 设置静态设备阈值
     */
    public static void setStaticDeviceThresholds(int normalThreshold, int vipThreshold) {
        staticNormalDeviceThreshold = normalThreshold;
        staticVipDeviceThreshold = vipThreshold;
        logger.info("静态设备阈值已设置 - 普通账号: {}, VIP账号: {}", normalThreshold, vipThreshold);
    }
    
    /**
     * 获取设备阈值信息
     */
    public DeviceThresholdInfo getDeviceThresholdInfo() {
        int deviceThreshold;
        String accountLevel;
        boolean isVip;
        
        try {
            // 使用静态方法判断VIP状态
            isVip = isVipAccount();
            deviceThreshold = isVip ? staticVipDeviceThreshold : staticNormalDeviceThreshold;
            accountLevel = isVip ? "VIP" : "普通";
            logger.debug("使用静态配置的设备阈值: {}, 账号等级: {}", deviceThreshold, accountLevel);
        } catch (Exception e) {
            logger.warn("获取设备阈值时出错: {}，使用默认值", e.getMessage());
            // 使用最保守的值
            deviceThreshold = staticNormalDeviceThreshold;
            accountLevel = "普通";
            isVip = false;
        }
        
        return new DeviceThresholdInfo(deviceThreshold, accountLevel, isVip);
    }
    
    /**
     * 获取登录用户名
     */
    public String getUsername(CrawlerConfig crawlerConfig) {
        // 优先使用传入的配置
        if (crawlerConfig != null && crawlerConfig.getUsername() != null && !crawlerConfig.getUsername().trim().isEmpty()) {
            return crawlerConfig.getUsername().trim();
        }

        // 其次使用静态配置
        if (staticUsername != null && !staticUsername.trim().isEmpty()) {
            return staticUsername.trim();
        }

        // 配置为空时抛出异常，强制要求正确配置
        String errorMsg = "登录用户名未配置！请在application.yml中配置crawler.account.username";
        logger.error(errorMsg);
        throw new IllegalStateException(errorMsg);
    }
    
    /**
     * 获取登录密码
     */
    public String getPassword(CrawlerConfig crawlerConfig) {
        // 优先使用传入的配置
        if (crawlerConfig != null && crawlerConfig.getPassword() != null && !crawlerConfig.getPassword().trim().isEmpty()) {
            return crawlerConfig.getPassword().trim();
        }

        // 其次使用静态配置
        if (staticPassword != null && !staticPassword.trim().isEmpty()) {
            return staticPassword.trim();
        }

        // 配置为空时抛出异常，强制要求正确配置
        String errorMsg = "登录密码未配置！请在application.yml中配置crawler.account.password";
        logger.error(errorMsg);
        throw new IllegalStateException(errorMsg);
    }
    
    /**
     * 设备阈值信息数据类
     */
    public static class DeviceThresholdInfo {
        private final int threshold;
        private final String levelName;
        private final boolean isVip;
        
        public DeviceThresholdInfo(int threshold, String levelName, boolean isVip) {
            this.threshold = threshold;
            this.levelName = levelName;
            this.isVip = isVip;
        }
        
        public int getThreshold() {
            return threshold;
        }
        
        public String getLevelName() {
            return levelName;
        }
        
        public boolean isVip() {
            return isVip;
        }
    }
    
    // Getter和Setter方法
    public int getDefaultDelay() {
        return defaultDelay;
    }
    
    public void setDefaultDelay(int defaultDelay) {
        this.defaultDelay = defaultDelay;
    }
    
    public int getTypeDelay() {
        return typeDelay;
    }
    
    public void setTypeDelay(int typeDelay) {
        this.typeDelay = typeDelay;
    }
    
    public boolean isSlowMode() {
        return slowMode;
    }
    
    public void setSlowMode(boolean slowMode) {
        this.slowMode = slowMode;
    }
    
    public int getPageLoadTimeout() {
        return pageLoadTimeout;
    }
    
    public void setPageLoadTimeout(int pageLoadTimeout) {
        this.pageLoadTimeout = pageLoadTimeout;
    }
    
    public static String getStaticUsername() {
        return staticUsername;
    }
    
    public static String getStaticPassword() {
        return staticPassword;
    }
    
    public static int getStaticNormalDeviceThreshold() {
        return staticNormalDeviceThreshold;
    }
    
    public static int getStaticVipDeviceThreshold() {
        return staticVipDeviceThreshold;
    }
}
