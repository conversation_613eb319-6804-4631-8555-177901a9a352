import { asset } from "@/router/enums";
import type { RouteConfigsTable } from "~/types/router";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/asset",
  name: "Asset",
  component: Layout,
  redirect: "/asset/index",
  meta: {
    icon: "ep:files",
    title: "资产列表",
    rank: asset
  },
  children: [
    {
      path: "/asset/index",
      name: "AssetList",
      component: () => import("@/views/result/index.vue"),
      meta: {
        title: "资产列表"
      }
    }
  ]
} satisfies RouteConfigsTable;
