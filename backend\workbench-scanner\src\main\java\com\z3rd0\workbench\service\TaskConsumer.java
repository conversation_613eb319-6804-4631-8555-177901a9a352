package com.z3rd0.workbench.service;

import com.z3rd0.workbench.config.RabbitMQConfig;
import com.z3rd0.workbench.controller.TaskTestController;
import com.z3rd0.workbench.exception.TaskProcessingException;
import com.z3rd0.common.model.Task;
import com.z3rd0.common.model.TaskExecutionDetail;
import com.z3rd0.common.model.SearchResult;
import com.z3rd0.workbench.repository.TaskRepository;
import com.z3rd0.workbench.repository.TaskExecutionDetailRepository;
import com.z3rd0.workbench.repository.SearchResultRepository;
import com.z3rd0.workbench.utils.BrowserManager;
import com.z3rd0.workbench.utils.TimeRangeValidator;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.nio.charset.StandardCharsets;

@Service
public class TaskConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(TaskConsumer.class);
    
    // 消费者状态常量
    private static final String CONSUMER_STATUS_IDLE = "空闲";
    private static final String CONSUMER_STATUS_PROCESSING = "处理中";
    private static final String CONSUMER_STATUS_ERROR = "错误";
    
    // 状态跟踪变量 - 使用线程安全的数据结构
    private final AtomicReference<String> status = new AtomicReference<>(CONSUMER_STATUS_IDLE);
    private volatile LocalDateTime lastProcessTime = LocalDateTime.now();
    private volatile LocalDateTime lastSuccessTime = null;
    private volatile LocalDateTime lastFailureTime = null;
    private final AtomicInteger taskProcessCount = new AtomicInteger(0);
    private final AtomicInteger taskSuccessCount = new AtomicInteger(0);
    private final AtomicInteger taskFailureCount = new AtomicInteger(0);
    // 使用线程安全的队列来存储日志，避免内存泄漏
    private final ConcurrentLinkedQueue<String> acknowledgementLogs = new ConcurrentLinkedQueue<>();
    private static final int MAX_LOG_SIZE = 100; // 最大日志条数
    
    @Autowired
    private TaskService taskService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private BrowserManager browserManager;
    
    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private TaskExecutionDetailRepository taskExecutionDetailRepository;

    @Autowired
    private SearchResultRepository searchResultRepository;

    @Autowired
    private TaskStateManager taskStateManager;

    @Autowired
    private TimeRangeValidator timeRangeValidator;

    @Autowired
    private RetryStrategyManager retryStrategyManager;

    @Autowired(required = false)
    private SimpMessagingTemplate messagingTemplate;

    // 记录最后接收任务的时间
    private LocalDateTime lastReceiveTime = LocalDateTime.now();

    // 任务重试次数配置
    @Value("${workbench.task.max-retry:3}")
    private int maxRetryCount = 3;

    // 重试等待时间（秒）
    @Value("${workbench.task.retry-delay:5}")
    private int retryDelaySeconds = 5;

    // 线程池执行器，用于延迟任务
    private final ScheduledExecutorService taskExecutor = Executors.newScheduledThreadPool(2);


    // 添加确认日志 - 使用线程安全的队列
    private void addAcknowledgementLog(String logEntry) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        String formattedLog = timestamp + " - " + logEntry;

        // 添加到队列
        acknowledgementLogs.offer(formattedLog);

        // 记录到系统日志
        logger.info("任务确认: {}", logEntry);

        // 限制队列大小，防止内存泄漏
        while (acknowledgementLogs.size() > MAX_LOG_SIZE) {
            acknowledgementLogs.poll(); // 移除最旧的日志
        }
    }
    
    /**
     * 监听RabbitMQ普通队列，接收任务并处理
     * @param message 消息对象，包含搜索规则等任务信息
     */
    @RabbitListener(queues = RabbitMQConfig.QUEUE_NAME, containerFactory = "rabbitListenerContainerFactory")
    public void receiveTask(Message message) {
        processTask(message, "普通队列");
    }

    /**
     * 监听RabbitMQ优先级队列，接收高优先级任务并处理
     * @param message 消息对象，包含搜索规则等任务信息
     */
    @RabbitListener(queues = RabbitMQConfig.PRIORITY_QUEUE_NAME, containerFactory = "rabbitListenerContainerFactory")
    public void receivePriorityTask(Message message) {
        processTask(message, "优先级队列");
    }

    /**
     * 处理任务的通用方法
     * @param message 消息对象
     * @param queueType 队列类型（用于日志）
     */
    private void processTask(Message message, String queueType) {
        lastReceiveTime = LocalDateTime.now();
        
        try {
            // 从消息头中获取任务信息 - 我们完全依赖headers而不是消息体
            Map<String, Object> headers = message.getMessageProperties().getHeaders();
            
            // 获取重试次数，如果不存在则默认为0
            int retryCount = getIntHeader(headers, "retryCount", 0);
            
            // 获取必要的任务信息，提供默认值避免空值
            String taskName = getStringHeader(headers, "name", "未命名任务");
            String searchRule = getStringHeader(headers, "rule", "");
            String timeRange = getStringHeader(headers, "timeRange", "");
            
            // 获取任务ID（如果存在）
            Long taskId = getLongHeader(headers, "taskId", null);

            // 获取优先级信息
            Integer priority = getIntHeader(headers, "priority", 5);

            logger.info("接收到搜索任务[{}]: {}, 规则: {}, 优先级: {}, 重试次数: {}, 任务ID: {}",
                       queueType, taskName, searchRule, priority, retryCount, taskId);
            
            if (searchRule == null || searchRule.trim().isEmpty()) {
                logger.warn("搜索规则为空，跳过处理");
                return;
            }
            
            // 先设置任务名称，确保在处理任务前就有名称
            if (taskName != null && !taskName.isEmpty()) {
                TaskTestController.setCurrentTaskName(taskName);
            }
            
            // 更新任务状态为处理中（如果任务ID存在）
            if (taskId != null) {
                updateTaskStatus(taskId, "PROCESSING", null);
            }

            // 验证时间范围有效性
            TimeRangeValidator.TimeRangeValidationResult validationResult =
                timeRangeValidator.validateTimeRange(timeRange, taskName);

            // 检查是否应该跳过任务执行
            if (validationResult.shouldSkipExecution()) {
                String skipReason = validationResult.getSkipReason();
                logger.warn("跳过任务执行 - 任务: {}, 原因: {}", taskName, skipReason);

                // 更新任务状态为已跳过
                if (taskId != null) {
                    markTaskAsSkipped(taskId, skipReason);
                    recordSkippedTaskExecution(taskId, taskName, searchRule, skipReason);
                }

                return;
            }

            // 规范化输入参数
            if (timeRange == null || timeRange.trim().isEmpty()) {
                // 如果没有提供时间范围，默认使用当前时间的前后一年
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime oneYearAgo = now.minusYears(1);
                LocalDateTime oneYearLater = now.plusYears(1);

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                timeRange = oneYearAgo.format(formatter) + "," + oneYearLater.format(formatter);
                logger.info("未提供时间范围，使用默认范围: {}", timeRange);
            }
            
            // 处理搜索任务
            try {
                // 更新任务状态为扫描中
                if (taskId != null) {
                    updateTaskStatus(taskId, "PROCESSING", null);
                }

                // 推送任务开始进度
                if (taskId != null) {
                    pushTaskProgress(taskId, 1, 5, "初始化", "开始处理搜索任务");
                    pushTaskLog(taskId, "INFO", "开始处理搜索任务: " + taskName);
                }

                taskService.processSearchTask(searchRule, timeRange, taskName);
                logger.info("搜索任务处理完成: {}", taskName);

                // 推送任务完成进度
                if (taskId != null) {
                    pushTaskProgress(taskId, 5, 5, "完成", "搜索任务处理完成");
                    pushTaskLog(taskId, "INFO", "搜索任务处理完成");
                }

                // 任务处理成功
                status.set(CONSUMER_STATUS_IDLE);
                lastSuccessTime = LocalDateTime.now();
                taskSuccessCount.incrementAndGet();
                addAcknowledgementLog("任务处理成功: " + taskName);

                // 更新任务状态为已完成，并更新执行信息
                if (taskId != null) {
                    updateTaskCompletion(taskId, timeRange);
                }
                
            } catch (TaskProcessingException e) {
                handleTaskException(e, message, headers, retryCount, taskName, taskId,
                    "处理搜索任务时发生业务异常", e.getErrorCode());
            } catch (Exception e) {
                handleTaskException(e, message, headers, retryCount, taskName, taskId,
                    "处理搜索任务时发生未知异常", null);
            }
            
        } catch (Exception e) {
            logger.error("消息处理异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 统一处理任务异常（使用智能重试策略）
     */
    private void handleTaskException(Exception e, Message message, Map<String, Object> headers,
                                   int retryCount, String taskName, Long taskId,
                                   String logPrefix, String errorCode) {
        String errorMessage = e.getMessage();
        String fullErrorMessage = errorCode != null ?
            errorMessage + ", 错误代码: " + errorCode : errorMessage;

        logger.error("{}: {}, 规则: {}, 错误: {}",
                   logPrefix, taskName, getStringHeader(headers, "rule", ""), fullErrorMessage, e);

        // 使用智能重试策略判断是否重试
        if (taskId != null) {
            RetryStrategyManager.RetryDecision decision = retryStrategyManager.shouldRetry(taskId, e, retryCount);

            if (decision.shouldRetry()) {
                // 增加重试次数
                int newRetryCount = retryCount + 1;
                long delayMs = decision.getDelayMs();

                logger.info("智能重试策略决定重试任务: {}, 延迟: {}ms, 重试次数: {}, 原因: {}",
                           taskName, delayMs, newRetryCount, decision.getReason());

                // 使用智能延迟重新发送任务
                scheduleSmartRetry(message, headers, newRetryCount, taskName, delayMs);

                // 更新任务状态为等待重试
                updateTaskStatus(taskId, "PENDING", "等待智能重试: " + decision.getReason());
            } else {
                logger.warn("智能重试策略决定不再重试任务: {}, 原因: {}", taskName, decision.getReason());
                sendToDeadLetterQueue(headers, taskName, "智能重试策略拒绝: " + decision.getReason());

                // 更新任务状态为扫描出错
                updateTaskStatus(taskId, "FAILED", fullErrorMessage);
            }
        } else {
            // 没有taskId的情况，使用传统重试逻辑
            if (retryCount < maxRetryCount) {
                int newRetryCount = retryCount + 1;
                logger.info("任务将在{}秒后重试，当前重试次数: {}/{}", retryDelaySeconds, newRetryCount, maxRetryCount);
                scheduleRetry(message, headers, newRetryCount, taskName);
            } else {
                logger.warn("任务重试次数已达上限({}次)，将发送到死信队列: {}", maxRetryCount, taskName);
                sendToDeadLetterQueue(headers, taskName, "重试次数已达上限: " + fullErrorMessage);
            }
        }
    }

    /**
     * 安排任务重试（同步处理，不使用线程池）
     */
    private void scheduleRetry(Message originalMessage, Map<String, Object> headers, int retryCount, String taskName) {
        try {
            // 创建消息属性
            org.springframework.amqp.core.MessageProperties properties = createMessageProperties(headers, retryCount);

            // 创建有效的JSON格式消息体
            String messageBody = createMessageBody(originalMessage, taskName);

            // 同步延迟发送
            try {
                // 睡眠指定的延迟时间
                TimeUnit.SECONDS.sleep(retryDelaySeconds);

                // 创建新消息并发送
                org.springframework.amqp.core.Message retryMessage =
                        new org.springframework.amqp.core.Message(
                                messageBody.getBytes(StandardCharsets.UTF_8), properties);

                rabbitTemplate.send(RabbitMQConfig.EXCHANGE_NAME,
                                   RabbitMQConfig.ROUTING_KEY,
                                   retryMessage);

                logger.info("已重新发送任务到队列: {}, 重试次数: {}", taskName, retryCount);
            } catch (Exception ex) {
                logger.error("安排任务重试失败: {}", ex.getMessage(), ex);
                sendToDeadLetterQueue(headers, taskName, "重试执行失败: " + ex.getMessage());
            }

        } catch (Exception ex) {
            logger.error("创建重试任务失败: {}", ex.getMessage(), ex);
            // 如果无法重试，发送到死信队列
            sendToDeadLetterQueue(headers, taskName, "无法创建重试任务: " + ex.getMessage());
        }
    }

    /**
     * 创建消息属性
     */
    private org.springframework.amqp.core.MessageProperties createMessageProperties(Map<String, Object> headers, int retryCount) {
        org.springframework.amqp.core.MessageProperties properties =
                new org.springframework.amqp.core.MessageProperties();
        properties.setContentType("application/json");

        // 更新重试次数
        Map<String, Object> newHeaders = new HashMap<>(headers);
        newHeaders.put("retryCount", retryCount);
        newHeaders.put("lastRetryTime", LocalDateTime.now().toString());
        properties.setHeaders(newHeaders);

        return properties;
    }

    /**
     * 创建消息体
     */
    private String createMessageBody(Message originalMessage, String taskName) {
        return originalMessage.getBody() != null ?
                new String(originalMessage.getBody(), StandardCharsets.UTF_8) :
                String.format("{\"id\":%d,\"name\":\"%s\"}", System.currentTimeMillis(), taskName);
    }
    
    /**
     * 发送任务到死信队列
     */
    private void sendToDeadLetterQueue(Map<String, Object> headers, String taskName, String errorText) {
        try {
            // 创建包含错误信息的header
            Map<String, Object> errorHeaders = new HashMap<>(headers);
            errorHeaders.put("error", errorText);
            errorHeaders.put("errorTime", LocalDateTime.now().toString());
            
            org.springframework.amqp.core.MessageProperties properties = 
                    new org.springframework.amqp.core.MessageProperties();
            properties.setContentType("application/json");
            properties.setHeaders(errorHeaders);
            
            // 创建有效的JSON格式错误消息
            String errorJson = String.format("{\"taskName\":\"%s\",\"error\":\"%s\",\"timestamp\":%d}",
                    taskName, "处理失败", System.currentTimeMillis());
            
            org.springframework.amqp.core.Message errorMessage = 
                    new org.springframework.amqp.core.Message(
                            errorJson.getBytes(StandardCharsets.UTF_8), properties);
            
            rabbitTemplate.send(RabbitMQConfig.DEAD_LETTER_EXCHANGE, 
                               RabbitMQConfig.DEAD_LETTER_ROUTING_KEY, 
                               errorMessage);
            
            logger.info("已将失败任务发送到死信队列: {}", taskName);
        } catch (Exception ex) {
            logger.error("发送到死信队列失败: {}", ex.getMessage(), ex);
        }
    }
    
    /**
     * 智能重试调度（支持自定义延迟）
     */
    private void scheduleSmartRetry(Message message, Map<String, Object> headers,
                                   int newRetryCount, String taskName, long delayMs) {
        try {
            // 更新重试次数
            headers.put("retryCount", newRetryCount);

            // 创建新的消息属性
            MessageProperties newProperties = new MessageProperties();
            newProperties.setContentType("application/json");
            newProperties.setHeaders(headers);

            // 创建新消息
            Message retryMessage = new Message(message.getBody(), newProperties);

            // 使用线程池延迟发送
            taskExecutor.schedule(() -> {
                try {
                    rabbitTemplate.send(RabbitMQConfig.EXCHANGE_NAME, RabbitMQConfig.ROUTING_KEY, retryMessage);
                    logger.info("智能重试任务已重新发送到队列: {}, 重试次数: {}", taskName, newRetryCount);
                } catch (Exception e) {
                    logger.error("智能重试任务发送失败: {}, 错误: {}", taskName, e.getMessage(), e);
                }
            }, delayMs, java.util.concurrent.TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            logger.error("智能重试调度失败: {}, 错误: {}", taskName, e.getMessage(), e);
        }
    }

    /**
     * 从headers中安全获取字符串值
     */
    private String getStringHeader(Map<String, Object> headers, String key, String defaultValue) {
        if (headers != null && headers.containsKey(key)) {
            Object value = headers.get(key);
            return value != null ? value.toString() : defaultValue;
        }
        return defaultValue;
    }
    
    /**
     * 从headers中安全获取整数值
     */
    private int getIntHeader(Map<String, Object> headers, String key, int defaultValue) {
        if (headers != null && headers.containsKey(key)) {
            Object value = headers.get(key);
            if (value != null) {
                try {
                    if (value instanceof Number) {
                        return ((Number) value).intValue();
                    } else {
                        return Integer.parseInt(value.toString());
                    }
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }
        return defaultValue;
    }
    
    /**
     * 从headers中安全获取Long值
     */
    private Long getLongHeader(Map<String, Object> headers, String key, Long defaultValue) {
        if (headers != null && headers.containsKey(key)) {
            Object value = headers.get(key);
            if (value != null) {
                try {
                    if (value instanceof Number) {
                        return ((Number) value).longValue();
                    } else {
                        return Long.parseLong(value.toString());
                    }
                } catch (NumberFormatException e) {
                    logger.warn("无法解析头部字段为Long: {}={}", key, value);
                }
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取服务状态信息
     * @return 包含状态信息的字符串
     */
    public String getStatusInfo() {
        // 获取浏览器状态
        String browserStatus = browserManager.getBrowserStatus();
        
        // 计算最后任务接收时间距现在的时长
        long idleMinutes = java.time.Duration.between(lastReceiveTime, LocalDateTime.now()).toMinutes();
        
        return String.format(
                "任务监听器状态:\n" +
                "- 浏览器状态: %s\n" +
                "- 最后接收任务时间: %s\n" +
                "- 空闲时间: %d 分钟",
                browserStatus, 
                lastReceiveTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                idleMinutes);
    }


    
    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 任务状态
     * @param errorMessage 错误信息（如果有）
     */
    private void updateTaskStatus(Long taskId, String status, String errorMessage) {
        try {
            // 查找任务
            Optional<Task> taskOpt = taskRepository.findById(taskId);
            if (taskOpt.isPresent()) {
                Task task = taskOpt.get();
                task.setStatus(status);

                // 设置错误信息（如果有）
                if (errorMessage != null) {
                    task.setErrorMessage(errorMessage);
                }

                // 如果任务已完成，设置完成时间
                if ("COMPLETED".equals(status)) {
                    task.setCompletedAt(LocalDateTime.now());
                }

                // 保存更新后的任务
                taskRepository.save(task);
                logger.info("更新任务状态成功: ID={}, 状态={}", taskId, status);
            } else {
                logger.warn("未找到要更新的任务: ID={}", taskId);
            }
        } catch (Exception e) {
            logger.error("更新任务状态失败: ID={}, 状态={}, 错误: {}", taskId, status, e.getMessage(), e);
        }
    }

    /**
     * 标记任务为已跳过
     * @param taskId 任务ID
     * @param skipReason 跳过原因
     */
    private void markTaskAsSkipped(Long taskId, String skipReason) {
        try {
            // 查找任务
            Optional<Task> taskOpt = taskRepository.findById(taskId);
            if (taskOpt.isPresent()) {
                Task task = taskOpt.get();
                task.markAsSkipped(skipReason);

                // 保存更新后的任务
                taskRepository.save(task);
                logger.info("任务已标记为跳过: ID={}, 原因={}", taskId, skipReason);
            } else {
                logger.warn("未找到要标记为跳过的任务: ID={}", taskId);
            }
        } catch (Exception e) {
            logger.error("标记任务为跳过失败: ID={}, 错误: {}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 更新任务完成状态和执行信息
     * @param taskId 任务ID
     * @param timeRange 时间范围（筛选范围）
     */
    private void updateTaskCompletion(Long taskId, String timeRange) {
        try {
            // 查找任务
            Optional<Task> taskOpt = taskRepository.findById(taskId);
            if (taskOpt.isPresent()) {
                Task task = taskOpt.get();

                // 更新任务状态和执行信息
                task.setStatus("COMPLETED");
                task.setFilterRange(timeRange);  // 设置实际执行的筛选范围
                task.setExecutedAt(LocalDateTime.now());  // 设置执行时间
                task.setCompletedAt(LocalDateTime.now()); // 设置完成时间

                // 保存更新后的任务
                taskRepository.save(task);
                logger.info("任务完成，已更新所有信息: ID={}, 状态=COMPLETED, 筛选范围={}",
                           taskId, timeRange);

                // 记录任务执行详情
                recordTaskExecutionDetail(task);

            } else {
                logger.warn("未找到要更新的任务: ID={}", taskId);
            }
        } catch (Exception e) {
            logger.error("更新任务完成信息失败: ID={}, 筛选范围={}, 错误: {}",
                        taskId, timeRange, e.getMessage(), e);
        }
    }

    /**
     * 记录任务执行详情
     * @param task 已完成的任务
     */
    private void recordTaskExecutionDetail(Task task) {
        try {
            // 获取任务状态信息
            TaskStateManager.TaskState taskState = taskStateManager.getTaskState(task.getName());
            if (taskState == null) {
                logger.warn("未找到任务状态信息，无法记录执行详情: {}", task.getName());
                return;
            }

            // 创建执行详情记录
            TaskExecutionDetail detail = new TaskExecutionDetail();
            detail.setTaskId(task.getId());
            detail.setTaskName(task.getName());
            detail.setSearchRule(task.getRule());

            // 设置时间信息
            detail.setStartTime(taskState.getStartTime());
            detail.setEndTime(task.getCompletedAt());
            detail.calculateDuration();

            // 设置结果统计
            detail.setValidResults(taskState.getItemsInRange());
            detail.setDuplicateResults(taskState.getDuplicateItems());
            detail.setOutOfRangeResults(taskState.getItemsOutOfRange());
            detail.setTotalResults(taskState.getItemsInRange() + taskState.getItemsOutOfRange() + taskState.getDuplicateItems());

            // 获取最后一条资产信息
            setLastAssetInfo(detail, task.getName());

            // 标记为成功完成
            detail.markAsCompleted();

            // 保存执行详情
            taskExecutionDetailRepository.save(detail);
            logger.info("任务执行详情已记录: 任务={}, 有效结果={}, 重复结果={}, 超出范围={}, 总耗时={}秒",
                       task.getName(), detail.getValidResults(), detail.getDuplicateResults(),
                       detail.getOutOfRangeResults(), detail.getExecutionDurationSeconds());

        } catch (Exception e) {
            logger.error("记录任务执行详情失败: 任务={}, 错误: {}", task.getName(), e.getMessage(), e);
        }
    }

    /**
     * 设置最后一条资产信息
     * @param detail 执行详情对象
     * @param taskName 任务名称
     */
    private void setLastAssetInfo(TaskExecutionDetail detail, String taskName) {
        try {
            // 查询该任务的最后一条搜索结果（按ID降序）
            List<SearchResult> lastResults = searchResultRepository.findByTaskNameOrderByIdDesc(taskName);
            if (!lastResults.isEmpty()) {
                SearchResult lastResult = lastResults.get(0);
                detail.setLastAssetIp(lastResult.getIp());
                detail.setLastAssetPort(lastResult.getPort());
                detail.setLastAssetDomain(lastResult.getDomain());
                detail.setLastAssetDiscoveryTime(lastResult.getTime());

                logger.debug("设置最后资产信息: IP={}, 端口={}, 域名={}, 发现时间={}",
                           lastResult.getIp(), lastResult.getPort(), lastResult.getDomain(), lastResult.getTime());
            } else {
                logger.warn("未找到任务的搜索结果，无法设置最后资产信息: {}", taskName);
            }
        } catch (Exception e) {
            logger.error("设置最后资产信息失败: 任务={}, 错误: {}", taskName, e.getMessage(), e);
        }
    }

    /**
     * 记录跳过的任务执行详情
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param searchRule 搜索规则
     * @param skipReason 跳过原因
     */
    private void recordSkippedTaskExecution(Long taskId, String taskName, String searchRule, String skipReason) {
        try {
            TaskExecutionDetail detail = new TaskExecutionDetail();
            detail.setTaskId(taskId);
            detail.setTaskName(taskName);
            detail.setSearchRule(searchRule);
            detail.setStartTime(LocalDateTime.now());
            detail.setEndTime(LocalDateTime.now());
            detail.setExecutionStatus("SKIPPED");
            detail.setErrorMessage(skipReason);
            detail.setTotalResults(0);
            detail.setValidResults(0);
            detail.setDuplicateResults(0);
            detail.setOutOfRangeResults(0);
            detail.setRetryCount(0);
            detail.setRetryStrategy("NONE");
            detail.setTotalRetryDelayMs(0L);
            detail.calculateDuration();

            taskExecutionDetailRepository.save(detail);
            logger.info("跳过任务执行详情已记录: 任务ID={}, 任务名称={}, 跳过原因={}",
                       taskId, taskName, skipReason);
        } catch (Exception e) {
            logger.error("记录跳过任务执行详情失败: 任务ID={}, 错误: {}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 记录失败任务的重试信息
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param searchRule 搜索规则
     * @param retryCount 重试次数
     * @param retryStrategy 重试策略
     * @param totalDelayMs 总延迟时间
     * @param errorMessage 错误信息
     */
    private void recordFailedTaskWithRetryInfo(Long taskId, String taskName, String searchRule,
                                              int retryCount, String retryStrategy, long totalDelayMs, String errorMessage) {
        try {
            TaskExecutionDetail detail = new TaskExecutionDetail();
            detail.setTaskId(taskId);
            detail.setTaskName(taskName);
            detail.setSearchRule(searchRule);
            detail.setStartTime(LocalDateTime.now().minusSeconds(30)); // 估算开始时间
            detail.setEndTime(LocalDateTime.now());
            detail.setExecutionStatus("FAILED");
            detail.setErrorMessage(errorMessage);
            detail.setTotalResults(0);
            detail.setValidResults(0);
            detail.setDuplicateResults(0);
            detail.setOutOfRangeResults(0);
            detail.setRetryCount(retryCount);
            detail.setRetryStrategy(retryStrategy);
            detail.setTotalRetryDelayMs(totalDelayMs);
            detail.calculateDuration();

            taskExecutionDetailRepository.save(detail);
            logger.info("失败任务重试信息已记录: 任务ID={}, 重试次数={}, 策略={}, 总延迟={}ms",
                       taskId, retryCount, retryStrategy, totalDelayMs);
        } catch (Exception e) {
            logger.error("记录失败任务重试信息失败: 任务ID={}, 错误: {}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 推送任务进度更新
     * @param taskId 任务ID
     * @param currentStep 当前步骤
     * @param totalSteps 总步骤数
     * @param stepName 步骤名称
     * @param description 描述
     */
    private void pushTaskProgress(Long taskId, int currentStep, int totalSteps, String stepName, String description) {
        if (messagingTemplate != null && taskId != null) {
            try {
                Map<String, Object> progress = new HashMap<>();
                progress.put("taskId", taskId);
                progress.put("currentStep", currentStep);
                progress.put("totalSteps", totalSteps);
                progress.put("stepName", stepName);
                progress.put("description", description);
                progress.put("percentage", totalSteps > 0 ? (double) currentStep / totalSteps * 100 : 0);
                progress.put("timestamp", LocalDateTime.now().toString());

                Map<String, Object> message = new HashMap<>();
                message.put("type", "PROGRESS_UPDATE");
                message.put("taskId", taskId);
                message.put("progress", progress);
                message.put("timestamp", LocalDateTime.now().toString());

                // 推送到特定任务的订阅者
                messagingTemplate.convertAndSend("/topic/task/" + taskId + "/progress", message);

                logger.debug("推送任务进度: 任务ID={}, 步骤={}/{}, 进度={:.1f}%",
                           taskId, currentStep, totalSteps, (double) currentStep / totalSteps * 100);
            } catch (Exception e) {
                logger.error("推送任务进度失败: 任务ID={}, 错误={}", taskId, e.getMessage(), e);
            }
        }
    }

    /**
     * 推送任务日志
     * @param taskId 任务ID
     * @param level 日志级别
     * @param message 日志消息
     */
    private void pushTaskLog(Long taskId, String level, String message) {
        if (messagingTemplate != null && taskId != null) {
            try {
                Map<String, Object> logMessage = new HashMap<>();
                logMessage.put("type", "LOG_MESSAGE");
                logMessage.put("taskId", taskId);
                logMessage.put("level", level);
                logMessage.put("message", message);
                logMessage.put("timestamp", LocalDateTime.now().toString());

                // 推送到特定任务的日志订阅者
                messagingTemplate.convertAndSend("/topic/task/" + taskId + "/logs", logMessage);
            } catch (Exception e) {
                logger.error("推送任务日志失败: 任务ID={}, 错误={}", taskId, e.getMessage(), e);
            }
        }
    }
}