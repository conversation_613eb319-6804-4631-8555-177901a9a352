<template>
  <div class="batch-operations">
    <!-- 批量选择工具栏 -->
    <div class="batch-toolbar" v-show="selectedTasks.length > 0">
      <div class="selection-info">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          已选择 {{ selectedTasks.length }} 个任务
        </el-checkbox>
      </div>
      
      <div class="batch-actions">
        <el-button
          type="primary"
          :icon="VideoPlay"
          @click="showBatchStartDialog = true"
          :disabled="selectedTasks.length === 0"
        >
          批量启动
        </el-button>
        
        <el-button
          type="warning"
          :icon="Edit"
          @click="showBatchStatusDialog = true"
          :disabled="selectedTasks.length === 0"
        >
          批量更新状态
        </el-button>
        
        <el-button
          type="danger"
          :icon="Delete"
          @click="showBatchDeleteDialog = true"
          :disabled="selectedTasks.length === 0"
        >
          批量删除
        </el-button>
        
        <el-button
          type="info"
          :icon="DataAnalysis"
          @click="showBatchStats"
          :disabled="selectedTasks.length === 0"
        >
          统计信息
        </el-button>
        
        <el-button
          :icon="Close"
          @click="clearSelection"
        >
          取消选择
        </el-button>
      </div>
    </div>

    <!-- 批量启动对话框 -->
    <el-dialog
      v-model="showBatchStartDialog"
      title="批量启动任务"
      width="500px"
    >
      <el-form :model="batchStartForm" label-width="100px">
        <el-form-item label="时间范围" required>
          <el-input
            v-model="batchStartForm.timeRange"
            placeholder="例如: 2024-01-01 00:00:00,2024-01-31 23:59:59"
          />
        </el-form-item>
        <el-form-item label="强制启动">
          <el-switch v-model="batchStartForm.forceStart" />
          <span class="form-tip">忽略任务状态检查，强制启动所有选中任务</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBatchStartDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleBatchStart"
          :loading="batchStartLoading"
        >
          确认启动 ({{ selectedTasks.length }}个)
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量状态更新对话框 -->
    <el-dialog
      v-model="showBatchStatusDialog"
      title="批量更新任务状态"
      width="500px"
    >
      <el-form :model="batchStatusForm" label-width="100px">
        <el-form-item label="目标状态" required>
          <el-select v-model="batchStatusForm.status" placeholder="选择状态">
            <el-option label="待处理" value="PENDING" />
            <el-option label="处理中" value="PROCESSING" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="失败" value="FAILED" />
            <el-option label="已跳过" value="SKIPPED" />
          </el-select>
        </el-form-item>
        <el-form-item label="错误信息">
          <el-input
            v-model="batchStatusForm.errorMessage"
            type="textarea"
            placeholder="可选，设置错误信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBatchStatusDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleBatchStatusUpdate"
          :loading="batchStatusLoading"
        >
          确认更新 ({{ selectedTasks.length }}个)
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量删除确认对话框 -->
    <el-dialog
      v-model="showBatchDeleteDialog"
      title="批量删除任务"
      width="500px"
    >
      <div class="delete-warning">
        <el-icon class="warning-icon"><WarningFilled /></el-icon>
        <div>
          <p>确定要删除选中的 <strong>{{ selectedTasks.length }}</strong> 个任务吗？</p>
          <p class="warning-text">此操作不可撤销，请谨慎操作！</p>
        </div>
      </div>
      
      <el-form :model="batchDeleteForm" label-width="100px">
        <el-form-item label="强制删除">
          <el-switch v-model="batchDeleteForm.forceDelete" />
          <span class="form-tip">忽略任务状态检查，强制删除所有选中任务</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBatchDeleteDialog = false">取消</el-button>
        <el-button
          type="danger"
          @click="handleBatchDelete"
          :loading="batchDeleteLoading"
        >
          确认删除 ({{ selectedTasks.length }}个)
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量统计信息对话框 -->
    <el-dialog
      v-model="showBatchStatsDialog"
      title="批量任务统计"
      width="600px"
    >
      <div v-if="batchStats" class="stats-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic title="总任务数" :value="batchStats.totalCount" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="待处理" :value="batchStats.pendingCount" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="处理中" :value="batchStats.processingCount" />
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="8">
            <el-statistic title="已完成" :value="batchStats.completedCount" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="失败" :value="batchStats.failedCount" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="已跳过" :value="batchStats.skippedCount" />
          </el-col>
        </el-row>
      </div>
      
      <template #footer>
        <el-button @click="showBatchStatsDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 批量操作结果对话框 -->
    <el-dialog
      v-model="showResultDialog"
      :title="resultDialogTitle"
      width="700px"
    >
      <div v-if="batchResult" class="result-content">
        <div class="result-summary">
          <el-tag type="success" size="large">成功: {{ batchResult.successCount }}</el-tag>
          <el-tag type="danger" size="large" style="margin-left: 10px;">
            失败: {{ batchResult.failureCount }}
          </el-tag>
        </div>
        
        <el-table :data="batchResult.results" style="margin-top: 20px;" max-height="300">
          <el-table-column prop="taskId" label="任务ID" width="80" />
          <el-table-column prop="taskName" label="任务名称" />
          <el-table-column prop="success" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.success ? 'success' : 'danger'">
                {{ row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="结果信息" />
        </el-table>
      </div>
      
      <template #footer>
        <el-button @click="showResultDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Edit, Delete, DataAnalysis, Close, WarningFilled } from '@element-plus/icons-vue'
import {
  batchStartTasks,
  batchDeleteTasks,
  batchUpdateTaskStatus,
  getBatchTaskStats,
  type BatchTaskStartRequest,
  type BatchTaskDeleteRequest,
  type BatchTaskStatusUpdateRequest,
  type BatchOperationResult,
  type BatchTaskStats,
  type Task
} from '@/api/task'

interface Props {
  selectedTasks: number[]
  allTasks: Task[]
}

interface Emits {
  (e: 'update:selectedTasks', value: number[]): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const showBatchStartDialog = ref(false)
const showBatchStatusDialog = ref(false)
const showBatchDeleteDialog = ref(false)
const showBatchStatsDialog = ref(false)
const showResultDialog = ref(false)

const batchStartLoading = ref(false)
const batchStatusLoading = ref(false)
const batchDeleteLoading = ref(false)

const batchStartForm = ref<BatchTaskStartRequest>({
  taskIds: [],
  timeRange: '',
  forceStart: false
})

const batchStatusForm = ref<BatchTaskStatusUpdateRequest>({
  taskIds: [],
  status: '',
  errorMessage: ''
})

const batchDeleteForm = ref<BatchTaskDeleteRequest>({
  taskIds: [],
  forceDelete: false
})

const batchStats = ref<BatchTaskStats | null>(null)
const batchResult = ref<BatchOperationResult | null>(null)
const resultDialogTitle = ref('')

// 计算属性
const selectAll = computed({
  get: () => props.selectedTasks.length === props.allTasks.length && props.allTasks.length > 0,
  set: (value: boolean) => {
    if (value) {
      emit('update:selectedTasks', props.allTasks.map(task => task.id))
    } else {
      emit('update:selectedTasks', [])
    }
  }
})

const isIndeterminate = computed(() => {
  return props.selectedTasks.length > 0 && props.selectedTasks.length < props.allTasks.length
})

// 方法
const handleSelectAll = (value: boolean) => {
  selectAll.value = value
}

const clearSelection = () => {
  emit('update:selectedTasks', [])
}

const handleBatchStart = async () => {
  if (!batchStartForm.value.timeRange) {
    ElMessage.warning('请输入时间范围')
    return
  }
  
  batchStartLoading.value = true
  try {
    batchStartForm.value.taskIds = props.selectedTasks
    const response = await batchStartTasks(batchStartForm.value)
    
    batchResult.value = response.data
    resultDialogTitle.value = '批量启动结果'
    showBatchStartDialog.value = false
    showResultDialog.value = true
    
    ElMessage.success(`批量启动完成：成功${response.data.successCount}个，失败${response.data.failureCount}个`)
    emit('refresh')
  } catch (error) {
    ElMessage.error('批量启动失败')
  } finally {
    batchStartLoading.value = false
  }
}

const handleBatchStatusUpdate = async () => {
  if (!batchStatusForm.value.status) {
    ElMessage.warning('请选择目标状态')
    return
  }
  
  batchStatusLoading.value = true
  try {
    batchStatusForm.value.taskIds = props.selectedTasks
    const response = await batchUpdateTaskStatus(batchStatusForm.value)
    
    batchResult.value = response.data
    resultDialogTitle.value = '批量状态更新结果'
    showBatchStatusDialog.value = false
    showResultDialog.value = true
    
    ElMessage.success(`批量状态更新完成：成功${response.data.successCount}个，失败${response.data.failureCount}个`)
    emit('refresh')
  } catch (error) {
    ElMessage.error('批量状态更新失败')
  } finally {
    batchStatusLoading.value = false
  }
}

const handleBatchDelete = async () => {
  batchDeleteLoading.value = true
  try {
    batchDeleteForm.value.taskIds = props.selectedTasks
    const response = await batchDeleteTasks(batchDeleteForm.value)
    
    batchResult.value = response.data
    resultDialogTitle.value = '批量删除结果'
    showBatchDeleteDialog.value = false
    showResultDialog.value = true
    
    ElMessage.success(`批量删除完成：成功${response.data.successCount}个，失败${response.data.failureCount}个`)
    emit('refresh')
    clearSelection()
  } catch (error) {
    ElMessage.error('批量删除失败')
  } finally {
    batchDeleteLoading.value = false
  }
}

const showBatchStats = async () => {
  try {
    const response = await getBatchTaskStats(props.selectedTasks)
    batchStats.value = response.data
    showBatchStatsDialog.value = true
  } catch (error) {
    ElMessage.error('获取统计信息失败')
  }
}
</script>

<style scoped>
.batch-operations {
  margin-bottom: 16px;
}

.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  margin-bottom: 16px;
}

.selection-info {
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.delete-warning {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--el-color-warning-light-9);
  border-radius: 6px;
  margin-bottom: 20px;
}

.warning-icon {
  color: var(--el-color-warning);
  font-size: 20px;
  margin-top: 2px;
}

.warning-text {
  color: var(--el-color-warning);
  font-size: 12px;
  margin: 4px 0 0 0;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-left: 8px;
}

.stats-content {
  padding: 20px 0;
}

.result-content {
  padding: 10px 0;
}

.result-summary {
  text-align: center;
  margin-bottom: 20px;
}
</style>
