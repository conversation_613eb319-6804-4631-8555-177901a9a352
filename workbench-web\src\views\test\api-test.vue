<template>
  <div class="api-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>API通信测试</span>
          <el-button type="primary" @click="runAllTests"
            >运行所有测试</el-button
          >
        </div>
      </template>

      <div class="test-section">
        <h3>测试结果</h3>
        <div v-if="loading" class="loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>测试中...</span>
        </div>

        <div
          v-for="(result, index) in testResults"
          :key="index"
          class="test-result"
        >
          <el-alert
            :title="result.name"
            :type="result.success ? 'success' : 'error'"
            :description="result.message"
            show-icon
            :closable="false"
          />
          <div v-if="result.data" class="result-data">
            <el-collapse>
              <el-collapse-item title="响应数据" name="1">
                <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>

      <div class="test-buttons">
        <el-button @click="testTasksAPI">测试任务列表API</el-button>
        <el-button @click="testSearchResultsAPI">测试搜索结果API</el-button>
        <el-button @click="testSystemAPI">测试系统API</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { http } from "@/utils/http";
import { ElMessage } from "element-plus";
import { Loading } from "@element-plus/icons-vue";

interface TestResult {
  name: string;
  success: boolean;
  message: string;
  data?: any;
}

const loading = ref(false);
const testResults = ref<TestResult[]>([]);

// 添加测试结果
const addTestResult = (result: TestResult) => {
  testResults.value.push(result);
};

// 清空测试结果
const clearResults = () => {
  testResults.value = [];
};

// 测试任务列表API
const testTasksAPI = async () => {
  try {
    const response = await http.request("get", "/api/tasks", {
      params: { page: 0, size: 10 }
    });

    addTestResult({
      name: "任务列表API测试",
      success: true,
      message: "请求成功，返回任务列表数据",
      data: response
    });

    ElMessage.success("任务列表API测试通过");
  } catch (error: any) {
    addTestResult({
      name: "任务列表API测试",
      success: false,
      message: `请求失败: ${error.message || "未知错误"}`,
      data: error.response?.data
    });

    ElMessage.error("任务列表API测试失败");
  }
};

// 测试搜索结果API
const testSearchResultsAPI = async () => {
  try {
    const response = await http.request("get", "/api/search/results", {
      params: { page: 0, size: 10 }
    });

    addTestResult({
      name: "搜索结果API测试",
      success: true,
      message: "请求成功，返回搜索结果数据",
      data: response
    });

    ElMessage.success("搜索结果API测试通过");
  } catch (error: any) {
    addTestResult({
      name: "搜索结果API测试",
      success: false,
      message: `请求失败: ${error.message || "未知错误"}`,
      data: error.response?.data
    });

    ElMessage.error("搜索结果API测试失败");
  }
};

// 测试系统API
const testSystemAPI = async () => {
  try {
    const response = await http.request("get", "/api/start", {
      params: { id: "test-123" }
    });

    addTestResult({
      name: "系统API测试",
      success: true,
      message: "请求成功，系统API响应正常",
      data: response
    });

    ElMessage.success("系统API测试通过");
  } catch (error: any) {
    addTestResult({
      name: "系统API测试",
      success: false,
      message: `请求失败: ${error.message || "未知错误"}`,
      data: error.response?.data
    });

    ElMessage.error("系统API测试失败");
  }
};

// 运行所有测试
const runAllTests = async () => {
  loading.value = true;
  clearResults();

  try {
    await testTasksAPI();
    await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms

    await testSearchResultsAPI();
    await new Promise(resolve => setTimeout(resolve, 500));

    await testSystemAPI();

    ElMessage.success("所有API测试完成");
  } catch (error) {
    ElMessage.error("测试过程中出现错误");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.api-test-container {
  padding: 20px;
}

.test-card {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 20px;
}

.loading {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 20px 0;
  color: #409eff;
}

.test-result {
  margin-bottom: 15px;
}

.result-data {
  margin-top: 10px;
}

.result-data pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style>
