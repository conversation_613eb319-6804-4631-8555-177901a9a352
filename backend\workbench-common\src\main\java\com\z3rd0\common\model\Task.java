package com.z3rd0.common.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "tasks")
@JsonIgnoreProperties(value = {"executionStatusDisplayName", "statusDisplayName", "executedToday"}, ignoreUnknown = true)
public class Task {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "task_name", nullable = false)
    private String name;
    @Column(columnDefinition = "TEXT")
    private String rule;
    @Column(name = "filter_range")
    private String filterRange; // 实际执行时的数据筛选范围
    @Column(name = "time_range", length = 100)
    private String timeRange; // 任务时间范围参数
    @Column(name = "priority", columnDefinition = "INT DEFAULT 5")
    private Integer priority = 5; // 任务优先级：1-10，数字越小优先级越高，默认为5
    @Column(name = "executed_at")
    private LocalDateTime executedAt; // 任务执行时间
    @Column(name = "status")
    private String status;
    @Transient
    private com.z3rd0.common.model.TaskState taskState;
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (status == null) {
            setTaskState(com.z3rd0.common.model.TaskState.PENDING);
        }
        if (retryCount == null) {
            retryCount = 0;
        }
        if (priority == null) {
            priority = 5; // 默认优先级
        }
    }
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    public com.z3rd0.common.model.TaskState getTaskState() {
        if (taskState == null && status != null) {
            taskState = com.z3rd0.common.model.TaskState.fromString(status);
        }
        return taskState != null ? taskState : com.z3rd0.common.model.TaskState.PENDING;
    }
    public void setTaskState(com.z3rd0.common.model.TaskState taskState) {
        this.taskState = taskState;
        this.status = taskState.name();
        if (com.z3rd0.common.model.TaskState.COMPLETED == taskState && completedAt == null) {
            completedAt = LocalDateTime.now();
        }
    }
    public com.z3rd0.common.model.TaskState moveToNextState() {
        com.z3rd0.common.model.TaskState currentState = getTaskState();
        com.z3rd0.common.model.TaskState nextState = currentState.next();
        setTaskState(nextState);
        return nextState;
    }
    public void markAsFailed(String errorMsg) {
        setTaskState(com.z3rd0.common.model.TaskState.FAILED);
        this.errorMessage = errorMsg;
    }

    public void markAsSkipped(String skipReason) {
        setTaskState(com.z3rd0.common.model.TaskState.SKIPPED);
        this.errorMessage = skipReason;
        this.executedAt = LocalDateTime.now();
    }
    @JsonIgnore
    public String getStatusDisplayName() {
        return getTaskState().getDisplayName();
    }

    /**
     * 判断任务今天是否已执行
     * @return true如果今天已执行，false如果今天未执行
     */
    @JsonIgnore
    public boolean isExecutedToday() {
        if (executedAt == null) {
            return false;
        }
        LocalDateTime today = LocalDateTime.now();
        return executedAt.toLocalDate().equals(today.toLocalDate());
    }

    /**
     * 获取基于执行状态的显示状态
     * @return 状态显示名称
     */
    @JsonIgnore
    public String getExecutionStatusDisplayName() {
        return isExecutedToday() ? "已完成" : "未完成";
    }

    /**
     * 更新任务执行信息
     * @param filterRange 筛选范围
     */
    public void updateExecutionInfo(String filterRange) {
        this.filterRange = filterRange;
        this.executedAt = LocalDateTime.now();
    }

    // Priority字段的Getter和Setter方法
    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
}