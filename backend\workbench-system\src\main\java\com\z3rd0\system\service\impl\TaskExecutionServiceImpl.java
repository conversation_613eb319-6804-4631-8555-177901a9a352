package com.z3rd0.system.service.impl;

import com.z3rd0.common.model.TaskExecutionDetail;
import com.z3rd0.system.repository.TaskExecutionDetailRepository;
import com.z3rd0.system.service.TaskExecutionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 任务执行详情服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskExecutionServiceImpl implements TaskExecutionService {

    private final TaskExecutionDetailRepository taskExecutionDetailRepository;

    @Override
    public Page<TaskExecutionDetail> findByConditions(String taskName, String startDate, String endDate, Pageable pageable) {
        try {
            if (taskName != null && !taskName.trim().isEmpty()) {
                if (startDate != null && endDate != null) {
                    // 按任务名称和时间范围查询
                    LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
                    LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
                    return taskExecutionDetailRepository.findByTaskNameAndStartTimeBetweenOrderByStartTimeDesc(
                            taskName.trim(), start, end, pageable);
                } else {
                    // 只按任务名称查询
                    return taskExecutionDetailRepository.findByTaskNameOrderByStartTimeDesc(taskName.trim(), pageable);
                }
            } else if (startDate != null && endDate != null) {
                // 只按时间范围查询 - 需要添加分页支持的方法
                LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
                LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
                // 由于Repository中没有分页版本的方法，我们需要先添加
                return taskExecutionDetailRepository.findByStartTimeBetweenOrderByStartTimeDesc(start, end, pageable);
            } else {
                // 查询所有，按创建时间降序
                return taskExecutionDetailRepository.findAll(pageable);
            }
        } catch (Exception e) {
            log.error("分页查询任务执行详情失败: taskName={}, startDate={}, endDate={}", 
                     taskName, startDate, endDate, e);
            throw new RuntimeException("查询任务执行详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TaskExecutionDetail> findByConditions(String taskName, String startDate, String endDate) {
        try {
            if (taskName != null && !taskName.trim().isEmpty()) {
                if (startDate != null && endDate != null) {
                    // 按任务名称和时间范围查询
                    LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
                    LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
                    return taskExecutionDetailRepository.findByTaskNameAndStartTimeBetweenOrderByStartTimeDesc(
                            taskName.trim(), start, end);
                } else {
                    // 只按任务名称查询
                    return taskExecutionDetailRepository.findByTaskNameOrderByStartTimeDesc(taskName.trim());
                }
            } else if (startDate != null && endDate != null) {
                // 只按时间范围查询
                LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
                LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
                return taskExecutionDetailRepository.findByStartTimeBetweenOrderByStartTimeDesc(start, end);
            } else {
                // 查询最近30天的数据，限制100条
                List<TaskExecutionDetail> details = taskExecutionDetailRepository.findRecentExecutions(
                        LocalDateTime.now().minusDays(30));
                if (details.size() > 100) {
                    return details.subList(0, 100);
                }
                return details;
            }
        } catch (Exception e) {
            log.error("查询任务执行详情失败: taskName={}, startDate={}, endDate={}", 
                     taskName, startDate, endDate, e);
            throw new RuntimeException("查询任务执行详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getExecutionStats(String taskName) {
        try {
            Map<String, Object> stats = new HashMap<>();

            if (taskName != null && !taskName.trim().isEmpty()) {
                // 特定任务的统计
                log.debug("查询特定任务统计: {}", taskName.trim());
                
                Long executionCount = safeGetExecutionCount(taskName.trim());
                Double avgDuration = safeGetAverageDuration(taskName.trim());
                Long totalValidResults = safeGetTotalValidResults(taskName.trim());

                stats.put("taskName", taskName.trim());
                stats.put("executionCount", executionCount);
                stats.put("averageDurationSeconds", avgDuration != null ? avgDuration.longValue() : 0L);
                stats.put("totalValidResults", totalValidResults);

                // 获取最新执行详情
                TaskExecutionDetail latestExecution = safeGetLatestExecution(taskName.trim());
                if (latestExecution != null) {
                    Map<String, Object> executionInfo = createSafeExecutionInfo(latestExecution);
                    stats.put("lastExecution", executionInfo);
                }

            } else {
                // 全局统计
                log.debug("查询全局统计");
                
                List<String> allTaskNames = safeGetAllTaskNames();
                Long totalExecutions = safeGetTotalExecutions();
                Long recentExecutions = safeGetRecentExecutions();

                stats.put("totalTaskNames", allTaskNames.size());
                stats.put("totalExecutions", totalExecutions);
                stats.put("recentExecutions", recentExecutions);
                stats.put("taskNames", allTaskNames);
            }

            return stats;
        } catch (Exception e) {
            log.error("获取任务执行统计失败: taskName={}", taskName, e);
            throw new RuntimeException("获取任务执行统计失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Optional<TaskExecutionDetail> findById(Long id) {
        return taskExecutionDetailRepository.findById(id);
    }

    @Override
    public Optional<TaskExecutionDetail> findByTaskId(Long taskId) {
        return taskExecutionDetailRepository.findByTaskId(taskId);
    }

    @Override
    public List<String> getAllTaskNames() {
        return safeGetAllTaskNames();
    }

    @Override
    public Optional<TaskExecutionDetail> getLatestExecution(String taskName) {
        TaskExecutionDetail latest = safeGetLatestExecution(taskName);
        return Optional.ofNullable(latest);
    }

    @Override
    public List<TaskExecutionDetail> getRecentExecutions(int days) {
        try {
            LocalDateTime startDate = LocalDateTime.now().minusDays(days);
            return taskExecutionDetailRepository.findRecentExecutions(startDate);
        } catch (Exception e) {
            log.warn("获取最近执行详情失败: days={}", days, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Long countByExecutionStatus(String executionStatus) {
        try {
            return taskExecutionDetailRepository.countByExecutionStatus(executionStatus);
        } catch (Exception e) {
            log.warn("根据执行状态统计失败: executionStatus={}", executionStatus, e);
            return 0L;
        }
    }

    // ==================== 安全的数据库查询方法 ====================

    private Long safeGetExecutionCount(String taskName) {
        try {
            Long count = taskExecutionDetailRepository.countByTaskName(taskName);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.warn("获取执行次数失败: {}", e.getMessage());
            return 0L;
        }
    }

    private Double safeGetAverageDuration(String taskName) {
        try {
            Double avgDuration = taskExecutionDetailRepository.getAverageExecutionDurationByTaskName(taskName);
            return avgDuration != null ? avgDuration : 0.0;
        } catch (Exception e) {
            log.warn("获取平均执行时长失败: {}", e.getMessage());
            return 0.0;
        }
    }

    private Long safeGetTotalValidResults(String taskName) {
        try {
            Long totalResults = taskExecutionDetailRepository.getTotalValidResultsByTaskName(taskName);
            return totalResults != null ? totalResults : 0L;
        } catch (Exception e) {
            log.warn("获取总有效结果数失败: {}", e.getMessage());
            return 0L;
        }
    }

    private TaskExecutionDetail safeGetLatestExecution(String taskName) {
        try {
            Optional<TaskExecutionDetail> latestExecution = 
                taskExecutionDetailRepository.findFirstByTaskNameOrderByStartTimeDesc(taskName);
            return latestExecution.orElse(null);
        } catch (Exception e) {
            log.warn("获取最新执行详情失败: {}", e.getMessage());
            return null;
        }
    }

    private List<String> safeGetAllTaskNames() {
        try {
            List<String> taskNames = taskExecutionDetailRepository.findAllDistinctTaskNames();
            return taskNames != null ? taskNames : new ArrayList<>();
        } catch (Exception e) {
            log.warn("获取所有任务名称失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private Long safeGetTotalExecutions() {
        try {
            Long total = taskExecutionDetailRepository.count();
            return total != null ? total : 0L;
        } catch (Exception e) {
            log.warn("获取总执行次数失败: {}", e.getMessage());
            return 0L;
        }
    }

    private Long safeGetRecentExecutions() {
        try {
            LocalDateTime weekAgo = LocalDateTime.now().minusDays(7);
            LocalDateTime now = LocalDateTime.now();
            Long recent = taskExecutionDetailRepository.countByStartTimeBetween(weekAgo, now);
            return recent != null ? recent : 0L;
        } catch (Exception e) {
            log.warn("获取最近执行次数失败: {}", e.getMessage());
            return 0L;
        }
    }

    private Map<String, Object> createSafeExecutionInfo(TaskExecutionDetail detail) {
        Map<String, Object> info = new HashMap<>();
        
        try {
            info.put("id", detail.getId());
            info.put("taskName", detail.getTaskName());
            info.put("startTime", detail.getStartTime());
            info.put("endTime", detail.getEndTime());
            info.put("executionDurationSeconds", detail.getExecutionDurationSeconds());
            info.put("totalResults", detail.getTotalResults());
            info.put("validResults", detail.getValidResults());
            info.put("duplicateResults", detail.getDuplicateResults());
            info.put("outOfRangeResults", detail.getOutOfRangeResults());
            info.put("executionStatus", detail.getExecutionStatus());
            info.put("lastAssetIp", detail.getLastAssetIp());
            info.put("lastAssetPort", detail.getLastAssetPort());
            info.put("lastAssetDomain", detail.getLastAssetDomain());
            info.put("lastAssetDiscoveryTime", detail.getLastAssetDiscoveryTime());
            info.put("createdAt", detail.getCreatedAt());
            
            // 安全地添加可能为null的字段
            info.put("errorMessage", detail.getErrorMessage());
            info.put("retryCount", detail.getRetryCount() != null ? detail.getRetryCount() : 0);
            info.put("peakMemoryUsageMb", detail.getPeakMemoryUsageMb() != null ? detail.getPeakMemoryUsageMb() : 0L);
            info.put("avgMemoryUsageMb", detail.getAvgMemoryUsageMb() != null ? detail.getAvgMemoryUsageMb() : 0L);
            info.put("browserRestartCount", detail.getBrowserRestartCount() != null ? detail.getBrowserRestartCount() : 0);
            
        } catch (Exception e) {
            log.warn("创建安全执行详情信息失败: {}", e.getMessage());
            // 返回基本信息
            info.put("id", detail.getId());
            info.put("taskName", detail.getTaskName());
            info.put("executionStatus", detail.getExecutionStatus());
        }
        
        return info;
    }
}
