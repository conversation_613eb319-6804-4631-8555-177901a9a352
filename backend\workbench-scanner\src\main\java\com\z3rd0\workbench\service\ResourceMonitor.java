package com.z3rd0.workbench.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 资源监控管理器
 * 监控系统内存、浏览器资源使用情况，并在超限时采取保护措施
 */
@Service
public class ResourceMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(ResourceMonitor.class);
    
    // 内存使用阈值配置
    @Value("${workbench.resource.memory.warning-threshold:0.8}")
    private double memoryWarningThreshold;
    
    @Value("${workbench.resource.memory.critical-threshold:0.9}")
    private double memoryCriticalThreshold;
    
    @Value("${workbench.resource.browser.max-memory-mb:1024}")
    private long browserMaxMemoryMb;
    
    @Value("${workbench.resource.browser.max-runtime-minutes:120}")
    private long browserMaxRuntimeMinutes;
    
    // JVM内存管理Bean
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    
    // 浏览器实例监控信息
    private final ConcurrentMap<String, BrowserResourceInfo> browserResources = new ConcurrentHashMap<>();
    
    /**
     * 检查系统内存使用情况
     * 
     * @return 内存监控结果
     */
    public MemoryMonitorResult checkMemoryUsage() {
        MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
        
        long heapUsed = heapMemory.getUsed();
        long heapMax = heapMemory.getMax();
        long nonHeapUsed = nonHeapMemory.getUsed();
        long nonHeapMax = nonHeapMemory.getMax();
        
        double heapUsageRatio = (double) heapUsed / heapMax;
        double nonHeapUsageRatio = nonHeapMax > 0 ? (double) nonHeapUsed / nonHeapMax : 0;
        
        // 判断内存状态
        MemoryStatus status = MemoryStatus.NORMAL;
        String recommendation = "内存使用正常";
        
        if (heapUsageRatio >= memoryCriticalThreshold) {
            status = MemoryStatus.CRITICAL;
            recommendation = "内存使用严重超限，建议立即重启浏览器并执行GC";
        } else if (heapUsageRatio >= memoryWarningThreshold) {
            status = MemoryStatus.WARNING;
            recommendation = "内存使用较高，建议执行GC或重启浏览器";
        }
        
        MemoryMonitorResult result = new MemoryMonitorResult(
            heapUsed, heapMax, heapUsageRatio,
            nonHeapUsed, nonHeapMax, nonHeapUsageRatio,
            status, recommendation
        );
        
        if (status != MemoryStatus.NORMAL) {
            logger.warn("内存使用警告: 堆内存使用率={:.2f}%, 非堆内存使用率={:.2f}%, 状态={}, 建议={}",
                       heapUsageRatio * 100, nonHeapUsageRatio * 100, status, recommendation);
        }
        
        return result;
    }
    
    /**
     * 注册浏览器实例
     * 
     * @param browserId 浏览器ID
     * @param taskName 关联的任务名称
     */
    public void registerBrowser(String browserId, String taskName) {
        BrowserResourceInfo info = new BrowserResourceInfo(browserId, taskName);
        browserResources.put(browserId, info);
        logger.info("注册浏览器实例: ID={}, 任务={}", browserId, taskName);
    }
    
    /**
     * 注销浏览器实例
     * 
     * @param browserId 浏览器ID
     */
    public void unregisterBrowser(String browserId) {
        BrowserResourceInfo info = browserResources.remove(browserId);
        if (info != null) {
            logger.info("注销浏览器实例: ID={}, 运行时长={}分钟", 
                       browserId, info.getRuntimeMinutes());
        }
    }
    
    /**
     * 更新浏览器内存使用情况
     * 
     * @param browserId 浏览器ID
     * @param memoryUsageMb 内存使用量(MB)
     */
    public void updateBrowserMemory(String browserId, long memoryUsageMb) {
        BrowserResourceInfo info = browserResources.get(browserId);
        if (info != null) {
            info.updateMemoryUsage(memoryUsageMb);
        }
    }
    
    /**
     * 检查浏览器资源使用情况
     * 
     * @return 需要重启的浏览器ID列表
     */
    public BrowserMonitorResult checkBrowserResources() {
        BrowserMonitorResult result = new BrowserMonitorResult();
        
        for (BrowserResourceInfo info : browserResources.values()) {
            boolean needRestart = false;
            String reason = "";
            
            // 检查内存使用
            if (info.getMemoryUsageMb() > browserMaxMemoryMb) {
                needRestart = true;
                reason = String.format("内存使用超限: %dMB > %dMB", 
                                     info.getMemoryUsageMb(), browserMaxMemoryMb);
            }
            
            // 检查运行时长
            if (info.getRuntimeMinutes() > browserMaxRuntimeMinutes) {
                needRestart = true;
                if (!reason.isEmpty()) reason += "; ";
                reason += String.format("运行时长超限: %d分钟 > %d分钟", 
                                      info.getRuntimeMinutes(), browserMaxRuntimeMinutes);
            }
            
            if (needRestart) {
                result.addRestartBrowser(info.getBrowserId(), info.getTaskName(), reason);
                logger.warn("浏览器需要重启: ID={}, 任务={}, 原因={}", 
                           info.getBrowserId(), info.getTaskName(), reason);
            }
        }
        
        return result;
    }
    
    /**
     * 获取资源监控统计信息
     */
    public ResourceStats getResourceStats() {
        MemoryMonitorResult memoryResult = checkMemoryUsage();
        
        int totalBrowsers = browserResources.size();
        long totalBrowserMemory = browserResources.values().stream()
            .mapToLong(BrowserResourceInfo::getMemoryUsageMb)
            .sum();
        
        long avgRuntimeMinutes = browserResources.values().stream()
            .mapToLong(BrowserResourceInfo::getRuntimeMinutes)
            .sum() / Math.max(totalBrowsers, 1);
        
        return new ResourceStats(
            memoryResult.getHeapUsageRatio(),
            memoryResult.getNonHeapUsageRatio(),
            totalBrowsers,
            totalBrowserMemory,
            avgRuntimeMinutes,
            memoryResult.getStatus()
        );
    }
    
    /**
     * 执行垃圾回收
     */
    public void performGarbageCollection() {
        logger.info("执行垃圾回收...");
        long beforeMemory = memoryBean.getHeapMemoryUsage().getUsed();
        
        System.gc();
        
        // 等待GC完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long afterMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long freedMemory = beforeMemory - afterMemory;
        
        logger.info("垃圾回收完成: 释放内存={}MB", freedMemory / 1024 / 1024);
    }
    
    /**
     * 内存状态枚举
     */
    public enum MemoryStatus {
        NORMAL("正常"),
        WARNING("警告"),
        CRITICAL("严重");
        
        private final String displayName;
        
        MemoryStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * 浏览器资源信息
     */
    private static class BrowserResourceInfo {
        private final String browserId;
        private final String taskName;
        private final LocalDateTime startTime;
        private long memoryUsageMb = 0;
        
        public BrowserResourceInfo(String browserId, String taskName) {
            this.browserId = browserId;
            this.taskName = taskName;
            this.startTime = LocalDateTime.now();
        }
        
        public void updateMemoryUsage(long memoryUsageMb) {
            this.memoryUsageMb = memoryUsageMb;
        }
        
        public long getRuntimeMinutes() {
            return java.time.Duration.between(startTime, LocalDateTime.now()).toMinutes();
        }
        
        // Getters
        public String getBrowserId() { return browserId; }
        public String getTaskName() { return taskName; }
        public long getMemoryUsageMb() { return memoryUsageMb; }
        public LocalDateTime getStartTime() { return startTime; }
    }
    
    /**
     * 内存监控结果
     */
    public static class MemoryMonitorResult {
        private final long heapUsed;
        private final long heapMax;
        private final double heapUsageRatio;
        private final long nonHeapUsed;
        private final long nonHeapMax;
        private final double nonHeapUsageRatio;
        private final MemoryStatus status;
        private final String recommendation;
        
        public MemoryMonitorResult(long heapUsed, long heapMax, double heapUsageRatio,
                                  long nonHeapUsed, long nonHeapMax, double nonHeapUsageRatio,
                                  MemoryStatus status, String recommendation) {
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
            this.heapUsageRatio = heapUsageRatio;
            this.nonHeapUsed = nonHeapUsed;
            this.nonHeapMax = nonHeapMax;
            this.nonHeapUsageRatio = nonHeapUsageRatio;
            this.status = status;
            this.recommendation = recommendation;
        }
        
        // Getters
        public long getHeapUsed() { return heapUsed; }
        public long getHeapMax() { return heapMax; }
        public double getHeapUsageRatio() { return heapUsageRatio; }
        public long getNonHeapUsed() { return nonHeapUsed; }
        public long getNonHeapMax() { return nonHeapMax; }
        public double getNonHeapUsageRatio() { return nonHeapUsageRatio; }
        public MemoryStatus getStatus() { return status; }
        public String getRecommendation() { return recommendation; }
    }

    /**
     * 浏览器监控结果
     */
    public static class BrowserMonitorResult {
        private final java.util.List<BrowserRestartInfo> restartBrowsers = new java.util.ArrayList<>();

        public void addRestartBrowser(String browserId, String taskName, String reason) {
            restartBrowsers.add(new BrowserRestartInfo(browserId, taskName, reason));
        }

        public java.util.List<BrowserRestartInfo> getRestartBrowsers() {
            return restartBrowsers;
        }

        public boolean hasRestartNeeded() {
            return !restartBrowsers.isEmpty();
        }

        public static class BrowserRestartInfo {
            private final String browserId;
            private final String taskName;
            private final String reason;

            public BrowserRestartInfo(String browserId, String taskName, String reason) {
                this.browserId = browserId;
                this.taskName = taskName;
                this.reason = reason;
            }

            // Getters
            public String getBrowserId() { return browserId; }
            public String getTaskName() { return taskName; }
            public String getReason() { return reason; }
        }
    }

    /**
     * 资源统计信息
     */
    public static class ResourceStats {
        private final double heapUsageRatio;
        private final double nonHeapUsageRatio;
        private final int totalBrowsers;
        private final long totalBrowserMemoryMb;
        private final long avgRuntimeMinutes;
        private final MemoryStatus memoryStatus;

        public ResourceStats(double heapUsageRatio, double nonHeapUsageRatio,
                           int totalBrowsers, long totalBrowserMemoryMb,
                           long avgRuntimeMinutes, MemoryStatus memoryStatus) {
            this.heapUsageRatio = heapUsageRatio;
            this.nonHeapUsageRatio = nonHeapUsageRatio;
            this.totalBrowsers = totalBrowsers;
            this.totalBrowserMemoryMb = totalBrowserMemoryMb;
            this.avgRuntimeMinutes = avgRuntimeMinutes;
            this.memoryStatus = memoryStatus;
        }

        // Getters
        public double getHeapUsageRatio() { return heapUsageRatio; }
        public double getNonHeapUsageRatio() { return nonHeapUsageRatio; }
        public int getTotalBrowsers() { return totalBrowsers; }
        public long getTotalBrowserMemoryMb() { return totalBrowserMemoryMb; }
        public long getAvgRuntimeMinutes() { return avgRuntimeMinutes; }
        public MemoryStatus getMemoryStatus() { return memoryStatus; }
    }
}
