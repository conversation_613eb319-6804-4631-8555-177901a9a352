package com.z3rd0.workbench.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 验证码配置类
 * 用于配置验证码检测和处理相关参数
 */
@Configuration
@ConfigurationProperties(prefix = "workbench.captcha")
public class CaptchaConfig {
    
    /**
     * 是否启用验证码检测
     */
    private boolean enabled = true;
    
    /**
     * 验证码等待超时时间（秒）
     */
    private int waitTimeout = 300;
    
    /**
     * 检查间隔时间（秒）
     */
    private int checkInterval = 2;
    
    /**
     * 是否显示等待进度
     */
    private boolean showProgress = true;
    
    /**
     * 进度显示间隔（秒）
     */
    private int progressInterval = 10;
    
    /**
     * 自定义验证码选择器（DOM检测方式，备用）
     */
    private String[] customSelectors = {};

    /**
     * 是否使用网络请求检测验证码
     */
    private boolean useRequestDetection = true;

    /**
     * 验证码请求URL关键词
     */
    private String captchaRequestKeyword = "captcha.bpd.360.cn";

    /**
     * 验证码验证完成请求URL关键词
     */
    private String captchaVerifyKeyword = "captcha.bpd.360.cn/v1/verify";

    /**
     * 点击登录后等待网络请求的时间（毫秒）
     */
    private int requestDetectionDelay = 5000;
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public int getWaitTimeout() {
        return waitTimeout;
    }
    
    public void setWaitTimeout(int waitTimeout) {
        this.waitTimeout = waitTimeout;
    }
    
    public int getCheckInterval() {
        return checkInterval;
    }
    
    public void setCheckInterval(int checkInterval) {
        this.checkInterval = checkInterval;
    }
    
    public boolean isShowProgress() {
        return showProgress;
    }
    
    public void setShowProgress(boolean showProgress) {
        this.showProgress = showProgress;
    }
    
    public int getProgressInterval() {
        return progressInterval;
    }
    
    public void setProgressInterval(int progressInterval) {
        this.progressInterval = progressInterval;
    }
    
    public String[] getCustomSelectors() {
        return customSelectors;
    }

    public void setCustomSelectors(String[] customSelectors) {
        this.customSelectors = customSelectors;
    }

    public boolean isUseRequestDetection() {
        return useRequestDetection;
    }

    public void setUseRequestDetection(boolean useRequestDetection) {
        this.useRequestDetection = useRequestDetection;
    }

    public String getCaptchaRequestKeyword() {
        return captchaRequestKeyword;
    }

    public void setCaptchaRequestKeyword(String captchaRequestKeyword) {
        this.captchaRequestKeyword = captchaRequestKeyword;
    }

    public String getCaptchaVerifyKeyword() {
        return captchaVerifyKeyword;
    }

    public void setCaptchaVerifyKeyword(String captchaVerifyKeyword) {
        this.captchaVerifyKeyword = captchaVerifyKeyword;
    }

    public int getRequestDetectionDelay() {
        return requestDetectionDelay;
    }

    public void setRequestDetectionDelay(int requestDetectionDelay) {
        this.requestDetectionDelay = requestDetectionDelay;
    }
}
