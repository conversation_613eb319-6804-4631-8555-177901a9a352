# 开发规范和规则

- RabbitMQ配置冲突解决方案：当多个模块都需要RabbitMQ配置时，应该重命名配置类避免Bean冲突，如SystemRabbitMQConfig和RabbitMQConfig，保持各模块配置的独立性和职责分离
- 微服务模块解耦方案：System模块不应直接依赖Scanner模块，通过RabbitMQ消息队列通信。ComponentScan只扫描自己模块的包，避免Bean冲突。配置类使用模块前缀命名避免冲突
- workbench-scanner模块数据库配置问题：缺少ip_assets表导致启动失败，需要将ddl-auto从validate改为update以允许自动创建表
- Vue Router错误修复：为IP详情页面路由添加了参数验证，防止Missing required param "ip"错误，包括路由守卫和组件内参数验证
- Vue Router深度修复：增强了路由守卫、组件参数验证、全局错误处理，解决Missing required param "ip"错误，包括多重验证条件和用户友好的错误处理
- Vue Router模板渲染阶段错误深度修复：修复了面包屑导航、标签页组件、多标签存储中的参数验证，解决useLink和setupStatefulComponent阶段的Missing required param "ip"错误
- Vue Router全面框架级修复：修复了Pure Admin框架的导航组件、多标签页、面包屑导航，添加了模板级条件渲染、DOM生命周期管理、全局错误处理，解决了useLink和setupStatefulComponent阶段的错误以及parentNode DOM问题
- Vue Router最终修复：添加了全局前置守卫进行参数验证，修复了SidebarLinkItem组件的模板条件渲染，优化了DOM生命周期错误处理，成功解决了Missing required param "ip"错误并实现了自动重定向
- Vue Router错误最终解决方案：通过设置showLink:false隐藏动态路由在侧边栏菜单中的显示，添加路由存储清理工具，优化SidebarLinkItem组件条件渲染，完全消除了Missing required param "ip"和parentNode错误
- Spring Boot路由问题修复：当出现NoResourceFoundException且请求被当作静态资源处理时，通常是缺少对应的Controller端点。需要在相应的Controller中添加@GetMapping注解的方法来处理该路径。对于/api/system/status端点，已在SystemController中添加getSystemStatus()方法，提供系统状态、健康检查、JVM信息和内存监控功能。
- Spring Boot CORS和Favicon错误修复：1) favicon.ico缺失导致NoResourceFoundException，通过在Controller中添加@GetMapping("/favicon.ico")返回204状态码解决；2) CORS配置冲突：当allowCredentials=true时不能使用@CrossOrigin(origins="*")，需要改为@CrossOrigin(originPatterns="*", allowCredentials="true")或在WebMvcConfig中统一使用allowedOriginPatterns。
- 前端侧边栏路由重构：1) 任务管理改为任务列表（顶级菜单）；2) IP资产管理下的IP列表提升为顶级IP列表菜单；3) 结果页面重构为独立的资产列表和数据统计两个顶级菜单；4) 调整了enums.ts中的排序，新增ip、asset、statistics枚举；5) 最终顺序：首页(0)→任务列表(1)→IP列表(2)→资产列表(3)→数据统计(4)→示例展示(5)。
- Vue Router Layout组件问题修复：在Pure Admin框架中，所有顶级路由都必须使用Layout组件包装才能在侧边栏正确显示。修复了statistics.ts、task.ts、result.ts等路由缺少Layout组件的问题。同时优化了图标选择：任务列表(ep:list)、IP列表(ep:connection)、资产列表(ep:files)、数据统计(ep:data-analysis)、示例展示(ep:grid)。
- 微服务API代理模式：当前端需要调用不同模块的API时，通过workbench-system模块进行代理转发。创建了ScannerProxyController将/api/scanner/*请求代理到workbench-scanner模块(38888端口)，并实现了降级机制：当scanner模块不可用时返回默认状态。同时在scanner模块创建了ScannerStatusController提供详细的服务状态信息。
- 前端侧边栏显示异常修复方案：1) 侧边栏组件本身正常，问题在于后端API服务未启动导致500错误；2) 已添加API容错处理，包括welcome页面的系统状态、Scanner状态、任务统计、资产统计API调用的降级处理；3) 已安装全局API拦截器，统一处理网络错误和超时；4) 已添加服务状态检查器和状态指示器组件，在应用启动时检查后端服务可用性；5) 已优化任务页面的状态检查函数，避免网络错误时的界面异常。
- 前端首页布局重构：1) 将2x2四方格布局改为2x3六方格布局（2行3列）；2) 移除了会遮挡侧边栏的右侧边栏模块（系统详情、Scanner详情、快速操作卡片）；3) 将原右侧边栏内容整合到第5格（系统详情）和第6格（快速操作）中；4) 更新CSS样式：grid-template-columns改为1fr 1fr 1fr，添加了detail-grid和action-grid样式；5) 优化响应式布局：平板端显示2列，手机端显示1列；6) 添加了新的图标样式detail-icon和action-icon。
- 前端首页布局优化：1) 将2行3列(2x3)六方格布局改为3行2列(3x2)布局；2) 更新CSS：grid-template-columns改为1fr 1fr，grid-template-rows改为1fr 1fr 1fr；3) 调整容器高度为720px，卡片最小高度为220px；4) 优化响应式：桌面端3行2列，平板端2行3列，手机端单列；5) 改善卡片内容对齐：添加flex布局，card-content垂直居中，优化detail-grid和action-grid间距；6) 提升视觉效果：减少内边距，优化按钮高度，增强整体紧凑性。
- 前端首页布局重构为四分屏：1) 将3行2列(6个卡片)改为2行2列(4个卡片)布局；2) 删除第5个(系统详情)和第6个(快速操作)卡片；3) 重新设计System卡片：显示JVM版本、厂商、运行时长、内存使用率、堆内存使用/最大值等6个指标；4) 重新设计Scanner卡片：显示活跃任务、队列大小、浏览器实例、内存占用、最后任务、处理速度等6个指标；5) 更新CSS：容器高度改为600px，添加system-metrics和scanner-metrics样式(3行2列网格)；6) 清理无用样式：删除detail-grid、action-grid、detail-icon、action-icon等样式；7) 响应式布局：桌面端和平板端都是2x2，手机端单列。
- ElBacktop组件DOM错误修复：当ElBacktop组件报错"target does not exist: .app-main .el-scrollbar__wrap"时，原因是组件在DOM完全渲染前就尝试查找target元素。解决方案：1) 添加v-if="showBackTop"条件渲染；2) 在onMounted中使用nextTick和setTimeout延迟100ms显示组件；3) 确保在layout/components/lay-content/index.vue和layout/index.vue两个文件中都进行修复；4) 添加visibility-height、right、bottom等配置参数提升用户体验。
- RabbitMQ队列统计数据一致性修复：首页统计中的"等待中"任务数量现在显示RabbitMQ队列的真实长度而不是数据库状态统计。实现了RabbitMQMonitorService服务查询队列长度，修改了DashboardController使用真实队列数据，前端添加了队列统计显示和颜色编码（0个绿色，1-5个蓝色，6-20个橙色，20+个红色）。
