<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>任务管理</span>
          <div class="button-group">
            <el-button
              type="primary"
              :loading="startAllLoading"
              @click="handleStartAll"
            >
              <IconifyIconOffline icon="ep:video-play" class="mr-1" />
              启动所有任务
            </el-button>
            <el-button type="success" @click="handleAdd">
              <IconifyIconOffline icon="ep:plus" class="mr-1" />
              新增任务
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="searchForm" inline>
        <el-form-item label="任务名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入任务名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="未扫描" value="PENDING" />
            <el-option label="待扫描" value="QUEUED" />
            <el-option label="扫描中" value="PROCESSING" />
            <el-option label="已扫描" value="COMPLETED" />
            <el-option label="扫描出错" value="FAILED" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <IconifyIconOffline icon="ep:search" class="mr-1" />
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 使用优化后的任务表格组件 -->
    <el-card class="box-card mt-4" shadow="never">
      <OptimizedTaskTable
        :tasks="tasks"
        :loading="loading"
        :pagination="pagination"
        :starting-tasks="startingTasks"
        :task-running="taskRunning"
        @selection-change="handleSelectionChange"
        @start="handleStart"
        @edit="handleEdit"
        @delete="handleDelete"
        @duplicate="handleDuplicate"
        @stop="handleStop"
        @view-logs="handleViewLogs"
        @view-details="handleViewDetails"
        @batch-start="handleBatchStart"
        @batch-delete="handleBatchDelete"
        @refresh="loadTasks"
        @create="handleCreate"
        @size-change="handleSizeChange"
        @page-change="handlePageChange"
      />
    </el-card>

    <!-- 编辑/新增对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editTask.id ? '编辑任务' : '新增任务'"
      width="700px"
      @close="resetEditForm"
    >
      <!-- 只有新增任务时才显示选项卡 -->
      <el-tabs v-if="!editTask.id" v-model="activeTab" type="border-card">
        <!-- 单个任务创建 -->
        <el-tab-pane label="单个任务" name="single">
          <el-form
            ref="editFormRef"
            :model="editTask"
            :rules="editRules"
            label-width="100px"
          >
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="editTask.name" placeholder="请输入任务名称" />
            </el-form-item>
            <el-form-item label="搜索规则" prop="rule">
              <el-input
                v-model="editTask.rule"
                type="textarea"
                :rows="4"
                placeholder="请输入搜索规则"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 批量任务创建 -->
        <el-tab-pane label="批量生成" name="batch">
          <el-form
            ref="batchFormRef"
            :model="batchForm"
            :rules="batchRules"
            label-width="100px"
          >
            <el-form-item label="任务前缀" prop="namePrefix">
              <el-input
                v-model="batchForm.namePrefix"
                placeholder="可选，任务名称前缀"
                clearable
              />
              <div class="form-tip">最终任务名称为：前缀 + 关键字</div>
            </el-form-item>
            <el-form-item label="关键字列表" prop="keywordsText">
              <el-input
                v-model="batchForm.keywordsText"
                type="textarea"
                :rows="8"
                placeholder="请输入关键字，每行一个&#10;例如：&#10;国网&#10;电力&#10;能源"
              />
              <div class="form-tip">每行一个关键字，将为每个关键字生成一个任务</div>
            </el-form-item>
            <el-form-item label="任务优先级">
              <el-select v-model="batchForm.priority" placeholder="选择优先级">
                <el-option label="最高 (1)" :value="1" />
                <el-option label="高 (2)" :value="2" />
                <el-option label="较高 (3)" :value="3" />
                <el-option label="中等 (4)" :value="4" />
                <el-option label="普通 (5)" :value="5" />
                <el-option label="较低 (6)" :value="6" />
                <el-option label="低 (7)" :value="7" />
                <el-option label="最低 (8)" :value="8" />
              </el-select>
            </el-form-item>
            <el-form-item label="查询模板">
              <el-input
                v-model="batchForm.queryTemplate"
                type="textarea"
                :rows="3"
                placeholder="查询模板，{关键字} 将被替换为实际关键字"
                readonly
              />
              <div class="form-tip">固定模板，{关键字} 部分将被替换为输入的关键字</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <!-- 编辑任务时的表单 -->
      <el-form
        v-else
        ref="editFormRef"
        :model="editTask"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="editTask.name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="搜索规则" prop="rule">
          <el-input
            v-model="editTask.rule"
            type="textarea"
            :rows="4"
            placeholder="请输入搜索规则"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="saveLoading"
            @click="activeTab === 'batch' && !editTask.id ? handleBatchSave() : handleSave()"
          >
            {{ activeTab === 'batch' && !editTask.id ? '批量创建' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 时间范围选择对话框 -->
    <el-dialog
      v-model="timeRangeDialogVisible"
      :title="timeRangeDialogTitle"
      width="500px"
    >
      <el-form label-width="100px">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="selectedTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="timeRangeShortcuts"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="timeRangeDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="timeRangeLoading"
            @click="confirmTimeRange"
          >
            确定启动
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务执行详情对话框 -->
    <el-dialog
      v-model="detailsDialogVisible"
      title="任务执行详情"
      width="1000px"
      @close="resetDetailsDialog"
    >
      <div v-loading="detailsLoading">
        <!-- 任务基本信息 -->
        <el-card v-if="currentTaskDetails" class="mb-4" shadow="never">
          <template #header>
            <div class="card-header">
              <span>任务信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务名称">
              {{ currentTaskDetails.name }}
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag
                :type="getExecutionStatusTagType(currentTaskDetails.status)"
              >
                {{ getExecutionStatusText(currentTaskDetails.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="搜索规则">
              <el-text class="rule-text">{{ currentTaskDetails.rule }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(currentTaskDetails.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 执行统计信息 -->
        <el-card v-if="executionStats" class="mb-4" shadow="never">
          <template #header>
            <div class="card-header">
              <span>执行统计</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic
                title="执行次数"
                :value="executionStats.executionCount || 0"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="平均耗时(秒)"
                :value="executionStats.averageDurationSeconds || 0"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="总有效结果"
                :value="executionStats.totalValidResults || 0"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="最后执行"
                :value="
                  executionStats.lastExecution?.startTime
                    ? new Date(executionStats.lastExecution.startTime).getTime()
                    : 0
                "
                :formatter="
                  (value: number) =>
                    value ? new Date(value).toLocaleString('zh-CN') : '-'
                "
              />
            </el-col>
          </el-row>
        </el-card>

        <!-- 执行历史记录 -->
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>执行历史</span>
            </div>
          </template>
          <el-table :data="executionDetails" border stripe>
            <el-table-column prop="startTime" label="开始时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.startTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="结束时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.endTime) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="executionDurationSeconds"
              label="耗时(秒)"
              width="100"
              align="center"
            />
            <el-table-column
              prop="totalResults"
              label="总结果"
              width="80"
              align="center"
            />
            <el-table-column
              prop="validResults"
              label="有效结果"
              width="80"
              align="center"
            />
            <el-table-column
              prop="duplicateResults"
              label="重复结果"
              width="80"
              align="center"
            />
            <el-table-column
              prop="outOfRangeResults"
              label="超出范围"
              width="80"
              align="center"
            />
            <el-table-column label="最后资产信息" min-width="200">
              <template #default="{ row }">
                <div v-if="row.lastAssetIp">
                  <div><strong>IP:</strong> {{ row.lastAssetIp }}</div>
                  <div v-if="row.lastAssetPort">
                    <strong>端口:</strong> {{ row.lastAssetPort }}
                  </div>
                  <div v-if="row.lastAssetDomain">
                    <strong>域名:</strong> {{ row.lastAssetDomain }}
                  </div>
                  <div v-if="row.lastAssetDiscoveryTime">
                    <strong>发现时间:</strong> {{ row.lastAssetDiscoveryTime }}
                  </div>
                </div>
                <span v-else class="text-gray-400">无资产信息</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="executionStatus"
              label="状态"
              width="80"
              align="center"
            >
              <template #default="{ row }">
                <el-tag
                  :type="
                    row.executionStatus === 'COMPLETED' ? 'success' : 'danger'
                  "
                  size="small"
                >
                  {{ row.executionStatus === "COMPLETED" ? "成功" : "失败" }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <div v-if="executionDetails.length === 0" class="empty-state">
            <el-empty description="暂无执行记录" />
          </div>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { IconifyIconOffline } from "@/components/ReIcon";
import {
  apiPageRequest,
  apiRequest,
  buildQueryString,
  getFriendlyErrorMessage
} from "@/utils/apiHelper";
import {
  getTaskExecutionDetails,
  getTaskExecutionStats,
  batchCreateTasks,
  type TaskExecutionDetail,
  type TaskExecutionStats,
  type PageResponse,
  type BatchTaskCreateRequest,
  type BatchOperationResult
} from "@/api/task";
import OptimizedTaskTable from "./optimized-table.vue";

// 类型定义
interface Task {
  id?: number;
  name: string;
  rule: string;
  filterRange?: string;
  executedAt?: string;
  status?: string;
  retryCount?: number;
  createdAt?: string;
  updatedAt?: string;
  completedAt?: string;
  errorMessage?: string;
}

// 响应式数据
const loading = ref(false);
const startAllLoading = ref(false);
const saveLoading = ref(false);
const dialogVisible = ref(false);
const editFormRef = ref();
const startingTasks = ref(new Set<number>());
const taskRunning = ref(false); // 添加任务运行状态管理

// 时间范围选择相关
const timeRangeDialogVisible = ref(false);
const timeRangeDialogTitle = ref("");
const timeRangeLoading = ref(false);
const selectedTimeRange = ref<string[]>([]);
const currentTaskId = ref<number | null>(null);
const isStartAll = ref(false);

const tasks = ref<Task[]>([]);
const selectedTasks = ref<Task[]>([]);

// 执行详情相关
const detailsDialogVisible = ref(false);
const detailsLoading = ref(false);
const currentTaskDetails = ref<any>(null);
const executionDetails = ref<any[]>([]);
const executionStats = ref<any>(null);

const searchForm = reactive({
  name: "",
  status: "",
  timeRange: [] as string[]
});

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

const editTask = ref<Task>({
  name: "",
  rule: ""
});

const editRules = {
  name: [{ required: true, message: "请输入任务名称", trigger: "blur" }],
  rule: [{ required: true, message: "请输入搜索规则", trigger: "blur" }]
};

// 批量创建相关
const activeTab = ref("single");
const batchFormRef = ref();
const batchForm = reactive({
  namePrefix: "",
  keywordsText: "",
  priority: 5,
  queryTemplate: 'title: "{关键字}" AND country: "China" AND -province_cn: "香港" AND -title: "首页" AND -favicon: "de84ef1e9248d5ce5847f451ea07d3c0" AND -favicon: "4214c76ef244357607f92bc658a03d65" AND -favicon: "de84ef1e9248d5ce5847f451ea07d3c0" AND -favicon: "0f86df4b41fc0ff3f9cede3a01316e81"'
});

const batchRules = {
  keywordsText: [
    { required: true, message: "请输入关键字列表", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (!value || !value.trim()) {
          callback(new Error("请输入关键字列表"));
          return;
        }
        const keywords = value.trim().split('\n').filter(k => k.trim());
        if (keywords.length === 0) {
          callback(new Error("请至少输入一个关键字"));
          return;
        }
        if (keywords.length > 100) {
          callback(new Error("关键字数量不能超过100个"));
          return;
        }
        callback();
      },
      trigger: "blur"
    }
  ]
};

// 时间范围快捷选项
const timeRangeShortcuts = [
  {
    text: "当天",
    value: () => {
      const today = new Date();
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        0,
        0,
        0
      );
      const endOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        23,
        59,
        59
      );
      return [startOfDay, endOfDay];
    }
  },
  {
    text: "最近三天",
    value: () => {
      const now = new Date();
      const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
      const startOfThreeDaysAgo = new Date(
        threeDaysAgo.getFullYear(),
        threeDaysAgo.getMonth(),
        threeDaysAgo.getDate(),
        0,
        0,
        0
      );
      return [startOfThreeDaysAgo, now];
    }
  },
  {
    text: "最近一周",
    value: () => {
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const startOfWeekAgo = new Date(
        oneWeekAgo.getFullYear(),
        oneWeekAgo.getMonth(),
        oneWeekAgo.getDate(),
        0,
        0,
        0
      );
      return [startOfWeekAgo, now];
    }
  },
  {
    text: "最近一个月",
    value: () => {
      const now = new Date();
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const startOfMonthAgo = new Date(
        oneMonthAgo.getFullYear(),
        oneMonthAgo.getMonth(),
        oneMonthAgo.getDate(),
        0,
        0,
        0
      );
      return [startOfMonthAgo, now];
    }
  }
];

// 方法定义
async function fetchTasks() {
  loading.value = true;
  try {
    const queryParams = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      ...(searchForm.name && { name: searchForm.name }),
      ...(searchForm.status && { status: searchForm.status }),
      ...(searchForm.timeRange &&
        searchForm.timeRange.length === 2 && {
          startTime: searchForm.timeRange[0],
          endTime: searchForm.timeRange[1]
        })
    };

    const queryString = buildQueryString(queryParams);
    const result = await apiPageRequest<Task>(`/api/tasks${queryString}`);

    if (result.success && result.data) {
      tasks.value = result.data.list || [];
      pagination.total = result.data.total || 0;
    } else {
      ElMessage.error(
        getFriendlyErrorMessage(result.errorCode, result.message)
      );
      tasks.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    ElMessage.error("获取任务列表失败");
    console.error("获取任务列表失败:", error);
  } finally {
    loading.value = false;
  }
}

// 完善的五状态系统
function getStatusTagType(task: Task) {
  // 状态优先级：扫描中 > 已扫描 > 待扫描 > 扫描出错 > 未扫描
  switch (task.status) {
    case "PROCESSING":
      return "warning"; // 扫描中 - 橙色
    case "COMPLETED":
      if (isExecutedToday(task)) {
        return "success"; // 已扫描 - 绿色
      }
      return "info"; // 历史完成 - 蓝色
    case "QUEUED":
      return "primary"; // 待扫描 - 蓝色
    case "FAILED":
      return "danger"; // 扫描出错 - 红色
    case "PENDING":
    default:
      return "info"; // 未扫描 - 灰色
  }
}

function getStatusDisplayName(task: Task) {
  // 状态优先级：扫描中 > 已扫描 > 待扫描 > 扫描出错 > 未扫描
  switch (task.status) {
    case "PROCESSING":
      return "扫描中";
    case "COMPLETED":
      if (isExecutedToday(task)) {
        return "已扫描";
      }
      return "历史完成";
    case "QUEUED":
      return "待扫描";
    case "FAILED":
      return "扫描出错";
    case "PENDING":
    default:
      return "未扫描";
  }
}

// 判断任务是否正在运行
function isTaskRunning(task: Task) {
  // 检查全局运行状态或任务特定状态
  return taskRunning.value || task.status === "PROCESSING";
}

function isExecutedToday(task: Task) {
  if (!task.executedAt) {
    return false;
  }
  const today = new Date().toDateString();
  const executedDate = new Date(task.executedAt).toDateString();
  return today === executedDate;
}

// 时间格式化函数
function formatDateTime(dateTime: string | undefined) {
  if (!dateTime) return "-";
  return new Date(dateTime).toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
}

function formatRelativeTime(dateTime: string) {
  if (!dateTime) return "";

  const now = new Date();
  const date = new Date(dateTime);
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) {
    return "刚刚";
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatDateTime(dateTime);
  }
}

function handleStart(task: Task) {
  currentTaskId.value = task.id!;
  isStartAll.value = false;
  timeRangeDialogTitle.value = `启动任务: ${task.name}`;
  selectedTimeRange.value = [];
  timeRangeDialogVisible.value = true;
}

function handleStartAll() {
  currentTaskId.value = null;
  isStartAll.value = true;
  timeRangeDialogTitle.value = "批量启动所有任务";
  selectedTimeRange.value = [];
  timeRangeDialogVisible.value = true;
}

async function confirmTimeRange() {
  if (!selectedTimeRange.value || selectedTimeRange.value.length !== 2) {
    ElMessage.error("请选择时间范围");
    return;
  }

  timeRangeLoading.value = true;
  try {
    const timeRange = `${selectedTimeRange.value[0]},${selectedTimeRange.value[1]}`;

    if (isStartAll.value) {
      // 批量启动
      const response = await fetch("/api/tasks/start-all", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ timeRange })
      });
      const result = await response.json();

      if (result.success && result.data) {
        const data = result.data;
        ElMessage.success(
          `批量启动完成: 总数${data.totalTasks}, 成功${data.successCount}, 失败${data.failCount}, 成功率${data.successRate.toFixed(1)}%`
        );
        if (data.successCount > 0) {
          taskRunning.value = true; // 有任务成功启动时设置运行状态
        }
        fetchTasks();
      } else {
        ElMessage.error(result.message || "批量启动失败");
      }
    } else {
      // 单个任务启动
      const response = await fetch(`/api/tasks/${currentTaskId.value}/start`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ timeRange })
      });
      const result = await response.json();

      if (result.success) {
        ElMessage.success(result.message || "任务启动成功");
        taskRunning.value = true; // 任务启动后设置运行状态
        fetchTasks();
      } else {
        ElMessage.error(result.message || "任务启动失败");
      }
    }

    timeRangeDialogVisible.value = false;
  } catch (error) {
    ElMessage.error("启动失败");
    console.error("启动失败:", error);
  } finally {
    timeRangeLoading.value = false;
  }
}

function handleAdd() {
  editTask.value = {
    name: "",
    rule: ""
  };
  activeTab.value = "single";
  resetBatchForm();
  dialogVisible.value = true;
}

function handleEdit(task: Task) {
  editTask.value = { ...task };
  dialogVisible.value = true;
}

async function handleDelete(task: Task) {
  try {
    await ElMessageBox.confirm("确定要删除该任务吗？", "提示", {
      type: "warning"
    });

    const response = await fetch(`/api/tasks/${task.id}`, {
      method: "DELETE"
    });
    const result = await response.json();

    if (result.success) {
      ElMessage.success(result.message || "删除成功");
      fetchTasks();
    } else {
      ElMessage.error(result.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
      console.error("删除失败:", error);
    }
  }
}

async function handleSave() {
  try {
    await editFormRef.value.validate();
    saveLoading.value = true;

    const method = editTask.value.id ? "PUT" : "POST";
    const url = editTask.value.id
      ? `/api/tasks/${editTask.value.id}`
      : "/api/tasks";

    const response = await fetch(url, {
      method,
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(editTask.value)
    });
    const result = await response.json();

    if (result.success) {
      ElMessage.success(
        result.message || (editTask.value.id ? "修改成功" : "新增成功")
      );
      dialogVisible.value = false;
      fetchTasks();
    } else {
      ElMessage.error(
        result.message || (editTask.value.id ? "修改失败" : "新增失败")
      );
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  } finally {
    saveLoading.value = false;
  }
}

function handleSearch() {
  pagination.currentPage = 1;
  fetchTasks();
}

function resetSearch() {
  searchForm.name = "";
  searchForm.status = "";
  searchForm.timeRange = [];
  handleSearch();
}

function resetEditForm() {
  editFormRef.value?.resetFields();
  batchFormRef.value?.resetFields();
  resetBatchForm();
}

function resetBatchForm() {
  batchForm.namePrefix = "";
  batchForm.keywordsText = "";
  batchForm.priority = 5;
}

// 批量创建任务处理函数
async function handleBatchSave() {
  try {
    await batchFormRef.value.validate();
    saveLoading.value = true;

    // 解析关键字列表
    const keywords = batchForm.keywordsText
      .trim()
      .split('\n')
      .map(k => k.trim())
      .filter(k => k.length > 0);

    if (keywords.length === 0) {
      ElMessage.warning("请输入至少一个关键字");
      return;
    }

    // 构建批量创建请求
    const request: BatchTaskCreateRequest = {
      keywords,
      namePrefix: batchForm.namePrefix || "",
      queryTemplate: batchForm.queryTemplate,
      priority: batchForm.priority
    };

    // 调用批量创建API
    const response = await batchCreateTasks(request);

    if (response.success) {
      const result = response.data;
      ElMessage.success(
        `批量创建完成：成功${result.successCount}个，失败${result.failureCount}个`
      );

      // 如果有失败的任务，显示详细信息
      if (result.failureCount > 0) {
        const failedTasks = result.results.filter(r => !r.success);
        console.warn("批量创建失败的任务:", failedTasks);
        ElMessage.warning(`有${result.failureCount}个任务创建失败，请检查控制台详情`);
      }

      dialogVisible.value = false;
      fetchTasks();
    } else {
      ElMessage.error(response.message || "批量创建失败");
    }
  } catch (error) {
    console.error("批量创建失败:", error);
    ElMessage.error("批量创建失败");
  } finally {
    saveLoading.value = false;
  }
}

function handleSelectionChange(selection: Task[]) {
  selectedTasks.value = selection;
}

// 优化组件需要的新方法
function handleDuplicate(task: Task) {
  editTask.value = {
    ...task,
    id: undefined,
    name: `${task.name} - 副本`
  };
  dialogVisible.value = true;
}

function handleStop(task: Task) {
  ElMessage.info("停止功能待实现");
}

function handleViewLogs(task: Task) {
  ElMessage.info("查看日志功能待实现");
}

function handleBatchStart() {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning("请先选择要启动的任务");
    return;
  }
  ElMessage.info("批量启动功能待实现");
}

function handleBatchDelete() {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning("请先选择要删除的任务");
    return;
  }
  ElMessage.info("批量删除功能待实现");
}

function loadTasks() {
  fetchTasks();
}

function handleCreate() {
  handleAdd();
}

function handlePageChange(page: number) {
  pagination.currentPage = page;
  fetchTasks();
}

function handleSizeChange(size: number) {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  fetchTasks();
}

// 查看任务执行详情
async function handleViewDetails(task: Task) {
  try {
    currentTaskDetails.value = task;
    detailsDialogVisible.value = true;
    detailsLoading.value = true;

    // 并行获取执行详情和统计信息
    console.log("开始获取任务执行详情，任务名称:", task.name);
    const [detailsResult, statsResult] = await Promise.all([
      getTaskExecutionDetails({ taskName: task.name }),
      getTaskExecutionStats(task.name)
    ]);

    console.log("执行详情API响应:", detailsResult);
    console.log("执行统计API响应:", statsResult);

    if (detailsResult.success) {
      // 处理分页响应数据
      const pageData = detailsResult.data;
      console.log("分页数据结构:", pageData);
      executionDetails.value = pageData?.content || [];
      console.log("最终执行详情数据:", executionDetails.value);
      console.log("执行详情数据长度:", executionDetails.value.length);
    } else {
      console.error("获取执行详情失败:", detailsResult);
      ElMessage.error("获取执行详情失败: " + detailsResult.message);
      executionDetails.value = [];
    }

    if (statsResult.success) {
      executionStats.value = statsResult.data || {};
      console.log("执行统计数据:", executionStats.value);
    } else {
      console.error("获取执行统计失败:", statsResult);
      ElMessage.error("获取执行统计失败: " + statsResult.message);
      executionStats.value = {};
    }
  } catch (error) {
    ElMessage.error("获取执行详情失败");
    console.error("获取执行详情失败:", error);
    executionDetails.value = [];
    executionStats.value = {};
  } finally {
    detailsLoading.value = false;
  }
}

// 重置详情对话框
function resetDetailsDialog() {
  currentTaskDetails.value = null;
  executionDetails.value = [];
  executionStats.value = null;
  detailsLoading.value = false;
}

// 获取执行状态标签类型（用于执行详情对话框）
function getExecutionStatusTagType(
  status: string
): "success" | "primary" | "warning" | "danger" | "info" {
  const statusMap: Record<
    string,
    "success" | "primary" | "warning" | "danger" | "info"
  > = {
    PENDING: "info",
    PROCESSING: "warning",
    COMPLETED: "success",
    FAILED: "danger"
  };
  return statusMap[status] || "info";
}

// 获取执行状态文本（用于执行详情对话框）
function getExecutionStatusText(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: "待处理",
    PROCESSING: "处理中",
    COMPLETED: "已完成",
    FAILED: "失败"
  };
  return statusMap[status] || status;
}

// 任务状态监听
import { createSmartPolling, type PollingManager } from '@/utils/pageVisibility';

let statusPollingManager: PollingManager | null = null;

// 检查任务运行状态
async function checkTaskRunningStatus() {
  try {
    const response = await fetch('/api/system/status');
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    const result = await response.json();

    if (result.success && result.data) {
      // 根据系统状态更新 taskRunning
      const wasRunning = taskRunning.value;
      taskRunning.value = result.data.status === 'busy' || result.data.queueCount > 0;

      // 如果任务状态发生变化，调整轮询频率
      if (wasRunning !== taskRunning.value && statusPollingManager) {
        const newConfig = taskRunning.value ? {
          visibleInterval: 2000,    // 任务运行时2秒检查一次
          hiddenInterval: 10000     // 隐藏时10秒检查一次
        } : {
          visibleInterval: 10000,   // 任务空闲时10秒检查一次
          hiddenInterval: 60000     // 隐藏时1分钟检查一次
        };
        statusPollingManager.updateConfig(newConfig);
      }
    } else {
      // API返回失败时，保持当前状态不变
      console.warn('系统状态API返回失败:', result.message);
    }
  } catch (error) {
    console.error('检查任务状态失败:', error);
    // 网络错误时，假设任务未运行以避免界面异常
    taskRunning.value = false;
  }
}

// 启动状态监听
function startStatusMonitoring() {
  // 创建智能轮询管理器
  statusPollingManager = createSmartPolling(checkTaskRunningStatus, {
    visibleInterval: 5000,      // 页面可见时5秒检查一次
    hiddenInterval: 30000,      // 页面隐藏时30秒检查一次
    stopAfterHidden: 300000,    // 页面隐藏5分钟后停止检查
    stopImmediately: false      // 不立即停止
  });

  statusPollingManager.start();
  console.log('[TaskPage] 智能任务状态监听已启动');
}

// 停止状态监听
function stopStatusMonitoring() {
  if (statusPollingManager) {
    statusPollingManager.destroy();
    statusPollingManager = null;
    console.log('[TaskPage] 智能任务状态监听已停止');
  }
}

// 生命周期
onMounted(() => {
  fetchTasks();
  startStatusMonitoring();
});

// 组件卸载时清理
onUnmounted(() => {
  stopStatusMonitoring();
});
</script>

<style scoped>
.main {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-group {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.mt-4 {
  margin-top: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.text-gray-400 {
  color: #9ca3af;
}

/* 执行详情对话框样式 */
.mb-4 {
  margin-bottom: 16px;
}

.rule-text {
  word-break: break-all;
  max-width: 300px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

/* 批量创建表单样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.el-tabs--border-card {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.el-tab-pane {
  padding: 20px;
}
</style>
