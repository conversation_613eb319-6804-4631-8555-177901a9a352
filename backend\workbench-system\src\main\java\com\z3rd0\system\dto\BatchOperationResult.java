package com.z3rd0.system.dto;

import lombok.Data;

import java.util.List;

/**
 * 批量操作结果
 */
@Data
public class BatchOperationResult {
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 成功数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int failureCount;
    
    /**
     * 详细结果列表
     */
    private List<TaskResult> results;
    
    public BatchOperationResult(String operationType, int successCount, int failureCount, List<TaskResult> results) {
        this.operationType = operationType;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.results = results;
    }
    
    /**
     * 单个任务操作结果
     */
    @Data
    public static class TaskResult {
        
        /**
         * 任务ID
         */
        private Long taskId;
        
        /**
         * 任务名称
         */
        private String taskName;
        
        /**
         * 操作是否成功
         */
        private boolean success;
        
        /**
         * 结果消息
         */
        private String message;
        
        public TaskResult(Long taskId, String taskName, boolean success, String message) {
            this.taskId = taskId;
            this.taskName = taskName;
            this.success = success;
            this.message = message;
        }
        
        /**
         * 创建成功结果
         */
        public static TaskResult success(Long taskId, String taskName, String message) {
            return new TaskResult(taskId, taskName, true, message);
        }
        
        /**
         * 创建失败结果
         */
        public static TaskResult failure(Long taskId, String taskName, String message) {
            return new TaskResult(taskId, taskName, false, message);
        }
    }
}
