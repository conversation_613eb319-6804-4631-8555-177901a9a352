package com.z3rd0.common.model;

/**
 * 任务优先级枚举
 * 定义任务执行的优先级等级，数字越小优先级越高
 */
public enum TaskPriority {
    
    URGENT(1, "紧急", "需要立即执行的任务"),
    HIGH(2, "高", "重要且紧急的任务"),
    ABOVE_NORMAL(3, "较高", "比正常优先级稍高的任务"),
    NORMAL(5, "正常", "常规任务，默认优先级"),
    BELOW_NORMAL(7, "较低", "比正常优先级稍低的任务"),
    LOW(8, "低", "不紧急的任务"),
    LOWEST(10, "最低", "可以延后执行的任务");
    
    private final int value;
    private final String displayName;
    private final String description;
    
    TaskPriority(int value, String displayName, String description) {
        this.value = value;
        this.displayName = displayName;
        this.description = description;
    }
    
    public int getValue() {
        return value;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据数值获取优先级枚举
     * @param value 优先级数值
     * @return 对应的优先级枚举，如果没有匹配的则返回NORMAL
     */
    public static TaskPriority fromValue(int value) {
        for (TaskPriority priority : values()) {
            if (priority.value == value) {
                return priority;
            }
        }
        return NORMAL; // 默认返回正常优先级
    }
    
    /**
     * 根据任务特征自动判断优先级
     * @param timeRange 时间范围
     * @param taskName 任务名称
     * @return 建议的优先级
     */
    public static TaskPriority suggestPriority(String timeRange, String taskName) {
        // 如果任务名包含"紧急"、"urgent"等关键词，设为高优先级
        if (taskName != null) {
            String lowerName = taskName.toLowerCase();
            if (lowerName.contains("紧急") || lowerName.contains("urgent") || 
                lowerName.contains("重要") || lowerName.contains("important")) {
                return HIGH;
            }
            if (lowerName.contains("测试") || lowerName.contains("test")) {
                return LOW;
            }
        }
        
        // 根据时间范围判断：如果是今天的数据，优先级较高
        if (timeRange != null && timeRange.contains(java.time.LocalDate.now().toString())) {
            return ABOVE_NORMAL;
        }
        
        return NORMAL; // 默认正常优先级
    }
    
    /**
     * 检查优先级是否有效
     * @param priority 优先级数值
     * @return 是否在有效范围内(1-10)
     */
    public static boolean isValidPriority(Integer priority) {
        return priority != null && priority >= 1 && priority <= 10;
    }
    
    /**
     * 规范化优先级数值
     * @param priority 输入的优先级
     * @return 规范化后的优先级，超出范围的会被调整到边界值
     */
    public static int normalizePriority(Integer priority) {
        if (priority == null) {
            return NORMAL.value;
        }
        if (priority < 1) {
            return URGENT.value;
        }
        if (priority > 10) {
            return LOWEST.value;
        }
        return priority;
    }
}
