<template>
  <div class="task-table-container">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button-group v-if="localSelectedTasks.length > 0">
          <el-button size="small" @click="handleBatchStart">
            <el-icon><VideoPlay /></el-icon>
            批量启动
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </el-button-group>
        <span v-if="localSelectedTasks.length > 0" class="selection-info">
          已选择 {{ localSelectedTasks.length }} 个任务
        </span>
      </div>

      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="toggleDensity">
            <el-icon><Operation /></el-icon>
            {{ density === "default" ? "紧凑" : "默认" }}
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建任务
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 优化后的任务表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tasks"
      :size="density"
      :border="true"
      :fit="true"
      :row-class-name="getRowClassName"
      :header-cell-style="{ fontWeight: 'bold', backgroundColor: '#fafafa' }"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 选择列 -->
      <el-table-column type="selection" width="50" align="center" />

      <!-- ID（包含状态指示） -->
      <el-table-column
        prop="id"
        label="ID"
        width="80"
        align="center"
        fixed="left"
        class-name="id-with-status"
      >
        <template #default="{ row }">
          <div class="id-cell">
            <div class="status-indicator-inline" :class="getStatusBarClass(row)" />
            <span class="id-text">{{ row.id }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 任务名称 -->
      <el-table-column
        prop="name"
        label="任务名称"
        width="120"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="task-name-cell">
            <span class="task-name">{{ row.name }}</span>
            <el-tag
              v-if="row.priority && row.priority !== 'normal'"
              :type="getPriorityTagType(row.priority)"
              size="small"
              class="priority-tag"
            >
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 搜索规则 -->
      <el-table-column label="搜索规则" min-width="200">
        <template #default="{ row }">
          <el-popover
            placement="top"
            width="400"
            trigger="hover"
            :disabled="!row.rule"
          >
            <template #reference>
              <div class="rule-preview">
                {{ truncateRule(row.rule) }}
              </div>
            </template>

            <div class="rule-detail">
              <pre class="rule-code">{{ row.rule }}</pre>
            </div>
          </el-popover>
        </template>
      </el-table-column>

      <!-- 筛选范围 -->
      <el-table-column
        prop="filterRange"
        label="筛选范围"
        width="180"
        align="center"
      >
        <template #default="{ row }">
          <div v-if="row.filterRange" class="filter-range-display">
            <div class="time-range-line">
              <span class="time-label">开始：</span>
              <span class="time-value">{{ formatTimeRange(row.filterRange, 'start') }}</span>
            </div>
            <div class="time-range-line">
              <span class="time-label">结束：</span>
              <span class="time-value">{{ formatTimeRange(row.filterRange, 'end') }}</span>
            </div>
          </div>
          <span v-else class="text-placeholder">全部</span>
        </template>
      </el-table-column>

      <!-- 状态 -->
      <el-table-column label="状态" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row)" size="small" class="status-tag">
            {{ getStatusDisplayName(row) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 执行信息 -->
      <el-table-column label="执行信息" width="140" align="center">
        <template #default="{ row }">
          <div class="execution-info">
            <div v-if="row.executedAt" class="execution-time">
              <el-tooltip
                :content="`执行时间: ${formatDateTime(row.executedAt)}`"
              >
                <span class="time-text">{{
                  formatRelativeTime(row.executedAt)
                }}</span>
              </el-tooltip>
            </div>
            <div v-else class="no-execution">
              <span class="text-placeholder">未执行</span>
            </div>

            <div v-if="row.retryCount > 0" class="retry-info">
              <el-tag type="warning" size="small">
                重试 {{ row.retryCount }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 创建时间 -->
      <el-table-column label="创建时间" width="120" align="center">
        <template #default="{ row }">
          <el-tooltip :content="formatDateTime(row.createdAt)">
            <span class="time-text">{{
              formatRelativeTime(row.createdAt)
            }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <!-- 主要操作：启动 -->
            <el-button
              size="small"
              type="primary"
              :loading="props.startingTasks.has(row.id)"
              :disabled="isTaskRunning(row)"
              class="action-primary"
              @click.stop="handleStart(row)"
            >
              <el-icon v-if="!props.startingTasks.has(row.id)"
                ><VideoPlay
              /></el-icon>
              {{ props.startingTasks.has(row.id) ? "启动中" : "启动" }}
            </el-button>

            <!-- 次要操作：下拉菜单 -->
            <el-dropdown
              trigger="click"
              @command="command => handleDropdownAction(command, row)"
            >
              <el-button
                size="small"
                type="info"
                plain
                circle
                class="action-more"
              >
                <el-icon><MoreFilled /></el-icon>
              </el-button>

              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="duplicate">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="stop"
                    :disabled="!isTaskRunning(row)"
                  >
                    <el-icon><VideoPause /></el-icon>
                    停止
                  </el-dropdown-item>
                  <el-dropdown-item command="logs">
                    <el-icon><Document /></el-icon>
                    查看日志
                  </el-dropdown-item>
                  <el-dropdown-item command="details">
                    <el-icon><DataAnalysis /></el-icon>
                    执行详情
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    <span style="color: #f56c6c">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="localPagination.currentPage"
        v-model:page-size="localPagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="localPagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  VideoPlay,
  VideoPause,
  Delete,
  Operation,
  Refresh,
  Plus,
  MoreFilled,
  Edit,
  CopyDocument,
  Document,
  DataAnalysis
} from "@element-plus/icons-vue";

// Props
const props = defineProps({
  tasks: { type: Array, default: () => [] },
  loading: { type: Boolean, default: false },
  pagination: { type: Object, required: true },
  startingTasks: { type: Set, default: () => new Set() },
  taskRunning: { type: Boolean, default: false }
});

// 响应式数据
const tableRef = ref();
const density = ref<"default" | "small" | "large">("default");
const localSelectedTasks = ref([]);

// 本地分页状态，避免直接修改props
const localPagination = computed({
  get: () => props.pagination,
  set: value => {
    emit("update:pagination", value);
  }
});

// 计算属性和方法
const truncateRule = rule => {
  if (!rule) return "";
  // 根据列宽动态计算截断长度，充分利用空间
  // 假设每个字符约占8px，列宽200px，减去padding约可显示120个字符
  return rule.length > 120 ? rule.slice(0, 120) + "..." : rule;
};

const getPriorityTagType = priority => {
  const types = {
    high: "danger",
    medium: "warning",
    low: "info"
  };
  return types[priority] || "info";
};

const getPriorityText = priority => {
  const texts = {
    high: "高",
    medium: "中",
    low: "低"
  };
  return texts[priority] || priority;
};

const getStatusBarClass = row => {
  // 基于新的五状态系统
  switch (row.status) {
    case "PROCESSING":
      return "status-running"; // 扫描中
    case "COMPLETED":
      return "status-completed"; // 已扫描/历史完成
    case "QUEUED":
      return "status-queued"; // 待扫描
    case "FAILED":
      return "status-failed"; // 扫描出错
    case "PENDING":
    default:
      return "status-idle"; // 未扫描
  }
};

const getRowClassName = ({ row }) => {
  const classes = [];
  if (isTaskRunning(row)) classes.push("row-running"); // 扫描中
  if (row.status === "FAILED") classes.push("row-failed"); // 失败状态
  return classes.join(" ");
};

const toggleDensity = () => {
  density.value = density.value === "default" ? "small" : "default";
};

const handleDropdownAction = (command, row) => {
  switch (command) {
    case "edit":
      handleEdit(row);
      break;
    case "duplicate":
      handleDuplicate(row);
      break;
    case "stop":
      handleStop(row);
      break;
    case "logs":
      handleViewLogs(row);
      break;
    case "details":
      handleViewDetails(row);
      break;
    case "delete":
      handleDelete(row);
      break;
  }
};

// 需要从原组件导入的方法
const handleSelectionChange = selection => {
  localSelectedTasks.value = selection;
  emit("selection-change", selection);
};

const handleRowClick = row => {
  // 可选：点击行查看详情
};

// 事件发射器
const emit = defineEmits([
  "selection-change",
  "start",
  "edit",
  "delete",
  "duplicate",
  "stop",
  "view-logs",
  "view-details",
  "batch-start",
  "batch-delete",
  "refresh",
  "create",
  "size-change",
  "page-change",
  "update:pagination"
]);

// 方法实现
const handleStart = row => {
  emit("start", row);
};

const handleEdit = row => {
  emit("edit", row);
};

const handleDelete = row => {
  emit("delete", row);
};

const handleDuplicate = row => {
  emit("duplicate", row);
};

const handleStop = row => {
  emit("stop", row);
};

const handleViewLogs = row => {
  emit("view-logs", row);
};

const handleViewDetails = row => {
  emit("view-details", row);
};

const handleBatchStart = () => {
  emit("batch-start", localSelectedTasks.value);
};

const handleBatchDelete = () => {
  emit("batch-delete", localSelectedTasks.value);
};

const handleRefresh = () => {
  emit("refresh");
};

const handleCreate = () => {
  emit("create");
};

// 完善的五状态系统 - 与主页面保持一致
const getStatusTagType = row => {
  // 状态优先级：扫描中 > 已扫描 > 待扫描 > 扫描出错 > 未扫描
  switch (row.status) {
    case "PROCESSING":
      return "warning"; // 扫描中 - 橙色
    case "COMPLETED":
      if (isExecutedToday(row)) {
        return "success"; // 已扫描 - 绿色
      }
      return "info"; // 历史完成 - 蓝色
    case "QUEUED":
      return "primary"; // 待扫描 - 蓝色
    case "FAILED":
      return "danger"; // 扫描出错 - 红色
    case "PENDING":
    default:
      return "info"; // 未扫描 - 灰色
  }
};

const getStatusDisplayName = row => {
  // 状态优先级：扫描中 > 已扫描 > 待扫描 > 扫描出错 > 未扫描
  switch (row.status) {
    case "PROCESSING":
      return "扫描中";
    case "COMPLETED":
      if (isExecutedToday(row)) {
        return "已扫描";
      }
      return "历史完成";
    case "QUEUED":
      return "待扫描";
    case "FAILED":
      return "扫描出错";
    case "PENDING":
    default:
      return "未扫描";
  }
};

// 判断任务是否正在运行
const isTaskRunning = row => {
  // 检查全局运行状态或任务特定状态
  return props.taskRunning || ["PROCESSING", "running"].includes(row.status);
};

// 判断任务今天是否已执行
const isExecutedToday = row => {
  if (!row.executedAt) {
    return false;
  }
  const today = new Date().toDateString();
  const executedDate = new Date(row.executedAt).toDateString();
  return today === executedDate;
};

const formatDateTime = date => {
  if (!date) return "";
  return new Date(date).toLocaleString("zh-CN");
};

const formatRelativeTime = date => {
  if (!date) return "";
  const now = new Date();
  const diff = now.getTime() - new Date(date).getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) return "今天";
  if (days === 1) return "昨天";
  if (days < 7) return `${days}天前`;
  if (days < 30) return `${Math.floor(days / 7)}周前`;
  return `${Math.floor(days / 30)}个月前`;
};

// 格式化时间范围显示
const formatTimeRange = (timeRange, type) => {
  if (!timeRange) return "";

  // 时间范围格式通常是 "startTime,endTime"
  const times = timeRange.split(',');
  if (times.length !== 2) return timeRange;

  const [startTime, endTime] = times;
  const targetTime = type === 'start' ? startTime : endTime;

  // 格式化时间显示，只显示日期和时间，去掉秒
  try {
    const date = new Date(targetTime);
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return targetTime;
  }
};

const handleSizeChange = size => {
  emit("size-change", size);
};

const handlePageChange = page => {
  emit("page-change", page);
};


</script>

<style scoped>
/* 任务表格容器 */
.task-table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* 工具栏样式 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.selection-info {
  color: #606266;
  font-size: 14px;
  margin-left: 12px;
}

/* 表格行状态 */
.row-running {
  background-color: #f0f9ff !important;
}

.row-failed {
  background-color: #fef2f2 !important;
}

/* ID列状态指示 */
.id-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.status-indicator-inline {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #e4e7ed;
  flex-shrink: 0;
}

.id-text {
  font-weight: 500;
  color: #606266;
}

.status-running .status-indicator-inline {
  background: #409eff;
  animation: pulse 2s infinite;
}

.status-failed .status-indicator-inline {
  background: #f56c6c;
}

.status-completed .status-indicator-inline {
  background: #67c23a;
}

.status-queued .status-indicator-inline {
  background: #409eff;
}

.status-idle .status-indicator-inline {
  background: #e4e7ed;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 任务名称单元格 */
.task-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-name {
  font-weight: 500;
  color: #303133;
}

.priority-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  padding: 0 4px;
}

/* 搜索规则预览 */
.rule-preview {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  color: #303133;
  padding: 4px 8px;
  cursor: help;
  line-height: 1.4;
  word-break: break-all;
  white-space: pre-wrap;
}

.rule-detail {
  padding: 8px 0;
}

.rule-code {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  color: #303133;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

/* 筛选范围显示 */
.filter-range-display {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.time-range-line {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.time-label {
  color: #909399;
  font-size: 11px;
  min-width: 32px;
  text-align: right;
}

.time-value {
  color: #303133;
  font-size: 11px;
  font-weight: 500;
}

/* 筛选标签 */
.filter-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

/* 状态标签 */
.status-tag {
  font-weight: 500;
  font-size: 11px;
}

/* 执行信息 */
.execution-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.execution-time {
  font-size: 12px;
}

.no-execution {
  color: #c0c4cc;
  font-style: italic;
  font-size: 12px;
}

.retry-info {
  margin-top: 2px;
}

.retry-info .el-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
}

/* 时间显示 */
.time-text {
  color: #909399;
  font-size: 12px;
  cursor: help;
}

.text-placeholder {
  color: #c0c4cc;
  font-style: italic;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-primary {
  min-width: 60px;
  font-size: 12px;
}

.action-primary:disabled {
  opacity: 0.6;
}

.action-more {
  width: 24px;
  height: 24px;
  padding: 0;
  min-height: 24px;
}

/* 分页容器 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
  background: #fafafa;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .table-toolbar {
    padding: 12px 16px;
  }

  .pagination-wrapper {
    padding: 12px 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-primary {
    font-size: 11px;
    padding: 4px 8px;
    min-width: 50px;
  }

  .task-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 表格密度调整 */
:deep(.el-table--small) {
  .action-primary {
    padding: 2px 6px;
    font-size: 11px;
    min-width: 50px;
  }

  .priority-tag,
  .filter-tag,
  .status-tag {
    height: 18px;
    line-height: 16px;
    font-size: 10px;
  }

  .rule-preview {
    padding: 2px 6px;
    font-size: 11px;
  }
}

/* 表格头部样式 */
:deep(.el-table__header-wrapper) {
  .el-table__header {
    .el-table__cell {
      background-color: #fafafa;
      color: #303133;
      font-weight: 600;
      border-bottom: 2px solid #e4e7ed;
    }
  }
}

/* 表格悬浮效果 */
:deep(.el-table__body) {
  .el-table__row:hover {
    background-color: #f5f7fa !important;
  }
}

/* 下拉菜单样式 */
:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 14px;
    }
  }

  .el-dropdown-menu__item:disabled {
    opacity: 0.5;
  }
}

/* 加载状态的按钮动画 */
.action-primary.is-loading {
  position: relative;
}

.action-primary.is-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  margin: -6px 0 0 -6px;
  border: 2px solid transparent;
  border-top-color: #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
