package com.z3rd0.system.monitoring;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异常监控组件
 * 用于统计和监控系统中的异常情况
 */
@Slf4j
@Component
public class ExceptionMonitor {

    /**
     * 异常计数器
     */
    private final ConcurrentHashMap<String, AtomicLong> exceptionCounters = new ConcurrentHashMap<>();

    /**
     * 最近异常记录
     */
    private final ConcurrentHashMap<String, ExceptionRecord> recentExceptions = new ConcurrentHashMap<>();

    /**
     * 记录异常
     * 
     * @param exceptionType 异常类型
     * @param message 异常消息
     * @param errorCode 错误代码
     */
    public void recordException(String exceptionType, String message, String errorCode) {
        // 增加计数
        exceptionCounters.computeIfAbsent(exceptionType, k -> new AtomicLong(0)).incrementAndGet();
        
        // 记录最近异常
        ExceptionRecord record = new ExceptionRecord(exceptionType, message, errorCode, LocalDateTime.now());
        recentExceptions.put(exceptionType, record);
        
        // 记录日志
        log.debug("异常统计 - 类型: {}, 错误代码: {}, 总计: {}", 
                 exceptionType, errorCode, exceptionCounters.get(exceptionType).get());
    }

    /**
     * 获取异常计数
     * 
     * @param exceptionType 异常类型
     * @return 异常计数
     */
    public long getExceptionCount(String exceptionType) {
        AtomicLong counter = exceptionCounters.get(exceptionType);
        return counter != null ? counter.get() : 0;
    }

    /**
     * 获取总异常计数
     * 
     * @return 总异常计数
     */
    public long getTotalExceptionCount() {
        return exceptionCounters.values().stream()
                .mapToLong(AtomicLong::get)
                .sum();
    }

    /**
     * 获取最近的异常记录
     * 
     * @param exceptionType 异常类型
     * @return 异常记录
     */
    public ExceptionRecord getRecentException(String exceptionType) {
        return recentExceptions.get(exceptionType);
    }

    /**
     * 获取所有异常统计
     * 
     * @return 异常统计信息
     */
    public ConcurrentHashMap<String, AtomicLong> getAllExceptionCounts() {
        return new ConcurrentHashMap<>(exceptionCounters);
    }

    /**
     * 清除统计信息
     */
    public void clearStatistics() {
        exceptionCounters.clear();
        recentExceptions.clear();
        log.info("异常统计信息已清除");
    }

    /**
     * 检查是否存在高频异常
     * 
     * @param threshold 阈值
     * @return 是否存在高频异常
     */
    public boolean hasHighFrequencyExceptions(long threshold) {
        return exceptionCounters.values().stream()
                .anyMatch(counter -> counter.get() > threshold);
    }

    /**
     * 获取异常统计报告
     * 
     * @return 统计报告
     */
    public String getStatisticsReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 异常统计报告 ===\n");
        report.append("总异常数: ").append(getTotalExceptionCount()).append("\n");
        report.append("异常类型统计:\n");
        
        exceptionCounters.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue().get(), e1.getValue().get()))
                .forEach(entry -> {
                    report.append("  ").append(entry.getKey())
                          .append(": ").append(entry.getValue().get()).append("\n");
                });
        
        return report.toString();
    }

    /**
     * 异常记录
     */
    public static class ExceptionRecord {
        private final String exceptionType;
        private final String message;
        private final String errorCode;
        private final LocalDateTime timestamp;

        public ExceptionRecord(String exceptionType, String message, String errorCode, LocalDateTime timestamp) {
            this.exceptionType = exceptionType;
            this.message = message;
            this.errorCode = errorCode;
            this.timestamp = timestamp;
        }

        public String getExceptionType() { return exceptionType; }
        public String getMessage() { return message; }
        public String getErrorCode() { return errorCode; }
        public LocalDateTime getTimestamp() { return timestamp; }

        @Override
        public String toString() {
            return String.format("ExceptionRecord{type='%s', errorCode='%s', message='%s', timestamp=%s}",
                    exceptionType, errorCode, message, timestamp);
        }
    }
}
