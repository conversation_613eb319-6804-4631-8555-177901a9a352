-- 修复任务表结构，确保与实体类一致
-- 添加缺失的字段和索引

-- 检查并添加 filter_range 字段（如果不存在）
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS filter_range VARCHAR(500) COMMENT '实际执行时的数据筛选范围';

-- 检查并添加 executed_at 字段（如果不存在）
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS executed_at TIMESTAMP NULL COMMENT '任务执行时间';

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_task_filter_range ON tasks(filter_range);
CREATE INDEX IF NOT EXISTS idx_task_executed_at ON tasks(executed_at);
CREATE INDEX IF NOT EXISTS idx_task_retry_count ON tasks(retry_count);

-- 更新现有记录的默认值
UPDATE tasks SET retry_count = 0 WHERE retry_count IS NULL;
UPDATE tasks SET status = 'PENDING' WHERE status IS NULL OR status = '';

-- 添加表注释
ALTER TABLE tasks COMMENT = '任务表 - 存储系统中的所有任务信息';
