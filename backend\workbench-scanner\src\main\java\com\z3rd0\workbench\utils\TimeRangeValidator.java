package com.z3rd0.workbench.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间范围验证工具类
 * 用于验证任务的时间范围是否有效，避免执行无意义的历史数据采集任务
 */
@Component
public class TimeRangeValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeRangeValidator.class);
    
    // 支持的时间格式
    private static final DateTimeFormatter[] FORMATTERS = {
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd")
    };
    
    /**
     * 验证任务时间范围的有效性
     * 
     * @param timeRange 时间范围字符串，格式: "startTime,endTime"
     * @param taskName 任务名称，用于日志记录
     * @return TimeRangeValidationResult 验证结果
     */
    public TimeRangeValidationResult validateTimeRange(String timeRange, String taskName) {
        logger.info("开始验证任务时间范围 - 任务: {}, 时间范围: {}", taskName, timeRange);
        
        // 检查时间范围是否为空
        if (timeRange == null || timeRange.trim().isEmpty()) {
            logger.warn("任务 {} 的时间范围为空，将使用默认范围", taskName);
            return TimeRangeValidationResult.valid("时间范围为空，使用默认范围");
        }
        
        try {
            // 解析时间范围
            String[] rangeParts = timeRange.split(",");
            if (rangeParts.length != 2) {
                logger.warn("任务 {} 的时间范围格式错误: {}，期望格式为'startTime,endTime'", taskName, timeRange);
                return TimeRangeValidationResult.valid("时间范围格式错误，使用默认范围");
            }
            
            String startTimeStr = rangeParts[0].trim();
            String endTimeStr = rangeParts[1].trim();
            
            // 解析开始时间和结束时间
            LocalDateTime startTime = parseDateTime(startTimeStr);
            LocalDateTime endTime = parseDateTime(endTimeStr);
            
            if (startTime == null || endTime == null) {
                logger.warn("任务 {} 的时间解析失败 - 开始时间: {}, 结束时间: {}", taskName, startTimeStr, endTimeStr);
                return TimeRangeValidationResult.valid("时间解析失败，使用默认范围");
            }
            
            // 验证时间范围逻辑
            if (startTime.isAfter(endTime)) {
                logger.warn("任务 {} 的开始时间晚于结束时间 - 开始: {}, 结束: {}", taskName, startTime, endTime);
                return TimeRangeValidationResult.invalid("开始时间晚于结束时间", startTime, endTime);
            }
            
            // 检查是否为过期任务（结束时间早于今天）
            LocalDate today = LocalDate.now();
            LocalDate endDate = endTime.toLocalDate();
            
            if (endDate.isBefore(today)) {
                logger.warn("任务 {} 为过期任务 - 结束时间: {} 早于今天: {}", taskName, endDate, today);
                return TimeRangeValidationResult.expired("任务结束时间早于今天，无法获取有效数据", startTime, endTime);
            }
            
            // 检查是否为未来任务（开始时间晚于今天）
            LocalDate startDate = startTime.toLocalDate();
            if (startDate.isAfter(today)) {
                logger.info("任务 {} 为未来任务 - 开始时间: {} 晚于今天: {}", taskName, startDate, today);
                return TimeRangeValidationResult.future("任务开始时间为未来日期", startTime, endTime);
            }
            
            logger.info("任务 {} 时间范围验证通过 - 开始: {}, 结束: {}", taskName, startTime, endTime);
            return TimeRangeValidationResult.valid("时间范围有效", startTime, endTime);
            
        } catch (Exception e) {
            logger.error("任务 {} 时间范围验证异常: {}", taskName, e.getMessage(), e);
            return TimeRangeValidationResult.valid("验证异常，使用默认范围");
        }
    }
    
    /**
     * 解析时间字符串
     * 
     * @param timeStr 时间字符串
     * @return LocalDateTime 解析后的时间，解析失败返回null
     */
    private LocalDateTime parseDateTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        
        timeStr = timeStr.trim();
        
        // 尝试不同的时间格式
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                // 如果是日期格式（不包含时间），添加默认时间
                if (timeStr.matches("\\d{4}[-/]\\d{2}[-/]\\d{2}$")) {
                    if (formatter.toString().contains("HH:mm:ss")) {
                        continue; // 跳过包含时间的格式
                    }
                    LocalDate date = LocalDate.parse(timeStr, formatter);
                    return date.atStartOfDay(); // 默认为当天开始时间
                } else {
                    return LocalDateTime.parse(timeStr, formatter);
                }
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        logger.warn("无法解析时间字符串: {}", timeStr);
        return null;
    }
    
    /**
     * 时间范围验证结果
     */
    public static class TimeRangeValidationResult {
        private final boolean valid;
        private final boolean expired;
        private final boolean future;
        private final String message;
        private final LocalDateTime startTime;
        private final LocalDateTime endTime;
        
        private TimeRangeValidationResult(boolean valid, boolean expired, boolean future, String message, 
                                        LocalDateTime startTime, LocalDateTime endTime) {
            this.valid = valid;
            this.expired = expired;
            this.future = future;
            this.message = message;
            this.startTime = startTime;
            this.endTime = endTime;
        }
        
        public static TimeRangeValidationResult valid(String message) {
            return new TimeRangeValidationResult(true, false, false, message, null, null);
        }
        
        public static TimeRangeValidationResult valid(String message, LocalDateTime startTime, LocalDateTime endTime) {
            return new TimeRangeValidationResult(true, false, false, message, startTime, endTime);
        }
        
        public static TimeRangeValidationResult invalid(String message, LocalDateTime startTime, LocalDateTime endTime) {
            return new TimeRangeValidationResult(false, false, false, message, startTime, endTime);
        }
        
        public static TimeRangeValidationResult expired(String message, LocalDateTime startTime, LocalDateTime endTime) {
            return new TimeRangeValidationResult(false, true, false, message, startTime, endTime);
        }
        
        public static TimeRangeValidationResult future(String message, LocalDateTime startTime, LocalDateTime endTime) {
            return new TimeRangeValidationResult(true, false, true, message, startTime, endTime);
        }
        
        // Getters
        public boolean isValid() { return valid; }
        public boolean isExpired() { return expired; }
        public boolean isFuture() { return future; }
        public String getMessage() { return message; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        
        /**
         * 是否应该跳过任务执行
         * @return true表示应该跳过，false表示可以执行
         */
        public boolean shouldSkipExecution() {
            return expired; // 只有过期任务需要跳过
        }
        
        /**
         * 获取跳过原因
         * @return 跳过原因描述
         */
        public String getSkipReason() {
            if (expired) {
                return "任务时间范围已过期，无法获取有效数据";
            }
            return null;
        }
    }
}
