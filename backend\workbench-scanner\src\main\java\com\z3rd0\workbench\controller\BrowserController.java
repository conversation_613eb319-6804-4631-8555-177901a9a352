package com.z3rd0.workbench.controller;

import com.z3rd0.workbench.config.CaptchaConfig;
import com.z3rd0.workbench.utils.SingleBrowserManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 浏览器控制器
 * 用于处理浏览器池管理相关的API请求
 */
@RestController
@RequestMapping("/api/browser")
@CrossOrigin(originPatterns = "*", maxAge = 3600, allowCredentials = "true")
public class BrowserController {
    
    private static final Logger logger = LoggerFactory.getLogger(BrowserController.class);
    
    @Autowired
    private SingleBrowserManager singleBrowserManager;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private CaptchaConfig captchaConfig;
    
    /**
     * 获取浏览器池状态
     * @return 浏览器池状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getBrowserStatus() {
        logger.debug("获取浏览器池状态");
        Map<String, Object> response = new HashMap<>();
        
        try {
            String status = singleBrowserManager.getBrowserStatus();
            boolean isInitialized = singleBrowserManager.isBrowserInitialized();
            boolean isProcessing = singleBrowserManager.isProcessingTask();
            
            response.put("status", status);
            response.put("initialized", isInitialized);
            response.put("processing", isProcessing);
            response.put("success", true);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取浏览器池状态失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "获取浏览器池状态失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 初始化浏览器实例
     * @return 初始化结果
     */
    @PostMapping("/initialize")
    public ResponseEntity<Map<String, Object>> initializeBrowser() {
        logger.info("初始化浏览器实例");
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (singleBrowserManager.isBrowserInitialized()) {
                logger.info("浏览器已初始化，无需重复操作");
                response.put("success", true);
                response.put("message", "浏览器已初始化");
                return ResponseEntity.ok(response);
            }

            // 重新初始化浏览器
            singleBrowserManager.init();
            
            response.put("success", true);
            response.put("message", "浏览器初始化成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("初始化浏览器失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "初始化浏览器失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 重新初始化浏览器实例（单例模式下不需要添加多个实例）
     * @return 初始化结果
     */
    @PostMapping("/add")
    public ResponseEntity<Map<String, Object>> addBrowser() {
        logger.info("重新初始化浏览器实例");
        Map<String, Object> response = new HashMap<>();

        try {
            if (singleBrowserManager.isBrowserInitialized()) {
                response.put("success", true);
                response.put("message", "浏览器实例已存在");
                response.put("currentStatus", singleBrowserManager.getBrowserStatus());
            } else {
                singleBrowserManager.init();
                response.put("success", true);
                response.put("message", "成功初始化浏览器实例");
                response.put("currentStatus", singleBrowserManager.getBrowserStatus());
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("初始化浏览器实例失败: {}", e.getMessage(), e);

            response.put("success", false);
            response.put("message", "初始化浏览器实例失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 关闭所有浏览器实例
     * @return 关闭结果
     */
    @PostMapping("/close")
    public ResponseEntity<Map<String, Object>> closeBrowser() {
        logger.info("关闭所有浏览器实例");
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (singleBrowserManager.isProcessingTask()) {
                logger.warn("有任务正在处理中，无法关闭浏览器");
                response.put("success", false);
                response.put("message", "有任务正在处理中，无法关闭浏览器");
                return ResponseEntity.badRequest().body(response);
            }

            singleBrowserManager.closeBrowser();
            
            response.put("success", true);
            response.put("message", "所有浏览器已关闭");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("关闭浏览器失败: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("message", "关闭浏览器失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 检查浏览器状态（单例模式下不需要清理多个实例）
     * @return 状态检查结果
     */
    @PostMapping("/cleanup-idle")
    public ResponseEntity<Map<String, Object>> cleanupIdleBrowsers() {
        logger.info("检查浏览器状态");
        Map<String, Object> response = new HashMap<>();

        try {
            String status = singleBrowserManager.getBrowserStatus();
            boolean isInitialized = singleBrowserManager.isBrowserInitialized();
            boolean isProcessing = singleBrowserManager.isProcessingTask();

            response.put("success", true);
            response.put("message", "浏览器状态检查完成");
            response.put("status", status);
            response.put("initialized", isInitialized);
            response.put("processing", isProcessing);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("检查浏览器状态失败: {}", e.getMessage(), e);

            response.put("success", false);
            response.put("message", "检查浏览器状态失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 修复数据库索引
     * @return 修复结果
     */
    @PostMapping("/fix-indexes")
    public ResponseEntity<Map<String, Object>> fixIndexes() {
        logger.info("开始修复数据库索引");
        Map<String, Object> response = new HashMap<>();

        try {
            // 删除有问题的索引
            logger.info("删除有问题的索引...");
            executeDropIndex("idx_title");
            executeDropIndex("idx_path");
            executeDropIndex("idx_unique_check");

            // 重新创建优化后的索引
            logger.info("创建优化后的索引...");
            executeCreateIndex("idx_title_prefix", "title(100)");
            executeCreateIndex("idx_path_prefix", "path(100)");
            executeCreateIndex("idx_original_id", "original_id");

            // 确保其他必要的索引存在
            logger.info("确保其他索引存在...");
            executeCreateIndex("idx_task_name", "task_name");
            executeCreateIndex("idx_ip", "ip");
            executeCreateIndex("idx_domain_prefix", "domain(100)");
            executeCreateIndex("idx_asn", "asn");
            executeCreateIndex("idx_status_code", "status_code");
            executeCreateIndex("idx_server", "server");
            executeCreateIndex("idx_icp_licence", "icp_licence");
            executeCreateIndex("idx_location_country", "location_country");
            executeCreateIndex("idx_created_at", "created_at");
            executeCreateIndex("idx_is_read", "is_read");
            executeCreateIndex("idx_is_excluded", "is_excluded");

            // 创建复合索引
            logger.info("创建复合索引...");
            executeCreateIndex("idx_task_ip", "task_name, ip");
            executeCreateIndex("idx_read_excluded", "is_read, is_excluded");

            // 显示当前索引状态
            List<String> indexes = showIndexes();

            response.put("success", true);
            response.put("message", "索引修复完成");
            response.put("indexes", indexes);

            logger.info("索引修复完成");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("索引修复失败: {}", e.getMessage(), e);

            response.put("success", false);
            response.put("message", "索引修复失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    private void executeDropIndex(String indexName) {
        try {
            String sql = "DROP INDEX " + indexName + " ON search_results";
            jdbcTemplate.execute(sql);
            logger.info("删除索引成功: {}", indexName);
        } catch (Exception e) {
            logger.debug("索引不存在或删除失败: {} ({})", indexName, e.getMessage());
        }
    }

    private void executeCreateIndex(String indexName, String columns) {
        try {
            String sql = "CREATE INDEX " + indexName + " ON search_results (" + columns + ")";
            jdbcTemplate.execute(sql);
            logger.info("创建索引成功: {} ({})", indexName, columns);
        } catch (Exception e) {
            logger.debug("索引已存在或创建失败: {} ({})", indexName, e.getMessage());
        }
    }

    private List<String> showIndexes() {
        try {
            String sql = "SHOW INDEX FROM search_results";
            return jdbcTemplate.query(sql, (rs, rowNum) ->
                rs.getString("Key_name") + " (" + rs.getString("Column_name") + ")"
            );
        } catch (Exception e) {
            logger.error("显示索引失败: {}", e.getMessage());
            return List.of("无法获取索引信息: " + e.getMessage());
        }
    }

    /**
     * 获取验证码配置
     * @return 验证码配置信息
     */
    @GetMapping("/captcha-config")
    public ResponseEntity<Map<String, Object>> getCaptchaConfig() {
        logger.info("获取验证码配置");
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> config = new HashMap<>();
            config.put("enabled", captchaConfig.isEnabled());
            config.put("waitTimeout", captchaConfig.getWaitTimeout());
            config.put("checkInterval", captchaConfig.getCheckInterval());
            config.put("showProgress", captchaConfig.isShowProgress());
            config.put("progressInterval", captchaConfig.getProgressInterval());
            config.put("useRequestDetection", captchaConfig.isUseRequestDetection());
            config.put("captchaRequestKeyword", captchaConfig.getCaptchaRequestKeyword());
            config.put("captchaVerifyKeyword", captchaConfig.getCaptchaVerifyKeyword());
            config.put("requestDetectionDelay", captchaConfig.getRequestDetectionDelay());
            config.put("customSelectors", captchaConfig.getCustomSelectors());

            response.put("success", true);
            response.put("config", config);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取验证码配置失败: {}", e.getMessage(), e);

            response.put("success", false);
            response.put("message", "获取验证码配置失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新验证码配置
     * @param configMap 配置参数
     * @return 更新结果
     */
    @PostMapping("/captcha-config")
    public ResponseEntity<Map<String, Object>> updateCaptchaConfig(@RequestBody Map<String, Object> configMap) {
        logger.info("更新验证码配置: {}", configMap);
        Map<String, Object> response = new HashMap<>();

        try {
            if (configMap.containsKey("enabled")) {
                captchaConfig.setEnabled((Boolean) configMap.get("enabled"));
            }
            if (configMap.containsKey("waitTimeout")) {
                captchaConfig.setWaitTimeout((Integer) configMap.get("waitTimeout"));
            }
            if (configMap.containsKey("checkInterval")) {
                captchaConfig.setCheckInterval((Integer) configMap.get("checkInterval"));
            }
            if (configMap.containsKey("showProgress")) {
                captchaConfig.setShowProgress((Boolean) configMap.get("showProgress"));
            }
            if (configMap.containsKey("progressInterval")) {
                captchaConfig.setProgressInterval((Integer) configMap.get("progressInterval"));
            }
            if (configMap.containsKey("useRequestDetection")) {
                captchaConfig.setUseRequestDetection((Boolean) configMap.get("useRequestDetection"));
            }
            if (configMap.containsKey("captchaRequestKeyword")) {
                captchaConfig.setCaptchaRequestKeyword((String) configMap.get("captchaRequestKeyword"));
            }
            if (configMap.containsKey("captchaVerifyKeyword")) {
                captchaConfig.setCaptchaVerifyKeyword((String) configMap.get("captchaVerifyKeyword"));
            }
            if (configMap.containsKey("requestDetectionDelay")) {
                captchaConfig.setRequestDetectionDelay((Integer) configMap.get("requestDetectionDelay"));
            }

            response.put("success", true);
            response.put("message", "验证码配置更新成功");

            logger.info("验证码配置更新成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("更新验证码配置失败: {}", e.getMessage(), e);

            response.put("success", false);
            response.put("message", "更新验证码配置失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }
}