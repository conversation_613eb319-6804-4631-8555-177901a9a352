<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>爬虫引擎</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>爬虫引擎</h1>
        </div>

        <!-- 系统状态展示 -->
        <div class="status-banner">
            <div class="status-badge badge-idle" id="system-status">
                <i class="bi bi-activity"></i>
                <span>空闲中</span>
            </div>
            <div class="status-info">
                <div class="status-row">
                    <div class="status-label">系统状态:</div>
                    <div class="status-value">
                        <span class="status-icon green"></span> 正常运行中
                    </div>
                </div>
                <div class="status-row">
                    <div class="status-label">任务队列:</div>
                    <div class="status-value" id="queue-status">
                        <span class="status-icon blue"></span> 0 个任务等待处理
                    </div>
                </div>
                <div class="status-row">
                    <div class="status-label">最近执行:</div>
                    <div class="status-value" id="last-run">
                        <span class="status-icon orange"></span> 暂无执行记录
                    </div>
                </div>
                <div class="status-row">
                    <div class="status-label">账号等级:</div>
                    <div class="status-value" id="account-level">
                        <span class="status-icon blue"></span> 加载中...
                    </div>
                </div>
                <div class="status-row">
                    <div class="status-label">数据限制:</div>
                    <div class="status-value" id="data-limit">
                        <span class="status-icon orange"></span> 加载中...
                    </div>
                </div>
            </div>
            <button class="refresh-btn" id="refresh-status">
                <i class="bi bi-arrow-repeat"></i> 刷新状态
            </button>
        </div>

        <div class="row widescreen-row">
            <!-- 左侧任务配置 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <span>任务配置</span>
                        <span class="info-icon" title="配置爬虫任务的搜索关键词、时间范围等参数">i</span>
                    </div>
                    <div class="card-body">
                        <div id="alert" class="alert"></div>
                        
                        <form id="task-form">
                            <div class="form-group">
                                <label for="task-name" class="form-label">任务名称</label>
                                <input type="text" id="task-name" name="taskName" class="form-control" placeholder="为任务命名，便于识别" required autocomplete="off">
                            </div>
                            
                            <div class="form-group">
                                <label for="search-rule" class="form-label">搜索规则</label>
                                <input type="text" id="search-rule" name="searchRule" class="form-control" placeholder="输入搜索关键词，如：国网" required autocomplete="off">
                            </div>
                            
                            <div class="form-group">
                                <label for="time-range" class="form-label">时间范围</label>
                                <div class="preset-container">
                                    <span class="preset-label">预设:</span>
                                    <div class="preset-buttons">
                                        <button type="button" class="btn btn-sm" data-preset="today">今天</button>
                                        <button type="button" class="btn btn-sm" data-preset="yesterday">昨天</button>
                                        <button type="button" class="btn btn-sm" data-preset="thisWeek">本周</button>
                                        <button type="button" class="btn btn-sm" data-preset="lastWeek">上周</button>
                                    </div>
                                </div>
                                <input type="text" id="time-range" name="timeRange" class="form-control" placeholder="格式: YYYY-MM-DD HH:MM:SS,YYYY-MM-DD HH:MM:SS" required autocomplete="off">
                            </div>
                            
                            <div class="btn-group">
                                <button type="submit" id="submit-btn" class="btn btn-primary">
                                    <i class="bi bi-play-fill"></i> 启动任务
                                </button>
                                <button type="button" id="clear-btn" class="btn btn-secondary">
                                    <i class="bi bi-x-lg"></i> 清空表单
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 任务进度显示 -->
                <div class="task-progress-container" id="task-progress-container">
                    <h3 id="current-task-name" class="text-truncate">任务进行中...</h3>
                    <div class="task-info">
                        <div class="task-info-item">
                            <div class="task-info-label">任务状态</div>
                            <div class="task-info-value" id="task-status">运行中</div>
                        </div>
                        <div class="task-info-item">
                            <div class="task-info-label">已处理</div>
                            <div class="task-info-value" id="processed-count">0</div>
                        </div>
                        <div class="task-info-item">
                            <div class="task-info-label">总数据量</div>
                            <div class="task-info-value" id="total-count">未知</div>
                        </div>
                        <div class="task-info-item">
                            <div class="task-info-label">耗时</div>
                            <div class="task-info-value" id="elapsed-time">00:00:00</div>
                        </div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar progress-bar-animated" role="progressbar" style="width: 0%;" id="progress-bar">0%</div>
                    </div>
                    <div class="progress-info">
                        <span id="progress-text">正在初始化...</span>
                        <span id="remaining-time">剩余时间: 计算中</span>
                    </div>
                    <div class="task-progress-details" id="task-progress-details">
                        等待任务处理信息...
                    </div>
                </div>
            </div>

            <!-- 右侧日志显示 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <span>任务执行日志</span>
                        <div class="btn-group">
                            <button id="clear-logs" class="btn btn-sm btn-secondary">
                                <i class="bi bi-trash"></i> 清空日志
                            </button>
                            <button id="auto-scroll-toggle" class="btn btn-sm btn-primary">
                                <i class="bi bi-arrow-down-circle"></i> 自动滚动: 开
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="console-wrapper" id="task-logs">
                            <!-- 日志将被动态填充 -->
                            <div class="console-line">
                                <span class="timestamp">[00:00:00]</span>
                                <span>系统已就绪，等待任务启动...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/taskManager.js"></script>
</body>
</html> 