package com.z3rd0.system.controller;

import com.z3rd0.system.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.HashMap;
import java.util.Map;

/**
 * 资源监控控制器
 * 提供系统资源使用情况的查询接口
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor")
@RequiredArgsConstructor
public class ResourceMonitorController {
    
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    
    /**
     * 获取系统内存使用情况
     */
    @GetMapping("/memory")
    public ApiResponse<Map<String, Object>> getMemoryUsage() {
        try {
            MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
            
            Map<String, Object> memoryInfo = new HashMap<>();
            
            // 堆内存信息
            Map<String, Object> heapInfo = new HashMap<>();
            heapInfo.put("used", heapMemory.getUsed());
            heapInfo.put("max", heapMemory.getMax());
            heapInfo.put("committed", heapMemory.getCommitted());
            heapInfo.put("init", heapMemory.getInit());
            heapInfo.put("usageRatio", (double) heapMemory.getUsed() / heapMemory.getMax());
            
            // 非堆内存信息
            Map<String, Object> nonHeapInfo = new HashMap<>();
            nonHeapInfo.put("used", nonHeapMemory.getUsed());
            nonHeapInfo.put("max", nonHeapMemory.getMax());
            nonHeapInfo.put("committed", nonHeapMemory.getCommitted());
            nonHeapInfo.put("init", nonHeapMemory.getInit());
            if (nonHeapMemory.getMax() > 0) {
                nonHeapInfo.put("usageRatio", (double) nonHeapMemory.getUsed() / nonHeapMemory.getMax());
            } else {
                nonHeapInfo.put("usageRatio", 0.0);
            }
            
            memoryInfo.put("heap", heapInfo);
            memoryInfo.put("nonHeap", nonHeapInfo);
            memoryInfo.put("timestamp", System.currentTimeMillis());
            
            return ApiResponse.success(memoryInfo, "获取内存使用情况成功");
        } catch (Exception e) {
            log.error("获取内存使用情况失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取内存使用情况失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行垃圾回收
     */
    @PostMapping("/gc")
    public ApiResponse<Map<String, Object>> performGarbageCollection() {
        try {
            log.info("手动执行垃圾回收...");
            
            // 记录GC前的内存使用
            long beforeMemory = memoryBean.getHeapMemoryUsage().getUsed();
            
            // 执行GC
            System.gc();
            
            // 等待GC完成
            Thread.sleep(1000);
            
            // 记录GC后的内存使用
            long afterMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long freedMemory = beforeMemory - afterMemory;
            
            Map<String, Object> gcResult = new HashMap<>();
            gcResult.put("beforeMemoryMB", beforeMemory / 1024 / 1024);
            gcResult.put("afterMemoryMB", afterMemory / 1024 / 1024);
            gcResult.put("freedMemoryMB", freedMemory / 1024 / 1024);
            gcResult.put("timestamp", System.currentTimeMillis());
            
            log.info("垃圾回收完成: 释放内存={}MB", freedMemory / 1024 / 1024);
            return ApiResponse.success(gcResult, "垃圾回收执行成功");
        } catch (Exception e) {
            log.error("执行垃圾回收失败: {}", e.getMessage(), e);
            return ApiResponse.error("执行垃圾回收失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统运行时信息
     */
    @GetMapping("/runtime")
    public ApiResponse<Map<String, Object>> getRuntimeInfo() {
        try {
            Runtime runtime = Runtime.getRuntime();
            
            Map<String, Object> runtimeInfo = new HashMap<>();
            runtimeInfo.put("availableProcessors", runtime.availableProcessors());
            runtimeInfo.put("totalMemory", runtime.totalMemory());
            runtimeInfo.put("freeMemory", runtime.freeMemory());
            runtimeInfo.put("maxMemory", runtime.maxMemory());
            runtimeInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            
            // JVM信息
            Map<String, Object> jvmInfo = new HashMap<>();
            jvmInfo.put("version", System.getProperty("java.version"));
            jvmInfo.put("vendor", System.getProperty("java.vendor"));
            jvmInfo.put("vmName", System.getProperty("java.vm.name"));
            jvmInfo.put("vmVersion", System.getProperty("java.vm.version"));
            
            Map<String, Object> result = new HashMap<>();
            result.put("runtime", runtimeInfo);
            result.put("jvm", jvmInfo);
            result.put("timestamp", System.currentTimeMillis());
            
            return ApiResponse.success(result, "获取运行时信息成功");
        } catch (Exception e) {
            log.error("获取运行时信息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取运行时信息失败: " + e.getMessage());
        }
    }
}
