/**
 * 服务配置文件
 * 生产环境通过Nginx反向代理，前端使用相对路径即可
 * 开发环境仍然需要配置代理地址
 */

// 开发环境配置
const developmentConfig = {
  // 开发环境直接代理到System模块
  apiBaseUrl: 'http://localhost:38889'
};

// 生产环境配置
const productionConfig = {
  // 生产环境使用相对路径，通过Nginx代理
  apiBaseUrl: ''
};

// 根据环境获取配置
const getConfig = () => {
  const isDevelopment = import.meta.env.MODE === 'development';
  return isDevelopment ? developmentConfig : productionConfig;
};

// 导出配置
export const config = getConfig();

// 导出API基础地址
export const API_BASE_URL = config.apiBaseUrl;
