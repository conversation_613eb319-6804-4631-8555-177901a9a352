# Workbench 分布式任务系统

本项目是一个现代化分布式任务系统，采用前后端分离架构，支持多业务模块协作与自动化采集。主要用于网络资产发现、数据采集和自动化任务处理。

## 📁 项目结构

```
Workbench/                    # 项目根目录
├── backend/                  # 后端工程（Maven多模块）
│   ├── workbench-system/     # 主控与API服务模块
│   │   ├── src/main/java/    # Java源码
│   │   │   └── com/z3rd0/system/
│   │   │       ├── controller/    # REST API控制器
│   │   │       ├── service/       # 业务逻辑服务
│   │   │       ├── config/        # 配置类
│   │   │       └── constants/     # 常量定义
│   │   ├── src/main/resources/    # 配置文件
│   │   └── pom.xml               # Maven配置
│   ├── workbench-scanner/    # 采集与自动化模块
│   │   ├── src/main/java/    # Java源码
│   │   │   └── com/z3rd0/workbench/
│   │   │       ├── service/       # 任务处理服务
│   │   │       ├── controller/    # 健康检查等API
│   │   │       ├── config/        # RabbitMQ等配置
│   │   │       └── utils/         # 工具类
│   │   ├── src/main/resources/    # 配置文件
│   │   └── pom.xml               # Maven配置
│   ├── workbench-common/     # 公共模块
│   │   ├── src/main/java/    # 共享实体和工具
│   │   │   └── com/z3rd0/common/
│   │   │       ├── model/         # 数据模型
│   │   │       └── utils/         # 通用工具
│   │   └── pom.xml               # Maven配置
│   └── pom.xml               # 父级Maven配置
├── workbench-web/            # 前端工程
│   ├── src/                  # Vue3源码
│   │   ├── api/              # API接口定义
│   │   ├── components/       # Vue组件
│   │   ├── views/            # 页面视图
│   │   ├── router/           # 路由配置
│   │   ├── store/            # Pinia状态管理
│   │   └── utils/            # 工具函数
│   ├── public/               # 静态资源
│   ├── build/                # 构建配置
│   ├── types/                # TypeScript类型定义
│   ├── package.json          # 前端依赖配置
│   ├── vite.config.ts        # Vite构建配置
│   └── tsconfig.json         # TypeScript配置
├── scripts/                  # 部署脚本
│   ├── quick-deploy.bat      # 快速部署脚本
│   └── recreate-database.sql # 数据库重建脚本
├── StartWorkbench.bat        # 项目启动器（批处理）
├── StartWorkbench.py         # 项目启动器（Python GUI）
├── start-web.bat             # 前端快速启动脚本
└── README.md                 # 项目说明文档
```

## 🏗️ 系统架构

### 技术栈概览

**前端技术栈**
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **样式框架**: Tailwind CSS
- **模板**: Pure Admin
- **路由**: Vue Router 4

**后端技术栈**
- **框架**: Spring Boot 3.5.3
- **数据库**: MySQL 8.0
- **ORM**: Spring Data JPA + Hibernate
- **消息队列**: RabbitMQ
- **自动化**: Playwright
- **构建工具**: Maven
- **Java版本**: JDK 17

### 模块架构

#### 🎯 workbench-system（主控服务）
- **端口**: 38889
- **职责**:
  - 提供统一的REST API接口
  - 任务创建、查询、管理
  - 任务分发到消息队列
  - 搜索结果数据管理
- **主要组件**:
  - `TaskController`: 任务管理API
  - `CompatibilityController`: 兼容性API
  - `TaskService`: 任务业务逻辑
  - `TaskPublisher`: 消息队列发布者

#### 🔍 workbench-scanner（采集服务）
- **端口**: 38888
- **职责**:
  - 消费RabbitMQ任务消息
  - 执行网络资产扫描和数据采集
  - Playwright自动化浏览器操作
  - 任务状态更新和结果存储
- **主要组件**:
  - `TaskConsumer`: 消息队列消费者
  - `BrowserManager`: 浏览器管理器
  - `TaskProcessor`: 任务处理器
  - `HealthController`: 健康检查

#### 📦 workbench-common（公共模块）
- **职责**:
  - 共享数据模型定义
  - 通用工具类和常量
  - 跨模块复用组件
- **主要实体**:
  - `Task`: 任务实体
  - `SearchResult`: 搜索结果实体
  - `MessageUtils`: 消息工具类

#### 🌐 workbench-web（前端应用）
- **端口**: 5173（开发环境）
- **职责**:
  - 任务管理界面
  - 数据查询和展示
  - 系统监控面板
  - 用户交互界面
- **主要功能**:
  - 任务创建和管理
  - 搜索结果查看
  - 实时状态监控
  - 数据导出功能

## 🚀 快速启动

### 环境要求

**基础环境**
- Java 17+
- Node.js 18+
- pnpm 8+
- MySQL 8.0+
- RabbitMQ 3.8+

**Python环境（可选，用于GUI启动器）**
- Python 3.8+
- ttkbootstrap
- psutil

### 启动方式

#### 方式一：GUI启动器（推荐）
```bash
# 在项目根目录执行
StartWorkbench.bat
```
或直接运行：
```bash
python StartWorkbench.py
```

**GUI启动器功能**：
- 一键构建所有模块
- 独立启动/停止各个服务
- 实时查看各模块日志
- 清理构建文件
- 进程管理和监控

#### 方式二：手动启动

**1. 启动后端服务**
```bash
# 启动主控服务（必须）
cd backend/workbench-system
mvn spring-boot:run

# 启动采集服务（必须）
cd backend/workbench-scanner
mvn spring-boot:run
```

**2. 启动前端服务**
```bash
# 方式1：使用快速启动脚本
start-web.bat

# 方式2：手动启动
cd workbench-web
pnpm install  # 首次运行需要安装依赖
pnpm dev
```

#### 方式三：快速部署脚本
```bash
# 一键构建并启动所有服务
scripts/quick-deploy.bat
```

### 服务访问地址

- **前端应用**: http://localhost:5173
- **主控API**: http://localhost:38889
- **采集服务**: http://localhost:38888
- **健康检查**: http://localhost:38888/health/status

## 🔧 配置说明

### 数据库配置

**MySQL配置**（`backend/workbench-system/src/main/resources/application.yml`）
```yaml
spring:
  datasource:
    url: *******************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

**数据库初始化**
```bash
# 使用提供的SQL脚本重建数据库
mysql -u root -p < scripts/recreate-database.sql
```

### RabbitMQ配置

**连接配置**
```yaml
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: your_password
```

**队列配置**
- `task_queue`: 主任务队列
- `task_update_queue`: 任务状态更新队列
- `task_dead_letter_queue`: 死信队列

### 前端代理配置

前端已配置Vite代理，所有`/api`请求自动转发到后端：
```typescript
// vite.config.ts
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:38889',
      changeOrigin: true
    }
  }
}
```

## 📊 核心功能

### 任务管理
- **任务创建**: 支持自定义搜索规则和时间范围
- **任务调度**: 基于RabbitMQ的异步任务处理
- **状态跟踪**: 实时任务状态监控和更新
- **错误处理**: 自动重试机制和死信队列

### 数据采集
- **网络扫描**: 基于规则的网络资产发现
- **自动化操作**: Playwright浏览器自动化
- **数据存储**: 结构化存储搜索结果
- **去重处理**: 基于多字段的智能去重

### 系统监控
- **健康检查**: 各服务状态实时监控
- **性能指标**: 任务执行统计和性能分析
- **日志管理**: 分模块的详细日志记录
- **异常告警**: 异常情况自动检测和通知

## 🏭 生产部署

### Docker部署（推荐）
```bash
# 构建镜像
docker build -t workbench-system ./backend/workbench-system
docker build -t workbench-scanner ./backend/workbench-scanner
docker build -t workbench-web ./workbench-web

# 使用docker-compose启动
docker-compose up -d
```

### 传统部署
```bash
# 后端打包
cd backend
mvn clean package -DskipTests

# 前端构建
cd workbench-web
pnpm build

# 部署到服务器
# 后端：将jar包部署到Java环境
# 前端：将dist目录部署到Nginx等Web服务器
```

### 负载均衡配置
- 前端：通过Nginx实现静态资源CDN和负载均衡
- 后端：支持多实例部署，通过注册中心实现服务发现
- 数据库：支持读写分离和主从复制
- 消息队列：支持集群模式提高可用性

## 🛠️ 开发指南

### API接口文档

#### 任务管理API

**创建任务**
```http
POST /api/tasks
Content-Type: application/json

{
  "name": "任务名称",
  "rule": "搜索规则",
  "timeRange": "时间范围"
}
```

**查询任务列表**
```http
GET /api/tasks?page=0&size=20&sort=id,desc
```

**获取任务详情**
```http
GET /api/tasks/{id}
```

**查询搜索结果**
```http
GET /api/search-results?taskName=任务名称&page=0&size=20
```

#### 兼容性API

**发布搜索任务**
```http
POST /api/publish-search-task
Content-Type: application/json

{
  "name": "任务名称",
  "rule": "搜索规则",
  "timeRange": "时间范围"
}
```

### 数据模型

#### Task（任务实体）
```java
public class Task {
    private Long id;              // 任务ID
    private String name;          // 任务名称
    private String rule;          // 搜索规则
    private String filterRange;   // 筛选范围
    private String timeRange;     // 时间范围
    private LocalDateTime executedAt; // 执行时间
    private String status;        // 任务状态
    private String errorMessage;  // 错误信息
    private Integer retryCount;   // 重试次数
}
```

#### SearchResult（搜索结果实体）
```java
public class SearchResult {
    private Long id;              // 结果ID
    private String originalId;    // 原始ID
    private String taskName;      // 任务名称
    private String ip;            // IP地址
    private String port;          // 端口
    private String domain;        // 域名
    private String hostname;      // 主机名
    private String org;           // 组织
    private Integer asn;          // ASN号
    private String server;        // 服务器信息
    private Integer statusCode;   // HTTP状态码
    private String title;         // 页面标题
    private String icpLicence;    // ICP备案号
    private String locationCountry; // 国家
    private String locationProvince; // 省份
    private String locationCity;  // 城市
    // ... 更多字段
}
```

### 消息队列

#### 队列配置
- **task_queue**: 主任务队列，用于任务分发
- **task_update_queue**: 任务状态更新队列
- **task_dead_letter_queue**: 死信队列，处理失败任务

#### 消息格式
```json
{
  "taskId": 123,
  "name": "任务名称",
  "rule": "搜索规则",
  "timeRange": "时间范围",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### 扩展开发

#### 添加新的数据采集器
1. 在`workbench-scanner`模块创建采集器类
2. 实现`DataCollector`接口
3. 在`TaskProcessor`中注册新采集器
4. 更新配置文件和文档

#### 添加新的API接口
1. 在`workbench-system`模块创建Controller
2. 定义Service业务逻辑
3. 更新API文档
4. 编写单元测试

## ❓ 常见问题

### 环境问题
**Q: Maven依赖下载慢怎么办？**
A: 配置阿里云镜像源，在`~/.m2/settings.xml`中添加：
```xml
<mirror>
  <id>aliyun</id>
  <mirrorOf>central</mirrorOf>
  <url>https://maven.aliyun.com/repository/central</url>
</mirror>
```

**Q: 数据库连接失败？**
A: 检查以下配置：
- MySQL服务是否启动
- 数据库用户名密码是否正确
- 数据库是否存在（使用`scripts/recreate-database.sql`创建）
- 防火墙是否阻止连接

**Q: RabbitMQ连接失败？**
A: 确认：
- RabbitMQ服务已启动
- 用户名密码正确
- 端口5672可访问
- 虚拟主机配置正确

### 功能问题
**Q: 任务执行失败？**
A: 查看日志文件：
- `backend/workbench-system/logs/workbench-system.log`
- `backend/workbench-scanner/logs/workbench-scanner.log`

**Q: 前端页面空白？**
A: 检查：
- 后端API服务是否正常
- 浏览器控制台是否有错误
- 网络代理配置是否正确

**Q: Playwright自动化失败？**
A: 确认：
- 浏览器驱动是否正确安装
- 系统资源是否充足
- 网络连接是否正常

### 性能问题
**Q: 任务处理速度慢？**
A: 优化建议：
- 增加RabbitMQ消费者并发数
- 优化数据库索引
- 调整JVM内存参数
- 使用连接池

**Q: 内存占用过高？**
A: 检查：
- JVM堆内存设置
- 数据库连接池配置
- 是否存在内存泄漏
- 定期清理临时数据

## 📈 监控运维

### 健康检查
```bash
# 检查系统服务状态
curl http://localhost:38888/health/status

# 检查主控服务
curl http://localhost:38889/actuator/health

# 检查前端服务
curl http://localhost:5173
```

### 日志监控
```bash
# 实时查看系统日志
tail -f backend/workbench-system/logs/workbench-system.log

# 实时查看采集日志
tail -f backend/workbench-scanner/logs/workbench-scanner.log

# 查看错误日志
grep ERROR backend/*/logs/*.log
```

### 性能监控
- JVM监控：使用JVisualVM或JProfiler
- 数据库监控：MySQL Performance Schema
- 消息队列监控：RabbitMQ Management Plugin
- 应用监控：集成Micrometer + Prometheus

## 🔄 版本更新

### 当前版本：v1.0.0
- ✅ 基础任务管理系统
- ✅ 网络资产扫描功能
- ✅ Web管理界面
- ✅ 消息队列异步处理
- ✅ 数据存储和查询
- ✅ GUI启动器

### 开发路线图
- 🔄 增强规则引擎和数据源支持
- 📋 实时数据流处理
- 📋 机器学习数据分析
- 📋 Docker容器化部署
- 📋 集群模式支持
- 📋 移动端适配

## 📚 相关文档
- [Pure Admin 官方文档](https://pure-admin.cn/)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [RabbitMQ 官方文档](https://www.rabbitmq.com/)
- [MySQL 官方文档](https://dev.mysql.com/doc/)

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

### 贡献指南
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

---

**⚠️ 免责声明**: 本项目仅供学习和研究使用，请遵守相关法律法规，不得用于非法用途。