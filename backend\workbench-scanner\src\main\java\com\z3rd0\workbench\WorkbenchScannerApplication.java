package com.z3rd0.workbench;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EntityScan(basePackages = {"com.z3rd0.common.model", "com.z3rd0.workbench.model"})
@EnableScheduling
public class WorkbenchScannerApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkbenchScannerApplication.class);

    public static void main(String[] args) {
        logger.info("WorkbenchScanner应用正在启动...");
        SpringApplication.run(WorkbenchScannerApplication.class, args);
        logger.info("WorkbenchScanner应用已启动完成！");
    }
} 