package com.z3rd0.workbench.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.ConditionalRejectingErrorHandler;
import org.springframework.amqp.rabbit.listener.FatalExceptionStrategy;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.support.converter.MessageConversionException;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RabbitMQConfig {

    // 正常任务队列配置
    public static final String QUEUE_NAME = "task_queue";
    public static final String EXCHANGE_NAME = "task_exchange";
    public static final String ROUTING_KEY = "task_routing_key";

    // 优先级任务队列配置（新队列，支持优先级）
    public static final String PRIORITY_QUEUE_NAME = "task_priority_queue";
    public static final String PRIORITY_EXCHANGE_NAME = "task_priority_exchange";
    public static final String PRIORITY_ROUTING_KEY = "task_priority_routing_key";

    // 任务更新队列配置
    public static final String UPDATE_QUEUE_NAME = "task_update_queue";
    public static final String UPDATE_EXCHANGE_NAME = "task_update_exchange";
    public static final String UPDATE_ROUTING_KEY = "task_update_routing_key";
    
    // 死信队列配置
    public static final String DEAD_LETTER_QUEUE = "task_dead_letter_queue";
    public static final String DEAD_LETTER_EXCHANGE = "task_dead_letter_exchange";
    public static final String DEAD_LETTER_ROUTING_KEY = "task_dead_letter_routing_key";

    // 正常队列添加死信队列配置（保持兼容性，不设置优先级）
    @Bean
    public Queue taskQueue() {
        Map<String, Object> args = new HashMap<>();
        // 设置死信交换机
        args.put("x-dead-letter-exchange", DEAD_LETTER_EXCHANGE);
        // 设置死信路由键
        args.put("x-dead-letter-routing-key", DEAD_LETTER_ROUTING_KEY);
        // 为了兼容性，也为普通队列添加优先级支持
        args.put("x-max-priority", 10);
        return new Queue(QUEUE_NAME, true, false, false, args);
    }

    // 优先级队列配置（新队列，支持优先级）
    @Bean
    public Queue priorityTaskQueue() {
        Map<String, Object> args = new HashMap<>();
        // 设置死信交换机
        args.put("x-dead-letter-exchange", DEAD_LETTER_EXCHANGE);
        // 设置死信路由键
        args.put("x-dead-letter-routing-key", DEAD_LETTER_ROUTING_KEY);
        // 设置队列最大优先级为10（支持1-10的优先级）
        args.put("x-max-priority", 10);
        return new Queue(PRIORITY_QUEUE_NAME, true, false, false, args);
    }

    @Bean
    public DirectExchange taskExchange() {
        return new DirectExchange(EXCHANGE_NAME);
    }

    @Bean
    public Binding binding() {
        return BindingBuilder.bind(taskQueue()).to(taskExchange()).with(ROUTING_KEY);
    }

    // 优先级交换机和绑定
    @Bean
    public DirectExchange priorityTaskExchange() {
        return new DirectExchange(PRIORITY_EXCHANGE_NAME);
    }

    @Bean
    public Binding priorityBinding() {
        return BindingBuilder.bind(priorityTaskQueue()).to(priorityTaskExchange()).with(PRIORITY_ROUTING_KEY);
    }

    // 任务更新队列配置
    @Bean
    public Queue taskUpdateQueue() {
        return new Queue(UPDATE_QUEUE_NAME, true);
    }

    @Bean
    public DirectExchange taskUpdateExchange() {
        return new DirectExchange(UPDATE_EXCHANGE_NAME);
    }

    @Bean
    public Binding updateBinding() {
        return BindingBuilder.bind(taskUpdateQueue()).to(taskUpdateExchange()).with(UPDATE_ROUTING_KEY);
    }

    // 死信队列配置
    @Bean
    public Queue deadLetterQueue() {
        return new Queue(DEAD_LETTER_QUEUE, true);
    }

    @Bean
    public DirectExchange deadLetterExchange() {
        return new DirectExchange(DEAD_LETTER_EXCHANGE);
    }

    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder.bind(deadLetterQueue()).to(deadLetterExchange()).with(DEAD_LETTER_ROUTING_KEY);
    }

    @Bean
    public MessageConverter jsonMessageConverter() {
        // 设置中文编码和转换选项
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // 关键修正：通过构造函数传入自定义的 ObjectMapper
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(jsonMessageConverter());
        // 设置返回消息模式
        rabbitTemplate.setMandatory(true);
        return rabbitTemplate;
    }
    
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(jsonMessageConverter());
        factory.setPrefetchCount(1);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        
        // 配置错误处理策略
        factory.setErrorHandler(customErrorHandler());
        
        return factory;
    }
    
    @Bean
    public ConditionalRejectingErrorHandler customErrorHandler() {
        return new ConditionalRejectingErrorHandler(new MyFatalExceptionStrategy());
    }
    
    // 自定义异常处理策略，允许消息解析错误不导致消息被拒绝
    public static class MyFatalExceptionStrategy extends ConditionalRejectingErrorHandler.DefaultExceptionStrategy {
        private final Logger logger = LoggerFactory.getLogger(getClass());
        
        @Override
        public boolean isFatal(Throwable t) {
            if (t instanceof MessageConversionException) {
                logger.warn("消息转换异常，将被视为非致命错误: {}", t.getMessage());
                return false;
            }
            return super.isFatal(t);
        }
    }
} 