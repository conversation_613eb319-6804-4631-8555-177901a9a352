import { $t } from "@/plugins/i18n";
import { task } from "@/router/enums";
import type { RouteConfigsTable } from "~/types/router";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/task",
  name: "TaskList",
  component: Layout,
  redirect: "/task/index",
  meta: {
    icon: "ep:list",
    title: "任务列表",
    rank: task
  },
  children: [
    {
      path: "/task/index",
      name: "TaskIndex",
      component: () => import("@/views/task/index.vue"),
      meta: {
        title: "任务列表"
      }
    }
  ]
} satisfies RouteConfigsTable;
