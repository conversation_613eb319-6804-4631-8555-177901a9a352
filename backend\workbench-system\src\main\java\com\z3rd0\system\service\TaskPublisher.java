package com.z3rd0.system.service;

import com.z3rd0.common.model.Task;
import com.z3rd0.common.model.TaskPriority;
import com.z3rd0.system.constants.RabbitMQConstants;
import com.z3rd0.system.repository.TaskRepository;
import com.z3rd0.system.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 任务发布服务
 * 负责将任务发送到RabbitMQ队列，供scanner模块消费
 * 使用统一的常量类管理RabbitMQ配置
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskPublisher {

    private final RabbitTemplate rabbitTemplate;
    private final TaskRepository taskRepository;

    /**
     * 发布搜索任务到RabbitMQ
     * @param name 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     */
    public void publishSearchTask(String name, String rule, String timeRange) {
        publishSearchTaskWithId(null, name, rule, timeRange);
    }

    /**
     * 发布搜索任务到RabbitMQ（带任务ID）
     * 如果任务ID为null，则创建新任务并获取ID
     * @param taskId 任务ID
     * @param name 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @return 任务ID
     */
    public Long publishSearchTaskWithId(Long taskId, String name, String rule, String timeRange) {
        try {
            // 如果没有提供taskId，则创建新任务
            Task task;
            if (taskId == null) {
                task = new Task();
                task.setName(name);
                task.setRule(rule);
                task.setTimeRange(timeRange);
                // 自动设置优先级
                TaskPriority suggestedPriority = TaskPriority.suggestPriority(timeRange, name);
                task.setPriority(suggestedPriority.getValue());
                // 不设置筛选范围，这个由scanner执行后更新
                task.setStatus("PENDING");
                task = taskRepository.save(task);
                taskId = task.getId();
                log.info("已创建新任务: ID={}, 名称={}, 优先级={} ({})",
                        taskId, name, task.getPriority(), suggestedPriority.getDisplayName());
            } else {
                // 更新现有任务状态
                final Long finalTaskId = taskId;
                task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new RuntimeException("找不到指定ID的任务: " + finalTaskId));
                task.setStatus("PENDING");
                task.setTimeRange(timeRange);
                // 重新评估优先级
                TaskPriority suggestedPriority = TaskPriority.suggestPriority(timeRange, name);
                task.setPriority(suggestedPriority.getValue());
                task = taskRepository.save(task);
                log.info("已更新任务状态: ID={}, 名称={}, 优先级={} ({})",
                        taskId, name, task.getPriority(), suggestedPriority.getDisplayName());
            }

            // 使用工具类创建消息，包含优先级信息
            Message message = MessageUtils.createTaskMessage(taskId, name, rule, timeRange, task.getRetryCount(), task.getPriority());

            // 发送消息到队列
            rabbitTemplate.send(RabbitMQConstants.TASK_EXCHANGE_NAME, RabbitMQConstants.TASK_ROUTING_KEY, message);

            // 更新任务状态为待扫描（已加入队列）
            task.setStatus("QUEUED");
            taskRepository.save(task);

            log.info("任务已发送到队列: ID={}, 名称={}, 规则={}, 时间范围={}, 状态=QUEUED",
                    taskId, name, rule, timeRange);

            return taskId;
        } catch (Exception e) {
            log.error("发布搜索任务失败: 名称={}, 错误={}", name, e.getMessage(), e);
            throw new RuntimeException("发布任务失败: " + e.getMessage(), e);
        }
    }
}