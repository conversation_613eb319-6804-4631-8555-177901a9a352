# Compiled class files
*.class

# Log files
*.log
logs/
log/

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# JVM crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
.idea_modules/

# Eclipse
.settings/
.classpath
.project
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/
.code-workspace

# OS X
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Project specific
application-local.properties
application-dev.properties
application-prod.properties
application-test.properties
*.env

# Cookie files
cert_common.txt
.cert_common_path

# Spring Boot 
.spring-boot-devtools.restart.trigger

# Playwright
.playwright/

# Node
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log

# Temporary files
temp/
tmp/

# Project specific temp files
/PlaywrightUtils.java
/response.txt
/send-task.bat
