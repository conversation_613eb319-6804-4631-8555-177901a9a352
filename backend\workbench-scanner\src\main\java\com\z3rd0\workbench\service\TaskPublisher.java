package com.z3rd0.workbench.service;

import com.z3rd0.workbench.config.RabbitMQConfig;
import com.z3rd0.common.model.Task;
import com.z3rd0.workbench.repository.TaskRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务发布服务
 * 负责将任务发送到RabbitMQ队列
 */
@Service
public class TaskPublisher {
    
    private static final Logger logger = LoggerFactory.getLogger(TaskPublisher.class);
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Autowired
    private TaskRepository taskRepository;
    
    /**
     * 设置RabbitTemplate - 用于主函数测试
     */
    public void setRabbitTemplate(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }
    
    /**
     * 发布搜索任务到RabbitMQ
     * @param name 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     */
    public void publishSearchTask(String name, String rule, String timeRange) {
        publishSearchTaskWithId(null, name, rule, timeRange);
    }
    
    /**
     * 发布搜索任务到RabbitMQ（带任务ID）
     * 如果任务ID为null，则创建新任务并获取ID
     * @param taskId 任务ID
     * @param name 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @return 任务ID
     */
    public Long publishSearchTaskWithId(Long taskId, String name, String rule, String timeRange) {
        return publishSearchTaskWithPriority(taskId, name, rule, timeRange, 5); // 默认优先级5
    }

    /**
     * 发布搜索任务到RabbitMQ（带任务ID和优先级）
     * @param taskId 任务ID
     * @param name 任务名称
     * @param rule 搜索规则
     * @param timeRange 时间范围
     * @param priority 任务优先级（1-10，数字越小优先级越高）
     * @return 任务ID
     */
    public Long publishSearchTaskWithPriority(Long taskId, String name, String rule, String timeRange, Integer priority) {
        try {
            // 如果没有提供taskId，则创建新任务
            Task task;
            if (taskId == null) {
                task = new Task();
                task.setName(name);
                task.setRule(rule);
                task.setPriority(priority != null ? priority : 5); // 设置优先级，默认为5
                // 不设置筛选范围，这个由执行时更新
                task.setStatus("PENDING");
                task = taskRepository.save(task);
                taskId = task.getId();
                logger.info("已创建新任务: ID={}, 名称={}, 优先级={}", taskId, name, task.getPriority());
            } else {
                // 更新现有任务状态
                final Long finalTaskId = taskId;  // 创建一个final变量供lambda使用
                task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new RuntimeException("找不到指定ID的任务: " + finalTaskId));
                task.setStatus("PENDING");
                if (priority != null) {
                    task.setPriority(priority);
                }
                task = taskRepository.save(task);
                logger.info("已更新现有任务: ID={}, 名称={}, 优先级={}", taskId, name, task.getPriority());
            }
            
            // 准备任务信息
            Map<String, Object> headers = new HashMap<>();
            headers.put("taskId", taskId);
            headers.put("name", name);
            headers.put("rule", rule);
            headers.put("timeRange", timeRange);
            headers.put("createdAt", LocalDateTime.now().toString());
            headers.put("priority", task.getPriority());

            // 发送任务到队列
            logger.info("发布搜索任务: ID={}, 名称={}, 规则={}, 优先级={}", taskId, name, rule, task.getPriority());

            MessageProperties properties = new MessageProperties();
            properties.setContentType("application/json");
            properties.setHeaders(headers);

            // 创建有效的JSON格式消息体
            String messageBody = String.format("{\"id\":%d,\"name\":\"%s\",\"priority\":%d}", taskId, name, task.getPriority());

            Message message = new Message(
                    messageBody.getBytes(StandardCharsets.UTF_8), properties);

            // 根据优先级选择队列
            String exchangeName, routingKey;
            if (task.getPriority() != null && task.getPriority() <= 3) {
                // 高优先级任务使用优先级队列
                exchangeName = RabbitMQConfig.PRIORITY_EXCHANGE_NAME;
                routingKey = RabbitMQConfig.PRIORITY_ROUTING_KEY;
                properties.setPriority(task.getPriority()); // 设置消息优先级
                logger.info("高优先级任务发送到优先级队列: ID={}, 优先级={}", taskId, task.getPriority());
            } else {
                // 普通优先级任务使用普通队列
                exchangeName = RabbitMQConfig.EXCHANGE_NAME;
                routingKey = RabbitMQConfig.ROUTING_KEY;
                logger.info("普通优先级任务发送到普通队列: ID={}, 优先级={}", taskId, task.getPriority());
            }

            rabbitTemplate.send(exchangeName, routingKey, message);
            logger.info("任务已发送到队列: ID={}, 名称={}, 队列类型={}", taskId, name,
                       task.getPriority() <= 3 ? "优先级队列" : "普通队列");
            
            return taskId;
        } catch (Exception e) {
            logger.error("发布搜索任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("发布搜索任务失败", e);
        }
    }

    /**
     * 请使用SendTaskTest.java或通过Spring环境启动的应用程序进行测试
     * 
     * 单独使用main方法会导致RabbitMQ身份验证失败:
     * 错误: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused
     */
    public static void main(String[] args) {
        System.out.println("==========================================");
        System.out.println("警告: 该方法无法直接使用, 请使用以下方式之一测试:");
        System.out.println("1. 使用 src/test/java/com/z3rd0/workbench/SendTaskTest.java");
        System.out.println("2. 或通过 REST API: http://localhost:8888/api/test/send-task");
        System.out.println("3. 或启动应用程序并使用依赖注入方式使用TaskPublisher");
        System.out.println("==========================================");
        System.out.println("\n以下为示例参数:");
        
        // 时间范围 - 今天的数据
        String timeRange = "2025-04-18 00:00:00,2025-04-18 23:59:59";
        
        // 示例1: 国网相关站点搜索
        String task1Rule = "title: \"国网\" AND country: \"China\" AND -province_cn: \"香港\"";
        System.out.println("搜索规则1: " + task1Rule);
        
        // 示例2: 电网相关站点搜索  
        String task2Rule = "title: \"电网\" AND country: \"China\" AND -province_cn: \"香港\"";
        System.out.println("搜索规则2: " + task2Rule);
        
        System.out.println("时间范围: " + timeRange);
    }
} 