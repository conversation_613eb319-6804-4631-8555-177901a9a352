<template>
  <div class="realtime-task-status">
    <!-- 连接状态 -->
    <div class="connection-indicator">
      <el-tag :type="wsConnected ? 'success' : 'danger'" size="small">
        <el-icon><Connection /></el-icon>
        {{ wsConnected ? '实时连接' : '连接断开' }}
      </el-tag>
    </div>

    <!-- 任务进度显示 -->
    <div v-if="taskProgress" class="progress-section">
      <div class="progress-header">
        <h4>任务执行进度</h4>
        <el-tag type="info">任务ID: {{ taskProgress.taskId }}</el-tag>
      </div>

      <div class="progress-content">
        <div class="progress-info">
          <span class="step-info">
            步骤 {{ taskProgress.currentStep }} / {{ taskProgress.totalSteps }}
          </span>
          <span class="step-name">{{ taskProgress.stepName }}</span>
        </div>

        <el-progress
          :percentage="taskProgress.percentage"
          :stroke-width="12"
          :color="getProgressColor(taskProgress.percentage)"
        >
          <template #default="{ percentage }">
            <span class="progress-text">{{ percentage.toFixed(1) }}%</span>
          </template>
        </el-progress>

        <div class="progress-description">
          {{ taskProgress.description }}
        </div>
      </div>
    </div>

    <!-- 任务状态变更历史 -->
    <div class="status-history">
      <div class="history-header">
        <h4>状态变更历史</h4>
        <el-button
          :icon="Delete"
          size="small"
          @click="clearHistory"
          v-if="statusHistory.length > 0"
        >
          清空
        </el-button>
      </div>

      <el-timeline v-if="statusHistory.length > 0">
        <el-timeline-item
          v-for="(item, index) in statusHistory"
          :key="index"
          :timestamp="item.timestamp"
          :type="getTimelineType(item.type)"
          :icon="getTimelineIcon(item.type)"
        >
          <div class="timeline-content">
            <div class="timeline-title">
              <span v-if="item.type === 'STATUS_CHANGE'">
                任务状态变更: {{ item.taskName }}
              </span>
              <span v-else-if="item.type === 'PROGRESS_UPDATE'">
                进度更新: {{ item.taskName }}
              </span>
              <span v-else-if="item.type === 'LOG_MESSAGE'">
                日志消息: {{ item.taskName }}
              </span>
              <span v-else>
                {{ item.type }}
              </span>
            </div>

            <div class="timeline-details">
              <div v-if="item.type === 'STATUS_CHANGE'" class="status-change">
                <el-tag v-if="item.oldStatus" :type="getStatusType(item.oldStatus)" size="small">
                  {{ item.oldStatus }}
                </el-tag>
                <el-icon v-if="item.oldStatus"><Right /></el-icon>
                <el-tag :type="getStatusType(item.newStatus)" size="small">
                  {{ item.newStatus }}
                </el-tag>
              </div>

              <div v-else-if="item.type === 'PROGRESS_UPDATE'" class="progress-update">
                <span>{{ item.progress?.stepName }}</span>
                <el-progress
                  :percentage="item.progress?.percentage || 0"
                  :stroke-width="6"
                  :show-text="false"
                  style="margin: 4px 0;"
                />
                <span class="progress-desc">{{ item.progress?.description }}</span>
              </div>

              <div v-else-if="item.type === 'LOG_MESSAGE'" class="log-message">
                <el-tag :type="getLogLevelType(item.level)" size="small">
                  {{ item.level }}
                </el-tag>
                <span class="log-content">{{ item.message }}</span>
              </div>

              <div v-if="item.errorMessage" class="error-message">
                <el-text type="danger" size="small">{{ item.errorMessage }}</el-text>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>

      <el-empty v-else description="暂无状态变更记录" :image-size="80" />
    </div>

    <!-- 系统通知 -->
    <div v-if="systemNotifications.length > 0" class="notifications-section">
      <div class="notifications-header">
        <h4>系统通知</h4>
        <el-button
          :icon="Delete"
          size="small"
          @click="clearNotifications"
        >
          清空
        </el-button>
      </div>

      <div class="notifications-list">
        <el-alert
          v-for="(notification, index) in systemNotifications"
          :key="index"
          :title="notification.title"
          :description="notification.content"
          :type="notification.level"
          :closable="true"
          @close="removeNotification(index)"
          style="margin-bottom: 8px;"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Connection, Delete, Right, VideoPlay, Warning, InfoFilled } from '@element-plus/icons-vue'
import {
  webSocketService,
  type TaskStatusMessage,
  type TaskProgress,
  type SystemNotification
} from '@/utils/websocket'

interface Props {
  taskId?: number
}

const props = defineProps<Props>()

// 响应式数据
const wsConnected = ref(false)
const taskProgress = ref<TaskProgress | null>(null)
const statusHistory = ref<TaskStatusMessage[]>([])
const systemNotifications = ref<SystemNotification[]>([])

// WebSocket监听器取消函数
let unsubscribeConnection: (() => void) | null = null
let unsubscribeTaskStatus: (() => void) | null = null
let unsubscribeTaskProgress: (() => void) | null = null
let unsubscribeSystemNotification: (() => void) | null = null

// 页面可见性处理
import { onVisibilityChange } from '@/utils/pageVisibility'

let visibilityUnsubscribe: (() => void) | null = null

// 生命周期
onMounted(() => {
  initializeWebSocket()

  // 监听页面可见性变化
  visibilityUnsubscribe = onVisibilityChange((visible) => {
    console.log(`[RealTimeTaskStatus] 页面可见性变化: ${visible ? '可见' : '隐藏'}`)

    if (visible) {
      // 页面重新可见时，确保WebSocket连接正常
      if (!webSocketService.isConnected()) {
        console.log('[RealTimeTaskStatus] 页面重新可见，重新连接WebSocket')
        webSocketService.connect()
      }
    }
    // 页面隐藏时不主动断开连接，让WebSocket服务自己管理
  })
})

onUnmounted(() => {
  cleanup()

  // 清理可见性监听器
  if (visibilityUnsubscribe) {
    visibilityUnsubscribe()
    visibilityUnsubscribe = null
  }
})

// 初始化WebSocket监听
const initializeWebSocket = () => {
  // 监听连接状态
  unsubscribeConnection = webSocketService.onConnectionStatus((connected) => {
    wsConnected.value = connected
  })

  // 监听任务状态变更
  unsubscribeTaskStatus = webSocketService.onTaskStatus((message) => {
    // 如果指定了taskId，只监听该任务的状态
    if (props.taskId && message.taskId !== props.taskId) {
      return
    }

    // 添加到历史记录
    statusHistory.value.unshift({
      ...message,
      timestamp: new Date().toLocaleString()
    })

    // 保持最多50条记录
    if (statusHistory.value.length > 50) {
      statusHistory.value = statusHistory.value.slice(0, 50)
    }
  })

  // 监听任务进度更新
  unsubscribeTaskProgress = webSocketService.onTaskProgress((progress) => {
    // 如果指定了taskId，只监听该任务的进度
    if (props.taskId && progress.taskId !== props.taskId) {
      return
    }

    taskProgress.value = progress

    // 同时添加到状态历史
    statusHistory.value.unshift({
      type: 'PROGRESS_UPDATE',
      taskId: progress.taskId,
      progress: progress,
      timestamp: new Date().toLocaleString()
    })
  })

  // 监听系统通知
  unsubscribeSystemNotification = webSocketService.onSystemNotification((notification) => {
    systemNotifications.value.unshift(notification)

    // 保持最多10条通知
    if (systemNotifications.value.length > 10) {
      systemNotifications.value = systemNotifications.value.slice(0, 10)
    }
  })

  // 如果指定了taskId，订阅该任务
  if (props.taskId) {
    webSocketService.subscribeToTask(props.taskId)
  }

  wsConnected.value = webSocketService.isConnected()
}

// 清理资源
const cleanup = () => {
  if (unsubscribeConnection) unsubscribeConnection()
  if (unsubscribeTaskStatus) unsubscribeTaskStatus()
  if (unsubscribeTaskProgress) unsubscribeTaskProgress()
  if (unsubscribeSystemNotification) unsubscribeSystemNotification()

  if (props.taskId) {
    webSocketService.unsubscribeFromTask(props.taskId)
  }
}

// 清空历史记录
const clearHistory = () => {
  statusHistory.value = []
  taskProgress.value = null
}

// 清空通知
const clearNotifications = () => {
  systemNotifications.value = []
}

// 移除单个通知
const removeNotification = (index: number) => {
  systemNotifications.value.splice(index, 1)
}

// 工具方法
const getProgressColor = (percentage: number): string => {
  if (percentage >= 100) return '#67C23A'
  if (percentage >= 80) return '#409EFF'
  if (percentage >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getTimelineType = (type: string): string => {
  switch (type) {
    case 'STATUS_CHANGE': return 'primary'
    case 'PROGRESS_UPDATE': return 'success'
    case 'LOG_MESSAGE': return 'info'
    default: return 'info'
  }
}

const getTimelineIcon = (type: string) => {
  switch (type) {
    case 'STATUS_CHANGE': return VideoPlay
    case 'PROGRESS_UPDATE': return InfoFilled
    case 'LOG_MESSAGE': return Warning
    default: return InfoFilled
  }
}

const getStatusType = (status: string): string => {
  switch (status) {
    case 'COMPLETED': return 'success'
    case 'FAILED': return 'danger'
    case 'PROCESSING': return 'warning'
    case 'SKIPPED': return 'info'
    default: return ''
  }
}

const getLogLevelType = (level: string): string => {
  switch (level) {
    case 'ERROR': return 'danger'
    case 'WARN': return 'warning'
    case 'INFO': return 'success'
    default: return 'info'
  }
}
</script>

<style scoped>
.realtime-task-status {
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
}

.connection-indicator {
  margin-bottom: 16px;
  text-align: right;
}

.progress-section {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.progress-content {
  space-y: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-info {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.step-name {
  color: var(--el-text-color-regular);
}

.progress-text {
  font-size: 12px;
  font-weight: 500;
}

.progress-description {
  margin-top: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.status-history,
.notifications-section {
  margin-bottom: 24px;
}

.history-header,
.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.history-header h4,
.notifications-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.timeline-content {
  padding-left: 8px;
}

.timeline-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.timeline-details {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.status-change {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-update {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.log-message {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-content {
  flex: 1;
}

.error-message {
  margin-top: 4px;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}
</style>
