-- 为任务执行详情表添加资源监控相关字段
-- 用于记录任务执行过程中的内存使用情况和浏览器重启次数

ALTER TABLE task_execution_details 
ADD COLUMN peak_memory_usage_mb BIGINT DEFAULT 0 COMMENT '峰值内存使用量(MB)';

ALTER TABLE task_execution_details 
ADD COLUMN avg_memory_usage_mb BIGINT DEFAULT 0 COMMENT '平均内存使用量(MB)';

ALTER TABLE task_execution_details 
ADD COLUMN browser_restart_count INT DEFAULT 0 COMMENT '浏览器重启次数';

-- 为现有记录设置默认值
UPDATE task_execution_details SET peak_memory_usage_mb = 0 WHERE peak_memory_usage_mb IS NULL;
UPDATE task_execution_details SET avg_memory_usage_mb = 0 WHERE avg_memory_usage_mb IS NULL;
UPDATE task_execution_details SET browser_restart_count = 0 WHERE browser_restart_count IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_execution_details_memory_usage ON task_execution_details(peak_memory_usage_mb);
CREATE INDEX idx_execution_details_browser_restart ON task_execution_details(browser_restart_count);
