package com.z3rd0.common.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "search_results",
       uniqueConstraints = {
           // 注意：由于title和path字段较长，这里不设置唯一约束
           // 如需要唯一性检查，建议在业务层处理或使用复合索引
       },
       indexes = {
           @Index(name = "idx_task_name", columnList = "task_name"),
           @Index(name = "idx_ip", columnList = "ip"),
           @Index(name = "idx_domain", columnList = "domain"),
           @Index(name = "idx_asn", columnList = "asn"),
           @Index(name = "idx_status_code", columnList = "status_code"),
           @Index(name = "idx_server", columnList = "server"),
           @Index(name = "idx_icp_licence", columnList = "icp_licence"),
           @Index(name = "idx_location_country", columnList = "location_country"),
           @Index(name = "idx_created_at", columnList = "created_at"),
           @Index(name = "idx_is_read", columnList = "is_read"),
           @Index(name = "idx_is_excluded", columnList = "is_excluded"),
           @Index(name = "idx_original_id", columnList = "original_id")
       })
public class SearchResult {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 基础标识信息
    @Column(name = "original_id", length = 100, nullable = false)
    private String originalId;

    @Column(name = "task_name", length = 100, nullable = false)
    private String taskName;

    @Column(columnDefinition = "TEXT")
    private String rule;

    // 网络基础信息
    @Column(length = 45, nullable = false)  // 支持IPv6地址
    private String ip;

    /**
     * 关联的IP资产ID
     */
    @Column(name = "ip_asset_id")
    private Long ipAssetId;

    @Column(length = 10)
    private String port;

    @Column(length = 500)
    private String domain;

    @Column(length = 200)
    private String hostname;

    @Column(length = 20)
    private String transport;

    @Column(length = 200)
    private String org;

    // 网络扩展信息
    @Column(name = "asn")
    private Integer asn;

    @Column(name = "is_ipv6", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isIpv6;

    @Column(name = "sys_tag", length = 500)
    private String sysTag;

    // 网站基础信息
    @Column(length = 1000)
    private String title;

    @Column(length = 2000)
    private String url;

    @Column(length = 1000)
    private String path;

    @Column(length = 200)
    private String host;

    @Column(length = 100)
    private String server;

    @Column(name = "x_powered_by", length = 100)
    private String xPoweredBy;

    @Column(name = "status_code")
    private Integer statusCode;

    // 地理位置信息
    @Column(name = "location_isp", length = 100)
    private String locationIsp;

    @Column(name = "location_scene", length = 100)
    private String locationScene;

    @Column(name = "location_country", length = 100)
    private String locationCountry;

    @Column(name = "location_province", length = 100)
    private String locationProvince;

    @Column(name = "location_city", length = 100)
    private String locationCity;

    // 保留的原有字段
    @Column(name = "http_load_url", length = 1000)
    private String httpLoadUrl;

    @Column(name = "page_type", length = 100)
    private String pageType;

    // 技术栈和备案信息
    @Column(name = "tech_stack", columnDefinition = "JSON")
    private String techStack;

    @Column(name = "icp_licence", length = 100)
    private String icpLicence;

    @Column(name = "icp_unit", length = 200)
    private String icpUnit;

    @Column(name = "icp_nature", length = 50)
    private String icpNature;

    // 网站元信息
    @Column(name = "meta_keywords", columnDefinition = "TEXT")
    private String metaKeywords;

    @Column(name = "favicon_hash", length = 64)
    private String faviconHash;

    @Column(name = "favicon_url", length = 500)
    private String faviconUrl;

    // 时间信息
    @Column(name = "data_time", length = 30)
    private String time;

    @Column(name = "created_at", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private LocalDateTime createdAt;

    @Column(name = "in_time_range", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean inTimeRange;

    // 内容信息
    @Column(columnDefinition = "LONGTEXT")
    private String content;

    @Column(columnDefinition = "TEXT")
    private String summary;

    // 管理字段
    @Column(name = "is_read", columnDefinition = "TINYINT DEFAULT 0")
    private Integer isRead;

    @Column(columnDefinition = "TEXT")
    private String note;

    @Column(name = "is_excluded", columnDefinition = "TINYINT DEFAULT 0")
    private Integer isExcluded;
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (isRead == null) {
            isRead = 0;
        }
        if (note == null) {
            note = "";
        }
        if (isExcluded == null) {
            isExcluded = 0;
        }
        if (inTimeRange == null) {
            inTimeRange = false;
        }
        if (isIpv6 == null) {
            isIpv6 = false;
        }
    }

    // 兼容性方法 - 保持向后兼容
    public void setSearchRule(String searchRule) {
        this.rule = searchRule;
    }

    public String getSearchRule() {
        return this.rule;
    }

    public void setCreatedTime(String createdTime) {
        this.time = createdTime;
    }

    public String getCreatedTime() {
        return this.time;
    }

    public void setInTimeRange(boolean inTimeRange) {
        this.inTimeRange = inTimeRange;
    }
}