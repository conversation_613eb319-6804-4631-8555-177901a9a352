package com.z3rd0.workbench.service;

import com.z3rd0.workbench.event.TaskDataStatsEvent;
import com.z3rd0.workbench.event.TaskLogEvent;
import com.z3rd0.workbench.event.TaskProgressEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 任务状态管理服务
 * 负责跟踪和管理任务的状态信息
 */
@Service
public class TaskStateManager {
    private static final Logger logger = LoggerFactory.getLogger(TaskStateManager.class);
    
    // 当前活跃任务信息
    private final ConcurrentHashMap<String, TaskState> taskStates = new ConcurrentHashMap<>();
    
    /**
     * 获取或创建任务状态
     */
    public TaskState getOrCreateTaskState(String taskName) {
        return taskStates.computeIfAbsent(taskName, name -> {
            logger.info("创建新的任务状态: {}", name);
            return new TaskState(name);
        });
    }
    
    /**
     * 根据任务名获取任务状态
     */
    public TaskState getTaskState(String taskName) {
        return taskStates.get(taskName);
    }
    
    /**
     * 移除任务状态
     */
    public void removeTaskState(String taskName) {
        taskStates.remove(taskName);
        logger.info("移除任务状态: {}", taskName);
    }
    
    /**
     * 重置任务状态
     */
    public void resetTaskState(String taskName) {
        TaskState state = getOrCreateTaskState(taskName);
        state.reset();
        logger.info("重置任务状态: {}", taskName);
    }
    
    /**
     * 获取当前活跃任务名称（排除已完成的任务）
     */
    public List<String> getActiveTaskNames() {
        List<String> activeNames = new ArrayList<>();
        for (Map.Entry<String, TaskState> entry : taskStates.entrySet()) {
            TaskState state = entry.getValue();
            if (state != null && !state.isCompleted()) {
                activeNames.add(entry.getKey());
            }
        }
        return activeNames;
    }

    /**
     * 获取当前正在执行的任务名称（最新的未完成任务）
     */
    public String getCurrentExecutingTaskName() {
        TaskState latestTask = null;
        String latestTaskName = null;

        for (Map.Entry<String, TaskState> entry : taskStates.entrySet()) {
            TaskState state = entry.getValue();
            if (state != null && !state.isCompleted()) {
                if (latestTask == null || state.getStartTime().isAfter(latestTask.getStartTime())) {
                    latestTask = state;
                    latestTaskName = entry.getKey();
                }
            }
        }

        return latestTaskName;
    }

    /**
     * 获取所有任务名称（包括已完成的）
     */
    public List<String> getAllTaskNames() {
        return new ArrayList<>(taskStates.keySet());
    }

    /**
     * 清理已完成的任务状态（保留最近的几个）
     */
    public void cleanupCompletedTasks() {
        List<Map.Entry<String, TaskState>> completedTasks = new ArrayList<>();

        // 收集已完成的任务
        for (Map.Entry<String, TaskState> entry : taskStates.entrySet()) {
            TaskState state = entry.getValue();
            if (state != null && state.isCompleted()) {
                completedTasks.add(entry);
            }
        }

        // 按开始时间排序，保留最近的3个已完成任务
        completedTasks.sort((a, b) -> b.getValue().getStartTime().compareTo(a.getValue().getStartTime()));

        // 移除多余的已完成任务
        if (completedTasks.size() > 3) {
            for (int i = 3; i < completedTasks.size(); i++) {
                String taskName = completedTasks.get(i).getKey();
                taskStates.remove(taskName);
                logger.info("清理已完成任务状态: {}", taskName);
            }
        }
    }

    /**
     * 强制清理指定任务状态
     */
    public void forceRemoveTaskState(String taskName) {
        TaskState removed = taskStates.remove(taskName);
        if (removed != null) {
            logger.info("强制清理任务状态: {}", taskName);
        }
    }
    
    /**
     * 监听任务进度事件
     */
    @EventListener
    public void handleTaskProgressEvent(TaskProgressEvent event) {
        String taskName = event.getTaskName();
        TaskState state = getOrCreateTaskState(taskName);
        
        state.setProgress(event.getProgress());
        state.setProcessedItems(event.getProcessedItems());
        
        // 如果进度描述包含"完成"，标记任务完成
        if (event.getProgress() != null && 
            (event.getProgress().contains("完成") || event.getProgress().contains("已完成"))) {
            state.setCompleted(true);
        }
        
        logger.debug("更新任务进度 - 任务:{}, 进度:{}, 已处理:{}项", 
                    taskName, event.getProgress(), event.getProcessedItems());
    }
    
    /**
     * 监听任务数据统计事件
     */
    @EventListener
    public void handleTaskDataStatsEvent(TaskDataStatsEvent event) {
        String taskName = event.getTaskName();
        TaskState state = getOrCreateTaskState(taskName);
        
        state.setItemsInRange(event.getItemsInRange());
        state.setItemsOutOfRange(event.getItemsOutOfRange());
        state.setDuplicateItems(event.getDuplicateItems());
        
        logger.debug("更新任务数据统计 - 任务:{}, 范围内:{}, 范围外:{}, 重复:{}",
                    taskName, event.getItemsInRange(), event.getItemsOutOfRange(), event.getDuplicateItems());
    }
    
    /**
     * 监听任务日志事件
     */
    @EventListener
    public void handleTaskLogEvent(TaskLogEvent event) {
        String taskName = event.getTaskName();
        TaskState state = getOrCreateTaskState(taskName);
        
        state.addLog(event.getTimestamp(), event.getMessage(), event.getType());
        
        logger.debug("添加任务日志 - 任务:{}, 消息:{}, 类型:{}", 
                    taskName, event.getMessage(), event.getType());
    }
    
    /**
     * 任务状态类
     * 存储单个任务的所有状态信息
     */
    public static class TaskState {
        private final String taskName;
        private LocalDateTime startTime;
        private String progress = "";
        private int processedItems = 0;
        private int currentPage = 0;
        private int totalPages = 0;
        private int itemsInRange = 0;
        private int itemsOutOfRange = 0;
        private int duplicateItems = 0;
        private String currentUrl = "";
        private int processingSpeed = 0;
        private int estimatedRemainingTime = 0;
        private boolean completed = false;
        private final ConcurrentLinkedQueue<LogEntry> logs = new ConcurrentLinkedQueue<>();
        
        public TaskState(String taskName) {
            this.taskName = taskName;
            this.startTime = LocalDateTime.now();
        }
        
        public void reset() {
            startTime = LocalDateTime.now();
            progress = "";
            processedItems = 0;
            currentPage = 0;
            totalPages = 0;
            itemsInRange = 0;
            itemsOutOfRange = 0;
            duplicateItems = 0;
            currentUrl = "";
            processingSpeed = 0;
            estimatedRemainingTime = 0;
            completed = false;
            logs.clear();
        }
        
        public void addLog(LocalDateTime timestamp, String message, String type) {
            logs.add(new LogEntry(timestamp, message, type));
        }
        
        public void updatePageInfo(int currentPage, int totalPages) {
            this.currentPage = currentPage;
            this.totalPages = totalPages;
        }
        
        public void updateCurrentUrl(String url) {
            this.currentUrl = url;
        }
        
        public String getTaskName() {
            return taskName;
        }
        
        public LocalDateTime getStartTime() {
            return startTime;
        }
        
        public void setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
        }
        
        public String getProgress() {
            return progress;
        }
        
        public void setProgress(String progress) {
            this.progress = progress;
        }
        
        public int getProcessedItems() {
            return processedItems;
        }
        
        public void setProcessedItems(int processedItems) {
            this.processedItems = processedItems;
            
            // 自动更新处理速度和预计剩余时间
            updateProcessingStats();
        }
        
        public int getCurrentPage() {
            return currentPage;
        }
        
        public int getTotalPages() {
            return totalPages;
        }
        
        public int getItemsInRange() {
            return itemsInRange;
        }
        
        public void setItemsInRange(int itemsInRange) {
            this.itemsInRange = itemsInRange;
        }
        
        public int getItemsOutOfRange() {
            return itemsOutOfRange;
        }
        
        public void setItemsOutOfRange(int itemsOutOfRange) {
            this.itemsOutOfRange = itemsOutOfRange;
        }
        
        public int getDuplicateItems() {
            return duplicateItems;
        }
        
        public void setDuplicateItems(int duplicateItems) {
            this.duplicateItems = duplicateItems;
        }
        
        /**
         * 更新数据统计信息
         * @param itemsInRange 范围内项目数
         * @param itemsOutOfRange 范围外项目数
         * @param duplicateItems 重复项目数
         */
        public void updateDataStats(int itemsInRange, int itemsOutOfRange, int duplicateItems) {
            this.itemsInRange = itemsInRange;
            this.itemsOutOfRange = itemsOutOfRange;
            this.duplicateItems = duplicateItems;
        }
        
        public String getCurrentUrl() {
            return currentUrl;
        }
        
        public int getProcessingSpeed() {
            return processingSpeed;
        }
        
        public int getEstimatedRemainingTime() {
            return estimatedRemainingTime;
        }
        
        public boolean isCompleted() {
            return completed;
        }
        
        public void setCompleted(boolean completed) {
            this.completed = completed;
        }
        
        public List<LogEntry> getLogs() {
            return new ArrayList<>(logs);
        }
        
        /**
         * 计算处理速度和预计剩余时间
         */
        private void updateProcessingStats() {
            if (startTime != null) {
                Duration duration = Duration.between(startTime, LocalDateTime.now());
                long durationSeconds = duration.getSeconds();
                
                // 如果运行时间大于1分钟且有处理项，计算速度
                if (durationSeconds > 60 && processedItems > 0) {
                    processingSpeed = (int)Math.round(processedItems / (durationSeconds / 60.0));
                    
                    // 预估剩余时间（假设总计有约50*totalPages条数据）
                    if (totalPages > 0 && currentPage > 0) {
                        int estimatedTotal = 50 * totalPages;
                        int remaining = estimatedTotal - processedItems;
                        if (processingSpeed > 0) {
                            estimatedRemainingTime = (remaining * 60) / processingSpeed;
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 日志条目
     */
    public static class LogEntry {
        private final LocalDateTime timestamp;
        private final String message;
        private final String type;
        
        public LogEntry(LocalDateTime timestamp, String message, String type) {
            this.timestamp = timestamp;
            this.message = message;
            this.type = type;
        }
        
        public LocalDateTime getTimestamp() {
            return timestamp;
        }
        
        public String getMessage() {
            return message;
        }
        
        public String getType() {
            return type;
        }
    }
} 