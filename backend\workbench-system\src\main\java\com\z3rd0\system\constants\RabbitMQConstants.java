package com.z3rd0.system.constants;

/**
 * RabbitMQ配置常量类
 * 统一管理所有RabbitMQ相关的队列、交换机、路由键配置
 * 确保system和scanner模块使用相同的配置，避免重复定义
 */
public final class RabbitMQConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private RabbitMQConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    // ==================== 任务队列配置 ====================
    
    /**
     * 主任务队列名称
     * 用于system模块向scanner模块发送任务
     */
    public static final String TASK_QUEUE_NAME = "task_queue";
    
    /**
     * 主任务交换机名称
     */
    public static final String TASK_EXCHANGE_NAME = "task_exchange";
    
    /**
     * 主任务路由键
     */
    public static final String TASK_ROUTING_KEY = "task_routing_key";

    // ==================== 任务更新队列配置 ====================
    
    /**
     * 任务更新队列名称
     * 用于scanner模块向system模块发送任务状态更新
     */
    public static final String TASK_UPDATE_QUEUE_NAME = "task_update_queue";
    
    /**
     * 任务更新交换机名称
     */
    public static final String TASK_UPDATE_EXCHANGE_NAME = "task_update_exchange";
    
    /**
     * 任务更新路由键
     */
    public static final String TASK_UPDATE_ROUTING_KEY = "task_update_routing_key";

    // ==================== 死信队列配置 ====================
    
    /**
     * 死信队列名称
     * 用于处理失败的任务消息
     */
    public static final String DEAD_LETTER_QUEUE_NAME = "task_dead_letter_queue";
    
    /**
     * 死信交换机名称
     */
    public static final String DEAD_LETTER_EXCHANGE_NAME = "task_dead_letter_exchange";
    
    /**
     * 死信路由键
     */
    public static final String DEAD_LETTER_ROUTING_KEY = "task_dead_letter_routing_key";

    // ==================== 消息属性配置 ====================
    
    /**
     * 消息内容类型
     */
    public static final String MESSAGE_CONTENT_TYPE = "application/json";
    
    /**
     * 消息编码
     */
    public static final String MESSAGE_ENCODING = "UTF-8";

    // ==================== 队列属性配置 ====================
    
    /**
     * 队列持久化标志
     */
    public static final boolean QUEUE_DURABLE = true;
    
    /**
     * 队列排他性标志
     */
    public static final boolean QUEUE_EXCLUSIVE = false;
    
    /**
     * 队列自动删除标志
     */
    public static final boolean QUEUE_AUTO_DELETE = false;

    // ==================== 重试配置 ====================
    
    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_COUNT = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    public static final long RETRY_INTERVAL_MS = 5000L;
    
    /**
     * 重试倍数
     */
    public static final double RETRY_MULTIPLIER = 1.5;

    // ==================== 消息头常量 ====================
    
    /**
     * 任务ID消息头
     */
    public static final String HEADER_TASK_ID = "taskId";
    
    /**
     * 任务名称消息头
     */
    public static final String HEADER_TASK_NAME = "name";
    
    /**
     * 搜索规则消息头
     */
    public static final String HEADER_TASK_RULE = "rule";
    
    /**
     * 时间范围消息头
     */
    public static final String HEADER_TIME_RANGE = "timeRange";
    
    /**
     * 创建时间消息头
     */
    public static final String HEADER_CREATED_AT = "createdAt";
    
    /**
     * 重试次数消息头
     */
    public static final String HEADER_RETRY_COUNT = "retryCount";

    // ==================== 消息动作类型 ====================
    
    /**
     * 更新任务执行信息动作
     */
    public static final String ACTION_UPDATE_EXECUTION_INFO = "UPDATE_EXECUTION_INFO";
    
    /**
     * 任务完成动作
     */
    public static final String ACTION_TASK_COMPLETED = "TASK_COMPLETED";
    
    /**
     * 任务失败动作
     */
    public static final String ACTION_TASK_FAILED = "TASK_FAILED";
}
