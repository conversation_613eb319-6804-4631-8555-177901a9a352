package com.z3rd0.system.repository;

import com.z3rd0.common.model.TaskExecutionDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务执行详情仓库接口 - System模块版本
 * 提供对TaskExecutionDetail实体的数据访问操作
 * 用于system模块查询和展示任务执行详情
 */
@Repository
public interface TaskExecutionDetailRepository extends JpaRepository<TaskExecutionDetail, Long> {
    
    /**
     * 根据任务名称查找执行详情
     * @param taskName 任务名称
     * @return 执行详情列表，按开始时间降序排序
     */
    List<TaskExecutionDetail> findByTaskNameOrderByStartTimeDesc(String taskName);
    
    /**
     * 根据任务名称分页查找执行详情
     * @param taskName 任务名称
     * @param pageable 分页参数
     * @return 执行详情分页结果
     */
    Page<TaskExecutionDetail> findByTaskNameOrderByStartTimeDesc(String taskName, Pageable pageable);
    
    /**
     * 根据任务ID查找执行详情
     * @param taskId 任务ID
     * @return 执行详情
     */
    Optional<TaskExecutionDetail> findByTaskId(Long taskId);
    
    /**
     * 根据时间范围查找执行详情
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行详情列表，按开始时间降序排序
     */
    List<TaskExecutionDetail> findByStartTimeBetweenOrderByStartTimeDesc(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据时间范围分页查找执行详情
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 执行详情分页结果
     */
    Page<TaskExecutionDetail> findByStartTimeBetweenOrderByStartTimeDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据任务名称和时间范围查找执行详情
     * @param taskName 任务名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行详情列表，按开始时间降序排序
     */
    List<TaskExecutionDetail> findByTaskNameAndStartTimeBetweenOrderByStartTimeDesc(
            String taskName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据任务名称和时间范围分页查找执行详情
     * @param taskName 任务名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 执行详情分页结果
     */
    Page<TaskExecutionDetail> findByTaskNameAndStartTimeBetweenOrderByStartTimeDesc(
            String taskName, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据执行状态查找执行详情
     * @param executionStatus 执行状态
     * @return 执行详情列表
     */
    List<TaskExecutionDetail> findByExecutionStatusOrderByStartTimeDesc(String executionStatus);
    
    /**
     * 查找所有不同的任务名称
     * @return 任务名称列表
     */
    @Query("SELECT DISTINCT ted.taskName FROM TaskExecutionDetail ted ORDER BY ted.taskName")
    List<String> findAllDistinctTaskNames();
    
    /**
     * 统计指定任务的执行次数
     * @param taskName 任务名称
     * @return 执行次数
     */
    Long countByTaskName(String taskName);
    
    /**
     * 统计指定时间范围内的执行次数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行次数
     */
    Long countByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取指定任务的最新执行详情
     * @param taskName 任务名称
     * @return 最新执行详情
     */
    Optional<TaskExecutionDetail> findFirstByTaskNameOrderByStartTimeDesc(String taskName);
    
    /**
     * 获取指定任务的平均执行时长（秒）
     * @param taskName 任务名称
     * @return 平均执行时长
     */
    @Query("SELECT AVG(ted.executionDurationSeconds) FROM TaskExecutionDetail ted WHERE ted.taskName = :taskName AND ted.executionDurationSeconds IS NOT NULL")
    Double getAverageExecutionDurationByTaskName(@Param("taskName") String taskName);
    
    /**
     * 获取指定任务的总有效结果数
     * @param taskName 任务名称
     * @return 总有效结果数
     */
    @Query("SELECT SUM(ted.validResults) FROM TaskExecutionDetail ted WHERE ted.taskName = :taskName")
    Long getTotalValidResultsByTaskName(@Param("taskName") String taskName);
    
    /**
     * 获取最近N天的执行统计
     * @param startDate 开始日期
     * @return 执行详情列表
     */
    @Query("SELECT ted FROM TaskExecutionDetail ted WHERE ted.startTime >= :startDate ORDER BY ted.startTime DESC")
    List<TaskExecutionDetail> findRecentExecutions(@Param("startDate") LocalDateTime startDate);

    /**
     * 根据执行状态统计执行记录数
     * @param executionStatus 执行状态
     * @return 执行记录数
     */
    Long countByExecutionStatus(String executionStatus);
}
