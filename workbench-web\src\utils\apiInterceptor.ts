/**
 * API拦截器
 * 统一处理API请求和响应，提供容错机制
 */

import { ElMessage } from "element-plus";

// 存储原始的fetch函数
const originalFetch = window.fetch;

// 请求计数器，用于避免重复错误提示
const errorCounts = new Map<string, number>();
const ERROR_THRESHOLD = 3; // 错误阈值，超过此次数后不再提示

/**
 * 重置错误计数
 */
function resetErrorCount(url: string) {
  errorCounts.delete(url);
}

/**
 * 增加错误计数
 */
function incrementErrorCount(url: string): number {
  const count = errorCounts.get(url) || 0;
  const newCount = count + 1;
  errorCounts.set(url, newCount);
  return newCount;
}

/**
 * 检查是否应该显示错误提示
 */
function shouldShowError(url: string): boolean {
  const count = errorCounts.get(url) || 0;
  return count < ERROR_THRESHOLD;
}

/**
 * 增强的fetch函数
 */
async function enhancedFetch(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
  const url = typeof input === 'string' ? input : input.toString();
  
  try {
    // 设置默认超时时间
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
    
    const config: RequestInit = {
      ...init,
      signal: init?.signal || controller.signal,
    };

    // 添加默认headers
    if (!config.headers) {
      config.headers = {};
    }
    
    const headers = config.headers as Record<string, string>;
    if (!headers['Content-Type'] && (config.method === 'POST' || config.method === 'PUT')) {
      headers['Content-Type'] = 'application/json';
    }

    const response = await originalFetch(input, config);
    clearTimeout(timeoutId);

    // 请求成功，重置错误计数
    if (response.ok) {
      resetErrorCount(url);
    } else {
      // HTTP错误状态码
      const errorCount = incrementErrorCount(url);
      
      if (shouldShowError(url)) {
        let errorMessage = `请求失败 (${response.status})`;
        
        switch (response.status) {
          case 400:
            errorMessage = "请求参数错误";
            break;
          case 401:
            errorMessage = "未授权，请重新登录";
            break;
          case 403:
            errorMessage = "权限不足";
            break;
          case 404:
            errorMessage = "请求的资源不存在";
            break;
          case 500:
            errorMessage = "服务器内部错误";
            break;
          case 502:
            errorMessage = "网关错误";
            break;
          case 503:
            errorMessage = "服务暂时不可用";
            break;
        }

        // 只在前几次错误时显示提示
        if (errorCount === 1) {
          ElMessage.error(errorMessage);
        } else if (errorCount === ERROR_THRESHOLD) {
          ElMessage.warning(`${url} 连续请求失败，将不再提示错误`);
        }
      }
    }

    return response;
    
  } catch (error: any) {
    const errorCount = incrementErrorCount(url);
    
    if (shouldShowError(url)) {
      let errorMessage = "网络请求失败";
      
      if (error.name === 'AbortError') {
        errorMessage = "请求超时";
      } else if (error.message) {
        errorMessage = error.message;
      }

      // 只在前几次错误时显示提示
      if (errorCount === 1) {
        ElMessage.error(errorMessage);
      } else if (errorCount === ERROR_THRESHOLD) {
        ElMessage.warning(`${url} 连续请求失败，将不再提示错误`);
      }
    }
    
    throw error;
  }
}

/**
 * 安装API拦截器
 */
export function installApiInterceptor(): void {
  // 替换全局fetch函数
  window.fetch = enhancedFetch;
  
  console.log('API拦截器已安装');
}

/**
 * 卸载API拦截器
 */
export function uninstallApiInterceptor(): void {
  // 恢复原始fetch函数
  window.fetch = originalFetch;
  
  console.log('API拦截器已卸载');
}

/**
 * 清除所有错误计数
 */
export function clearErrorCounts(): void {
  errorCounts.clear();
  console.log('错误计数已清除');
}

/**
 * 获取错误统计信息
 */
export function getErrorStats(): Record<string, number> {
  const stats: Record<string, number> = {};
  errorCounts.forEach((count, url) => {
    stats[url] = count;
  });
  return stats;
}

/**
 * 创建一个带有重试机制的fetch函数
 */
export function createRetryFetch(maxRetries = 3, retryDelay = 1000) {
  return async function retryFetch(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await enhancedFetch(input, init);
        
        // 如果是服务器错误且还有重试次数，则重试
        if (response.status >= 500 && attempt < maxRetries) {
          throw new Error(`Server error: ${response.status}`);
        }
        
        return response;
      } catch (error: any) {
        lastError = error;
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === maxRetries) {
          throw error;
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
      }
    }
    
    throw lastError!;
  };
}
