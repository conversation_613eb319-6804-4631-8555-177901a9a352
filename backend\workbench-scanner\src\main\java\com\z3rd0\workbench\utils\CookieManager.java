package com.z3rd0.workbench.utils;

import com.z3rd0.common.model.CookieRecord;
import com.z3rd0.workbench.service.CookieService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Cookie管理工具类
 * 负责Cookie的保存、获取和验证
 */
@Component
public class CookieManager {
    
    private static final Logger logger = LoggerFactory.getLogger(CookieManager.class);
    
    // 静态CookieService实例，用于非Spring环境
    private static CookieService staticCookieService;
    
    /**
     * 设置CookieService实例
     */
    public static void setCookieService(CookieService cookieService) {
        CookieManager.staticCookieService = cookieService;
        logger.info("静态CookieService已设置");
    }
    
    /**
     * 直接保存cert_common到数据库
     */
    public boolean saveCertCommon(String certCommon) {
        logger.info("开始保存cert_common到数据库...");
        
        if (certCommon == null || certCommon.trim().isEmpty()) {
            logger.warn("certCommon值为空，不保存");
            return false;
        }
        
        try {
            // 使用静态cookieService
            if (staticCookieService != null) {
                boolean success = staticCookieService.updateCertCommon(certCommon);
                if (success) {
                    logger.info("成功使用静态cookieService保存cert_common");
                    return true;
                } else {
                    logger.warn("使用静态cookieService保存cert_common失败");
                }
            } else {
                // 尝试通过Spring上下文获取
                try {
                    org.springframework.context.ApplicationContext context = 
                        org.springframework.web.context.ContextLoader.getCurrentWebApplicationContext();
                    
                    if (context != null) {
                        CookieService service = context.getBean(CookieService.class);
                        if (service != null) {
                            boolean success = service.updateCertCommon(certCommon);
                            if (success) {
                                logger.info("成功通过Spring上下文获取CookieService保存cert_common");
                                return true;
                            } else {
                                logger.warn("通过Spring上下文获取CookieService保存cert_common失败");
                            }
                        } else {
                            logger.warn("无法通过Spring上下文获取CookieService");
                        }
                    } else {
                        logger.warn("无法获取Spring上下文");
                    }
                } catch (Exception e) {
                    logger.error("通过Spring上下文获取CookieService异常: {}", e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("保存cert_common到数据库异常: {}", e.getMessage(), e);
        }
        
        logger.error("无法保存cert_common到数据库");
        return false;
    }
    
    /**
     * 从CookieService获取有效的Cookie
     */
    public String getValidCookie() {
        if (staticCookieService != null) {
            Optional<CookieRecord> cookieRecordOptional = staticCookieService.getValidCookie();
            if (cookieRecordOptional.isPresent()) {
                CookieRecord cookieRecord = cookieRecordOptional.get();
                return cookieRecord.getCookie();
            }
        }
        return null;
    }
    
    /**
     * 从CookieService获取Cookie（不检查时效性，用于兼容）
     */
    public String getLatestCookie() {
        if (staticCookieService != null) {
            Optional<CookieRecord> cookieRecordOptional = staticCookieService.getLatestCookie();
            if (cookieRecordOptional.isPresent()) {
                CookieRecord cookieRecord = cookieRecordOptional.get();
                return cookieRecord.getCookie();
            }
        }
        return null;
    }
    
    /**
     * 从JSON字符串中提取cert_common值
     */
    public String extractCertCommonFromJson(String cookieJson) {
        try {
            // 首先检查是否是简单的cert_common=value格式
            if (cookieJson.contains("cert_common=")) {
                Pattern pattern = Pattern.compile("cert_common=([^;]+)");
                Matcher matcher = pattern.matcher(cookieJson);
                if (matcher.find()) {
                    String value = matcher.group(1);
                    logger.info("从cookie字符串中直接提取到cert_common值，长度: {}", value.length());
                    return value;
                }
            }
            
            // 尝试作为JSON数组解析
            try {
                JSONArray cookiesArray = new JSONArray(cookieJson);
                for (int i = 0; i < cookiesArray.length(); i++) {
                    JSONObject cookieObj = cookiesArray.getJSONObject(i);
                    if ("cert_common".equals(cookieObj.getString("name")) &&
                        cookieObj.getString("domain").contains("quake.360.net")) {
                        String value = cookieObj.getString("value");
                        logger.info("从JSON中提取到cert_common值，长度: {}", value.length());
                        return value;
                    }
                }
            } catch (Exception e) {
                logger.debug("作为JSON数组解析失败，尝试其他方式: {}", e.getMessage());
            }

            // 尝试作为单个JSON对象解析
            try {
                JSONObject cookieObj = new JSONObject(cookieJson);
                if ("cert_common".equals(cookieObj.optString("name")) &&
                    cookieObj.optString("domain", "").contains("quake.360.net")) {
                    String value = cookieObj.getString("value");
                    logger.info("从单个JSON对象中提取到cert_common值，长度: {}", value.length());
                    return value;
                }
            } catch (Exception e) {
                logger.debug("作为单个JSON对象解析失败: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("提取cert_common值时发生异常: {}", e.getMessage());
        }
        
        logger.warn("无法从提供的字符串中提取cert_common值");
        return null;
    }
    
    /**
     * 通过Spring上下文获取CookieService
     */
    private CookieService getCookieServiceFromContext() {
        try {
            org.springframework.context.ApplicationContext context = 
                org.springframework.web.context.ContextLoader.getCurrentWebApplicationContext();
            
            if (context != null) {
                return context.getBean(CookieService.class);
            }
        } catch (Exception e) {
            logger.warn("通过Spring上下文获取CookieService失败: {}", e.getMessage());
        }
        return null;
    }
}
