package com.z3rd0.system.service.impl;

import com.z3rd0.common.model.Task;
import com.z3rd0.system.repository.TaskRepository;
import com.z3rd0.system.service.TaskService;
import com.z3rd0.system.service.TaskStatusPushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 任务服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final TaskStatusPushService taskStatusPushService;

    @Override
    public Page<Task> findByConditions(String name, String status, String startTime, String endTime, Pageable pageable) {
        Specification<Task> spec = (root, query, cb) -> {
            Predicate p = cb.conjunction();
            
            // 任务名称模糊查询
            if (name != null && !name.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("name"), "%" + name.trim() + "%"));
            }
            
            // 任务状态精确查询
            if (status != null && !status.trim().isEmpty()) {
                p = cb.and(p, cb.equal(root.get("status"), status.trim()));
            }
            
            // 时间范围查询
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    LocalDateTime start = LocalDateTime.parse(startTime.trim(), formatter);
                    p = cb.and(p, cb.greaterThanOrEqualTo(root.get("createdAt"), start));
                } catch (Exception e) {
                    // 记录时间格式错误，但不影响查询
                    log.warn("开始时间格式错误，将忽略此条件: {}, 错误: {}", startTime, e.getMessage());
                }
            }

            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    LocalDateTime end = LocalDateTime.parse(endTime.trim(), formatter);
                    p = cb.and(p, cb.lessThanOrEqualTo(root.get("createdAt"), end));
                } catch (Exception e) {
                    // 记录时间格式错误，但不影响查询
                    log.warn("结束时间格式错误，将忽略此条件: {}, 错误: {}", endTime, e.getMessage());
                }
            }
            
            return p;
        };
        
        return taskRepository.findAll(spec, pageable);
    }

    @Override
    public Optional<Task> findById(Long id) {
        return taskRepository.findById(id);
    }

    @Override
    public Task save(Task task) {
        // 保存前记录旧状态
        String oldStatus = null;
        if (task.getId() != null) {
            Optional<Task> existingTask = taskRepository.findById(task.getId());
            if (existingTask.isPresent()) {
                oldStatus = existingTask.get().getStatus();
            }
        }

        // 保存任务
        Task savedTask = taskRepository.save(task);

        // 如果状态发生变化，推送状态更新
        if (oldStatus != null && !oldStatus.equals(savedTask.getStatus())) {
            taskStatusPushService.pushTaskStatusChange(savedTask, oldStatus, savedTask.getStatus());
        } else if (oldStatus == null && savedTask.getStatus() != null) {
            // 新任务创建
            taskStatusPushService.pushTaskStatusChange(savedTask, null, savedTask.getStatus());
        }

        return savedTask;
    }

    @Override
    public boolean existsById(Long id) {
        return taskRepository.existsById(id);
    }

    @Override
    public void deleteById(Long id) {
        taskRepository.deleteById(id);
    }

    @Override
    public List<Task> findAll() {
        return taskRepository.findAll();
    }

    @Override
    public List<Task> findByIds(List<Long> ids) {
        return taskRepository.findAllById(ids);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        taskRepository.deleteAllById(ids);
    }

    @Override
    public List<Task> saveAll(List<Task> tasks) {
        return taskRepository.saveAll(tasks);
    }
}
