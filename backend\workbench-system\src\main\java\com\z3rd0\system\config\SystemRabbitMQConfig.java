package com.z3rd0.system.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.z3rd0.system.constants.RabbitMQConstants;

/**
 * System模块RabbitMQ配置类
 * 主要负责任务发送和状态更新接收的配置
 * 与scanner模块保持一致的队列和交换机配置
 * 使用统一的常量类管理配置，避免重复定义
 */
@Configuration
public class SystemRabbitMQConfig {

    @Bean
    public DirectExchange taskExchange() {
        return new DirectExchange(RabbitMQConstants.TASK_EXCHANGE_NAME);
    }

    // 任务更新队列配置
    @Bean
    public Queue taskUpdateQueue() {
        return new Queue(RabbitMQConstants.TASK_UPDATE_QUEUE_NAME, RabbitMQConstants.QUEUE_DURABLE);
    }

    @Bean
    public DirectExchange taskUpdateExchange() {
        return new DirectExchange(RabbitMQConstants.TASK_UPDATE_EXCHANGE_NAME);
    }

    @Bean
    public Binding updateBinding() {
        return BindingBuilder.bind(taskUpdateQueue()).to(taskUpdateExchange()).with(RabbitMQConstants.TASK_UPDATE_ROUTING_KEY);
    }

    @Bean
    public MessageConverter jsonMessageConverter(ObjectMapper objectMapper) {
        // 使用JacksonConfig中已配置的ObjectMapper，并添加RabbitMQ特定配置
        ObjectMapper rabbitMQObjectMapper = objectMapper.copy();
        rabbitMQObjectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        rabbitMQObjectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        rabbitMQObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return new Jackson2JsonMessageConverter(rabbitMQObjectMapper);
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, MessageConverter messageConverter) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter);
        // 设置返回消息模式
        rabbitTemplate.setMandatory(true);
        return rabbitTemplate;
    }
}