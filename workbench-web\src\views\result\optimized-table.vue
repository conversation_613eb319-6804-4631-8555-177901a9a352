<template>
  <div class="result-table-container">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button-group v-if="props.selectedRows.length > 0">
          <el-button size="small" @click="handleBatchRead">
            <el-icon><View /></el-icon>
            批量标记已读
          </el-button>
          <el-button size="small" @click="handleBatchExclude">
            <el-icon><Remove /></el-icon>
            批量排除
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </el-button-group>
        <span v-if="props.selectedRows.length > 0" class="selection-info">
          已选择 {{ props.selectedRows.length }} 项
        </span>
      </div>

      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="toggleDensity">
            <el-icon><Operation /></el-icon>
            {{ density === "default" ? "紧凑" : "默认" }}
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="showColumnSettings = true">
            <el-icon><Setting /></el-icon>
            列设置
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 优化后的数据表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="results"
      :size="density"
      :border="true"
      :fit="true"
      :row-class-name="getRowClassName"
      :header-cell-style="{ fontWeight: 'bold', backgroundColor: '#fafafa' }"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 选择列 -->
      <el-table-column type="selection" width="50" align="center" />

      <!-- IP地址 - 核心字段 -->
      <el-table-column
        prop="ip"
        label="IP地址"
        min-width="90"
        align="center"
        fixed="left"
      >
        <template #default="{ row }">
          <div class="ip-cell" :class="getRowStatusClass(row)">
            <span class="ip-text">{{ row.ip }}</span>
            <el-tag v-if="row.isIpv6" type="info" size="small" class="ip-tag"
              >IPv6</el-tag
            >
          </div>
        </template>
      </el-table-column>

      <!-- 端口 -->
      <el-table-column
        prop="port"
        label="端口"
        min-width="70"
        width="90"
        align="center"
      >
        <template #default="{ row }">
          <div class="port-cell">
            <span class="port-text">{{ row.port || "-" }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 域名 -->
      <el-table-column
        prop="domain"
        label="域名"
        min-width="100"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span v-if="row.domain" class="domain-text">{{ row.domain }}</span>
          <span v-else class="text-placeholder">-</span>
        </template>
      </el-table-column>

      <!-- 标题 -->
      <el-table-column
        prop="title"
        label="标题"
        min-width="170"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span v-if="row.title" class="title-text">{{ row.title }}</span>
          <span v-else class="text-placeholder">-</span>
        </template>
      </el-table-column>

      <!-- IP归属（组织+位置） -->
      <el-table-column
        label="IP归属"
        min-width="85"
        width="85"
        align="center"
        class-name="detail-info-column"
      >
        <template #default="{ row }">
          <el-popover
            placement="top"
            width="300"
            trigger="hover"
            :disabled="!hasDetailInfo(row)"
          >
            <template #reference>
              <el-button
                v-if="hasDetailInfo(row)"
                size="small"
                type="info"
                plain
                circle
              >
                <el-icon><InfoFilled /></el-icon>
              </el-button>
              <span v-else class="text-placeholder">-</span>
            </template>

            <div class="detail-popover">
              <div v-if="row.org" class="detail-item">
                <span class="detail-label">组织:</span>
                <span class="detail-value">{{ row.org }}</span>
              </div>
              <div v-if="getLocationText(row)" class="detail-item">
                <span class="detail-label">位置:</span>
                <span class="detail-value">{{ getLocationText(row) }}</span>
              </div>
            </div>
          </el-popover>
        </template>
      </el-table-column>

      <!-- 标签 -->
      <el-table-column
        label="标签"
        min-width="60"
        max-width="120"
        align="center"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="tags-compact">
            <el-popover
              placement="top"
              width="250"
              trigger="hover"
              :disabled="!shouldShowPopover(row)"
            >
              <template #reference>
                <div class="tags-display">
                  <template v-if="hasAnyTags(row)">
                    <!-- 显示前2个系统标签 -->
                    <el-tag
                      v-for="tag in getDisplayTags(row).slice(0, 2)"
                      :key="tag"
                      type="warning"
                      size="small"
                      class="tag-item"
                    >
                      {{ tag }}
                    </el-tag>
                    <!-- 如果有更多标签，显示+N -->
                    <el-tag
                      v-if="getTotalTagCount(row) > 2"
                      type="info"
                      size="small"
                      class="tag-item more-tag"
                    >
                      +{{ getTotalTagCount(row) - 2 }}
                    </el-tag>
                  </template>
                  <span v-else class="text-placeholder">-</span>
                </div>
              </template>

              <div class="tags-popover">
                <div v-if="row.sysTag" class="tag-group">
                  <span class="tag-group-title">系统标签:</span>
                  <div class="tag-list">
                    <el-tag
                      v-for="tag in row.sysTag.split(',')"
                      :key="tag"
                      type="warning"
                      size="small"
                      class="popover-tag"
                    >
                      {{ tag.trim() }}
                    </el-tag>
                  </div>
                </div>
                <div v-if="row.isIpv6" class="tag-group">
                  <span class="tag-group-title">网络类型:</span>
                  <el-tag type="info" size="small" class="popover-tag"
                    >IPv6</el-tag
                  >
                </div>
              </div>
            </el-popover>
          </div>
        </template>
      </el-table-column>

      <!-- 备注 -->
      <el-table-column
        prop="note"
        label="备注"
        min-width="80"
        show-overflow-tooltip
        class-name="note-column"
      >
        <template #default="{ row }">
          <span v-if="row.note" class="note-text">
            {{ truncateText(row.note, 10) }}
          </span>
          <span v-else class="text-placeholder">-</span>
        </template>
      </el-table-column>

      <!-- 创建时间 -->
      <el-table-column
        label="创建时间"
        min-width="110"
        max-width="150"
        align="center"
      >
        <template #default="{ row }">
          <span class="time-text">{{ formatDateTime(row.createdAt) }}</span>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        label="操作"
        min-width="160"
        width="180"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <div class="action-buttons">
            <!-- 访问按钮：放在最前面 -->
            <el-button
              v-if="canAccess(row)"
              size="small"
              type="success"
              plain
              circle
              title="在新标签页打开"
              class="action-access"
              @click.stop="handleAccess(row)"
            >
              <el-icon><TopRight /></el-icon>
            </el-button>

            <!-- 主要操作：详情 -->
            <el-button
              size="small"
              type="primary"
              plain
              class="action-primary"
              @click.stop="handleDetail(row)"
            >
              详情
            </el-button>

            <!-- 次要操作：下拉菜单 -->
            <el-dropdown
              trigger="click"
              @command="command => handleDropdownAction(command, row)"
            >
              <el-button
                size="small"
                type="info"
                plain
                circle
                class="action-more"
              >
                <el-icon><MoreFilled /></el-icon>
              </el-button>

              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon><Edit /></el-icon>
                    修改备注
                  </el-dropdown-item>
                  <el-dropdown-item :command="row.isRead ? 'unread' : 'read'">
                    <el-icon><View /></el-icon>
                    {{ row.isRead ? "标记未读" : "标记已读" }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="row.isExcluded ? 'include' : 'exclude'"
                  >
                    <el-icon><Remove /></el-icon>
                    {{ row.isExcluded ? "取消排除" : "排除" }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    <span style="color: #f56c6c">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="localPagination.currentPage"
        v-model:page-size="localPagination.pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="localPagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  View,
  Remove,
  Delete,
  Operation,
  Refresh,
  Setting,
  InfoFilled,
  More,
  MoreFilled,
  Edit,
  TopRight
} from "@element-plus/icons-vue";

// 响应式数据
const tableRef = ref();
const density = ref<"default" | "small" | "large">("default");
const showColumnSettings = ref(false);

// 本地分页状态，避免直接修改props
const localPagination = computed({
  get: () => props.pagination,
  set: value => {
    // 通过emit通知父组件更新分页状态
    emit("update:pagination", value);
  }
});

// 计算属性
const hasDetailInfo = row => {
  return (
    row.org || row.locationCountry || row.locationProvince || row.locationCity
  );
};

const getLocationText = row => {
  return [row.locationCountry, row.locationProvince, row.locationCity]
    .filter(Boolean)
    .join(" ");
};

const getFirstTag = sysTag => {
  return sysTag ? sysTag.split(",")[0] : "";
};

const hasAnyTags = row => {
  return row.sysTag || row.isIpv6;
};

const getDisplayTags = row => {
  if (!row.sysTag) return [];
  return row.sysTag
    .split(",")
    .map(tag => tag.trim())
    .filter(Boolean);
};

const getTotalTagCount = row => {
  let count = 0;
  if (row.sysTag) {
    count += row.sysTag.split(",").filter(tag => tag.trim()).length;
  }
  if (row.isIpv6) {
    count += 1;
  }
  return count;
};

// 智能判断是否显示悬浮效果
const shouldShowPopover = row => {
  // 如果没有标签，不显示悬浮
  if (!hasAnyTags(row)) return false;

  // 获取所有标签
  const displayTags = getDisplayTags(row);
  const allTags = [...displayTags];
  if (row.isIpv6) allTags.push("IPv6");

  // 如果标签总数超过2个，显示悬浮
  if (allTags.length > 2) return true;

  // 计算标签显示的总宽度（估算）
  // 每个标签大约：文字长度*8px + padding 12px + gap 4px
  let totalWidth = 0;
  const visibleTags = allTags.slice(0, 2); // 最多显示2个标签

  visibleTags.forEach(tag => {
    totalWidth += tag.length * 8 + 16; // 文字宽度 + padding
  });
  totalWidth += (visibleTags.length - 1) * 4; // gap

  // 如果估算宽度超过列的最大宽度（约100px），显示悬浮
  return totalWidth > 100;
};

const getRowStatusClass = row => {
  const classes = [];
  if (!row.isRead) classes.push("status-unread");
  if (row.isExcluded) classes.push("status-excluded");
  return classes.join(" ");
};

// 访问相关方法
const canAccess = row => {
  return row.ip && row.port && row.port !== "-";
};

const handleAccess = row => {
  if (!canAccess(row)) return;

  // 构建访问URL
  const protocol = getProtocol(row);
  const url = `${protocol}://${row.ip}:${row.port}`;

  // 在新标签页打开
  window.open(url, "_blank");
};

const getProtocol = row => {
  // 根据端口和其他信息判断协议
  const port = parseInt(row.port);

  // 常见的HTTPS端口
  if (port === 443 || port === 8443) {
    return "https";
  }

  // 常见的HTTP端口
  if (port === 80 || port === 8080 || port === 8000 || port === 3000) {
    return "http";
  }

  // 如果有状态码信息，可能是HTTP服务
  if (row.statusCode) {
    return "http";
  }

  // 默认使用HTTP
  return "http";
};

const truncateText = (text, maxLength) => {
  if (!text) return "";
  return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};

// 方法
const toggleDensity = () => {
  density.value = density.value === "default" ? "small" : "default";
};

const getRowClassName = ({ row }) => {
  const classes = [];
  if (!row.isRead) classes.push("row-unread");
  if (row.isExcluded) classes.push("row-excluded");
  return classes.join(" ");
};

const handleDropdownAction = (command, row) => {
  switch (command) {
    case "edit":
      handleEdit(row);
      break;
    case "read":
    case "unread":
      handleToggleRead(row);
      break;
    case "exclude":
    case "include":
      handleToggleExclude(row);
      break;
    case "delete":
      handleDelete(row);
      break;
  }
};

// 其他方法声明（需要从原组件迁移）
const handleSelectionChange = selection => {
  emit("selection-change", selection);
};

const handleRowClick = row => {
  // 可选：点击行查看详情
};

// 事件发射器
const emit = defineEmits([
  "selection-change",
  "detail",
  "edit",
  "delete",
  "toggle-read",
  "toggle-exclude",
  "batch-read",
  "batch-exclude",
  "batch-delete",
  "refresh",
  "size-change",
  "page-change",
  "update:pagination"
]);

// 方法实现
const handleDetail = row => {
  emit("detail", row);
};

const handleEdit = row => {
  emit("edit", row);
};

const handleDelete = row => {
  emit("delete", row);
};

const handleToggleRead = row => {
  emit("toggle-read", row);
};

const handleToggleExclude = row => {
  emit("toggle-exclude", row);
};

const handleBatchRead = () => {
  emit("batch-read", props.selectedRows);
};

const handleBatchExclude = () => {
  emit("batch-exclude", props.selectedRows);
};

const handleBatchDelete = () => {
  emit("batch-delete", props.selectedRows);
};

const handleRefresh = () => {
  emit("refresh");
};

const showTagsDetail = row => {
  // 显示标签详情弹窗
  console.log("显示标签详情:", row);
};

const getStatusCodeType = code => {
  if (code >= 200 && code < 300) return "success";
  if (code >= 300 && code < 400) return "warning";
  if (code >= 400 && code < 500) return "danger";
  if (code >= 500) return "danger";
  return "info";
};

const formatDateTime = date => {
  if (!date) return "";
  return new Date(date).toLocaleString("zh-CN");
};

const formatRelativeTime = date => {
  if (!date) return "";
  const now = new Date();
  const diff = now.getTime() - new Date(date).getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) return "今天";
  if (days === 1) return "昨天";
  if (days < 7) return `${days}天前`;
  if (days < 30) return `${Math.floor(days / 7)}周前`;
  return `${Math.floor(days / 30)}个月前`;
};

const handleSizeChange = size => {
  emit("size-change", size);
};

const handlePageChange = page => {
  emit("page-change", page);
};

// Props（需要从父组件传入）
const props = defineProps({
  results: { type: Array, default: () => [] },
  loading: { type: Boolean, default: false },
  pagination: { type: Object, required: true },
  selectedRows: { type: Array, default: () => [] }
});
</script>

<style scoped>
/* 表格容器 */
.result-table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* 工具栏样式 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.selection-info {
  color: #606266;
  font-size: 14px;
  margin-left: 12px;
}

/* 表格行状态 */
.row-unread {
  background-color: #f0f9ff !important;
}

.row-excluded {
  background-color: #fef2f2 !important;
  opacity: 0.7;
}

/* 状态指示样式 - 移除背景色 */

/* IP地址单元格 */
.ip-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.ip-text {
  color: #303133;
  line-height: 1.4;
}

.ip-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
}

/* 端口单元格 */
.port-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.port-text {
  color: #303133;
  line-height: 1.4;
}

/* 域名和标题 */
.domain-text,
.title-text {
  color: #303133;
  line-height: 1.4;
}

.text-placeholder {
  color: #c0c4cc;
  font-style: italic;
}

/* 状态码标签 */
.status-tag {
  font-weight: 500;
}

/* 详细信息弹窗 */
.detail-popover {
  padding: 8px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
  line-height: 1.5;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  min-width: 50px;
  margin-right: 8px;
}

.detail-value {
  color: #303133;
  word-break: break-all;
}

/* 标签紧凑显示 */
.tags-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.tags-display {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: help;
  width: 100%;
  gap: 4px;
  flex-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tag-item {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
  padding: 0 6px;
}

.more-tag {
  background-color: #f0f2f5 !important;
  border-color: #d9d9d9 !important;
  color: #666 !important;
}

/* 标签悬浮弹窗 */
.tags-popover {
  padding: 8px 0;
}

.tag-group {
  margin-bottom: 8px;
}

.tag-group:last-child {
  margin-bottom: 0;
}

.tag-group-title {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  display: block;
  margin-bottom: 4px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.popover-tag {
  font-size: 11px;
  height: 18px;
  line-height: 16px;
  padding: 0 4px;
}

/* 备注文本 */
.note-text {
  color: #606266;
  font-size: 13px;
}

/* 时间显示 */
.time-text {
  color: #303133;
  line-height: 1.4;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-access {
  width: 28px;
  height: 28px;
  padding: 0;
  min-height: 28px;
}

.action-primary {
  min-width: 55px;
  font-size: 12px;
  padding: 4px 8px;
}

.action-more {
  width: 28px;
  height: 28px;
  padding: 0;
  min-height: 28px;
}

/* 分页容器 */
.pagination-wrapper {
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
  background: #fafafa;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 - 自适应布局 */
@media (min-width: 1400px) {
  /* 大屏幕：充分利用空间 */
  :deep(.el-table) {
    .el-table__header-wrapper,
    .el-table__body-wrapper {
      .el-table__cell {
        padding: 12px 8px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  /* 中等屏幕：适度压缩 */
  :deep(.el-table) {
    .el-table__header-wrapper,
    .el-table__body-wrapper {
      .el-table__cell {
        padding: 8px 6px;
      }
    }
  }
}

@media (max-width: 768px) {
  .table-toolbar {
    padding: 12px 16px;
  }

  .pagination-wrapper {
    padding: 12px 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-access {
    width: 20px;
    height: 20px;
  }

  .action-primary {
    font-size: 11px;
    padding: 4px 8px;
  }

  /* 小屏幕：紧凑布局 */
  :deep(.el-table) {
    .el-table__header-wrapper,
    .el-table__body-wrapper {
      .el-table__cell {
        padding: 6px 4px;
        font-size: 12px;
      }
    }
  }

  /* 小屏幕下隐藏次要列 */
  :deep(.el-table__header-wrapper),
  :deep(.el-table__body-wrapper) {
    .detail-info-column,
    .note-column {
      display: none;
    }
  }
}

/* 表格密度调整 */
:deep(.el-table--small) {
  .action-access {
    width: 20px;
    height: 20px;
  }

  .action-primary {
    padding: 2px 6px;
    font-size: 11px;
  }

  .tag-item,
  .popover-tag {
    height: 18px;
    line-height: 16px;
    font-size: 10px;
  }
}

/* 表格头部样式 */
:deep(.el-table__header-wrapper) {
  .el-table__header {
    .el-table__cell {
      background-color: #fafafa;
      color: #303133;
      font-weight: 600;
      border-bottom: 2px solid #e4e7ed;
    }
  }
}

/* 表格悬浮效果 */
:deep(.el-table__body) {
  .el-table__row:hover {
    background-color: #f5f7fa !important;
  }
}

/* 下拉菜单样式 */
:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 14px;
    }
  }
}
</style>
