package com.z3rd0.system.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务启动结果DTO
 */
@Data
@NoArgsConstructor
public class TaskStartResult {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 时间范围
     */
    private String timeRange;
    
    /**
     * 结果消息
     */
    private String message;
    
    public TaskStartResult(Long taskId, String taskName, String timeRange, String message) {
        this.taskId = taskId;
        this.taskName = taskName;
        this.timeRange = timeRange;
        this.message = message;
    }
}
