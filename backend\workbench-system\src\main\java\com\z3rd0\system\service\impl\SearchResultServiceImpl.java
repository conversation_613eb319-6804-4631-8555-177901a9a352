package com.z3rd0.system.service.impl;

import com.z3rd0.common.model.SearchResult;
import com.z3rd0.system.repository.SearchResultRepository;
import com.z3rd0.system.service.SearchResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.IOException;

/**
 * 搜索结果服务实现类
 * 提供搜索结果的查询和管理功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SearchResultServiceImpl implements SearchResultService {

    private final SearchResultRepository searchResultRepository;

    @Override
    public Page<SearchResult> findByIpPaged(String ip, int page, int size) {
        // 创建按创建时间倒序排序的分页请求
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));

        if (ip == null || ip.isEmpty()) {
            return searchResultRepository.findAll(pageable);
        } else {
            return searchResultRepository.findByIpContaining(ip, pageable);
        }
    }

    @Override
    public Page<SearchResult> findByConditions(String ip, String domain, String title, String org, String statusCode, String locationCountry, String asn, Integer isRead, Integer isExcluded, String startTime, String endTime, Pageable pageable) {
        Specification<SearchResult> spec = (root, query, cb) -> {
            Predicate p = cb.conjunction();

            // 字符串字段模糊查询
            if (ip != null && !ip.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("ip"), "%" + ip.trim() + "%"));
            }
            if (domain != null && !domain.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("domain"), "%" + domain.trim() + "%"));
            }
            if (title != null && !title.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("title"), "%" + title.trim() + "%"));
            }
            if (org != null && !org.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("org"), "%" + org.trim() + "%"));
            }
            if (locationCountry != null && !locationCountry.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("locationCountry"), "%" + locationCountry.trim() + "%"));
            }
            if (asn != null && !asn.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("asn"), "%" + asn.trim() + "%"));
            }

            // 状态码查询 - 支持多个状态码用逗号分隔
            if (statusCode != null && !statusCode.trim().isEmpty()) {
                String[] codes = statusCode.trim().split(",");
                if (codes.length == 1) {
                    // 单个状态码
                    try {
                        Integer code = Integer.parseInt(codes[0].trim());
                        p = cb.and(p, cb.equal(root.get("statusCode"), code));
                    } catch (NumberFormatException e) {
                        log.warn("状态码格式错误，将忽略此条件: {}", statusCode);
                    }
                } else {
                    // 多个状态码
                    Predicate statusPredicate = cb.disjunction();
                    for (String code : codes) {
                        try {
                            Integer codeInt = Integer.parseInt(code.trim());
                            statusPredicate = cb.or(statusPredicate, cb.equal(root.get("statusCode"), codeInt));
                        } catch (NumberFormatException e) {
                            log.warn("状态码格式错误，将忽略: {}", code);
                        }
                    }
                    p = cb.and(p, statusPredicate);
                }
            }

            // 状态字段精确查询
            if (isRead != null) {
                p = cb.and(p, cb.equal(root.get("isRead"), isRead));
            }
            if (isExcluded != null) {
                p = cb.and(p, cb.equal(root.get("isExcluded"), isExcluded));
            }

            // 时间范围查询 - 添加异常处理
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    LocalDateTime start = LocalDateTime.parse(startTime.trim(), formatter);
                    p = cb.and(p, cb.greaterThanOrEqualTo(root.get("createdAt"), start));
                } catch (DateTimeParseException e) {
                    // 记录时间格式错误，但不影响查询
                    log.warn("开始时间格式错误，将忽略此条件: {}, 错误: {}", startTime, e.getMessage());
                }
            }

            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    LocalDateTime end = LocalDateTime.parse(endTime.trim(), formatter);
                    p = cb.and(p, cb.lessThanOrEqualTo(root.get("createdAt"), end));
                } catch (DateTimeParseException e) {
                    // 记录时间格式错误，但不影响查询
                    log.warn("结束时间格式错误，将忽略此条件: {}, 错误: {}", endTime, e.getMessage());
                }
            }

            return p;
        };
        return searchResultRepository.findAll(spec, pageable);
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 基础统计
        stats.put("totalCount", searchResultRepository.count());
        stats.put("uniqueIpCount", searchResultRepository.countDistinctIp());
        stats.put("uniqueDomainCount", searchResultRepository.countDistinctDomain());

        // 今日新增
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        stats.put("todayCount", searchResultRepository.countByCreatedAtAfter(todayStart));

        // 本周新增（从本周一00:00:00开始）
        LocalDateTime weekStart = getThisWeekStart();
        stats.put("weeklyCount", searchResultRepository.countThisWeekResults(weekStart));

        // 本月新增（从本月1日00:00:00开始）
        LocalDateTime monthStart = getThisMonthStart();
        stats.put("monthlyCount", searchResultRepository.countThisMonthResults(monthStart));

        // 状态码统计
        List<Object[]> statusCodeData = searchResultRepository.getStatusCodeStats();
        List<Map<String, Object>> statusCodeStats = statusCodeData.stream()
            .limit(10) // 取前10个
            .map(row -> {
                Map<String, Object> item = new HashMap<>();
                item.put("statusCode", row[0]);
                item.put("count", row[1]);
                return item;
            })
            .collect(Collectors.toList());
        stats.put("statusCodeStats", statusCodeStats);

        // 地理位置统计
        List<Object[]> locationData = searchResultRepository.getLocationStats();
        List<Map<String, Object>> locationStats = locationData.stream()
            .limit(10) // 取前10个
            .map(row -> {
                Map<String, Object> item = new HashMap<>();
                item.put("location", row[0]);
                item.put("count", row[1]);
                return item;
            })
            .collect(Collectors.toList());
        stats.put("locationStats", locationStats);

        // 端口统计
        List<Object[]> portData = searchResultRepository.getPortStats();
        List<Map<String, Object>> portStats = portData.stream()
            .limit(10) // 取前10个
            .map(row -> {
                Map<String, Object> item = new HashMap<>();
                item.put("port", row[0]);
                item.put("count", row[1]);
                return item;
            })
            .collect(Collectors.toList());
        stats.put("portStats", portStats);

        // 时间趋势统计（最近7天）
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        List<Object[]> timeData = searchResultRepository.getTimeStats(sevenDaysAgo);
        List<Map<String, Object>> timeStats = timeData.stream()
            .map(row -> {
                Map<String, Object> item = new HashMap<>();
                item.put("date", row[0].toString());
                item.put("count", row[1]);
                return item;
            })
            .collect(Collectors.toList());
        stats.put("timeStats", timeStats);

        // 技术栈统计（需要解析techStack字段）
        stats.put("techStats", getTechStackStats());

        return stats;
    }

    private List<Map<String, Object>> getTechStackStats() {
        // 获取所有有技术栈数据的记录
        List<SearchResult> results = searchResultRepository.findAll().stream()
            .filter(sr -> sr.getTechStack() != null && !sr.getTechStack().trim().isEmpty())
            .collect(Collectors.toList());

        // 统计技术栈出现次数
        Map<String, Integer> techCount = new HashMap<>();
        for (SearchResult result : results) {
            try {
                // 简单解析技术栈JSON（假设格式为数组）
                String techStack = result.getTechStack();
                if (techStack.startsWith("[") && techStack.endsWith("]")) {
                    // 简单提取技术名称（这里可以用更复杂的JSON解析）
                    String[] techs = techStack.replaceAll("[\\[\\]\"{}]", "")
                        .split(",");
                    for (String tech : techs) {
                        String cleanTech = tech.trim();
                        if (!cleanTech.isEmpty() && cleanTech.length() > 2) {
                            // 提取技术名称（去掉版本号等）
                            String techName = cleanTech.split(":")[0].trim();
                            if (techName.length() > 1) {
                                techCount.put(techName, techCount.getOrDefault(techName, 0) + 1);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // 忽略解析错误
                log.warn("解析技术栈失败: {}", result.getTechStack());
            }
        }

        // 转换为统计结果并排序
        return techCount.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(20) // 取前20个
            .map(entry -> {
                Map<String, Object> item = new HashMap<>();
                item.put("tech", entry.getKey());
                item.put("count", entry.getValue());
                return item;
            })
            .collect(Collectors.toList());
    }

    @Override
    public void exportToExcel(String ip, String domain, String title, String org, String statusCode,
                             String locationCountry, String asn, Integer isRead, Integer isExcluded,
                             String startTime, String endTime, HttpServletResponse response) throws IOException {

        // 构建查询条件，获取所有符合条件的数据（不分页）
        Specification<SearchResult> spec = (root, query, cb) -> {
            Predicate p = cb.conjunction();

            // 字符串字段模糊查询
            if (ip != null && !ip.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("ip"), "%" + ip.trim() + "%"));
            }
            if (domain != null && !domain.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("domain"), "%" + domain.trim() + "%"));
            }
            if (title != null && !title.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("title"), "%" + title.trim() + "%"));
            }
            if (org != null && !org.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("org"), "%" + org.trim() + "%"));
            }
            if (locationCountry != null && !locationCountry.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("locationCountry"), "%" + locationCountry.trim() + "%"));
            }
            if (asn != null && !asn.trim().isEmpty()) {
                p = cb.and(p, cb.like(root.get("asn"), "%" + asn.trim() + "%"));
            }

            // 状态码查询
            if (statusCode != null && !statusCode.trim().isEmpty()) {
                String[] codes = statusCode.trim().split(",");
                if (codes.length == 1) {
                    try {
                        Integer code = Integer.parseInt(codes[0].trim());
                        p = cb.and(p, cb.equal(root.get("statusCode"), code));
                    } catch (NumberFormatException e) {
                        log.warn("状态码格式错误，将忽略此条件: {}", statusCode);
                    }
                } else {
                    Predicate statusPredicate = cb.disjunction();
                    for (String code : codes) {
                        try {
                            Integer codeInt = Integer.parseInt(code.trim());
                            statusPredicate = cb.or(statusPredicate, cb.equal(root.get("statusCode"), codeInt));
                        } catch (NumberFormatException e) {
                            log.warn("状态码格式错误，将忽略: {}", code);
                        }
                    }
                    p = cb.and(p, statusPredicate);
                }
            }

            // 状态字段精确查询
            if (isRead != null) {
                p = cb.and(p, cb.equal(root.get("isRead"), isRead));
            }
            if (isExcluded != null) {
                p = cb.and(p, cb.equal(root.get("isExcluded"), isExcluded));
            }

            // 时间范围查询
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    LocalDateTime start = LocalDateTime.parse(startTime.trim(), formatter);
                    p = cb.and(p, cb.greaterThanOrEqualTo(root.get("createdAt"), start));
                } catch (DateTimeParseException e) {
                    log.warn("开始时间格式错误，将忽略此条件: {}", startTime);
                }
            }

            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    LocalDateTime end = LocalDateTime.parse(endTime.trim(), formatter);
                    p = cb.and(p, cb.lessThanOrEqualTo(root.get("createdAt"), end));
                } catch (DateTimeParseException e) {
                    log.warn("结束时间格式错误，将忽略此条件: {}", endTime);
                }
            }

            return p;
        };

        List<SearchResult> results = searchResultRepository.findAll(spec);

        // 创建Excel工作簿
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("搜索结果");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "ID", "IP", "端口", "域名", "标题", "组织", "状态码", "国家", "省份", "城市",
                "ASN", "ISP", "是否IPv6", "系统标签", "技术栈", "备注", "创建时间", "已读", "排除"
            };

            // 创建标题样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            int rowNum = 1;
            for (SearchResult result : results) {
                Row row = sheet.createRow(rowNum++);

                row.createCell(0).setCellValue(result.getId() != null ? result.getId().toString() : "");
                row.createCell(1).setCellValue(result.getIp() != null ? result.getIp() : "");
                row.createCell(2).setCellValue(result.getPort() != null ? result.getPort().toString() : "");
                row.createCell(3).setCellValue(result.getDomain() != null ? result.getDomain() : "");
                row.createCell(4).setCellValue(result.getTitle() != null ? result.getTitle() : "");
                row.createCell(5).setCellValue(result.getOrg() != null ? result.getOrg() : "");
                row.createCell(6).setCellValue(result.getStatusCode() != null ? result.getStatusCode().toString() : "");
                row.createCell(7).setCellValue(result.getLocationCountry() != null ? result.getLocationCountry() : "");
                row.createCell(8).setCellValue(result.getLocationProvince() != null ? result.getLocationProvince() : "");
                row.createCell(9).setCellValue(result.getLocationCity() != null ? result.getLocationCity() : "");
                row.createCell(10).setCellValue(result.getAsn() != null ? result.getAsn().toString() : "");
                row.createCell(11).setCellValue(result.getLocationIsp() != null ? result.getLocationIsp() : "");
                row.createCell(12).setCellValue(result.getIsIpv6() != null && result.getIsIpv6() ? "是" : "否");
                row.createCell(13).setCellValue(result.getSysTag() != null ? result.getSysTag() : "");
                row.createCell(14).setCellValue(result.getTechStack() != null ? result.getTechStack() : "");
                row.createCell(15).setCellValue(result.getNote() != null ? result.getNote() : "");
                row.createCell(16).setCellValue(result.getCreatedAt() != null ? result.getCreatedAt().toString() : "");
                row.createCell(17).setCellValue(result.getIsRead() != null && result.getIsRead() == 1 ? "是" : "否");
                row.createCell(18).setCellValue(result.getIsExcluded() != null && result.getIsExcluded() == 1 ? "是" : "否");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=search_results.xlsx");

            // 写入响应
            workbook.write(response.getOutputStream());
        }
    }

    @Override
    @Transactional
    public void batchOperation(List<Long> ids, String operation, Integer value) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("ID列表不能为空");
        }

        int updatedCount = 0;
        switch (operation) {
            case "markRead":
                updatedCount = searchResultRepository.batchUpdateIsRead(ids, value);
                break;
            case "exclude":
                updatedCount = searchResultRepository.batchUpdateIsExcluded(ids, value);
                break;
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + operation);
        }

        log.info("批量操作完成: {} 条记录, 操作: {}, 值: {}", updatedCount, operation, value);
    }

    @Override
    @Transactional
    public void batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("ID列表不能为空");
        }

        int deletedCount = searchResultRepository.batchDeleteByIds(ids);
        log.info("批量删除完成: {} 条记录", deletedCount);
    }

    /**
     * 获取本周开始时间（周一00:00:00）
     * @return 本周一的00:00:00时间
     */
    private LocalDateTime getThisWeekStart() {
        LocalDateTime now = LocalDateTime.now();
        // 获取当前是周几（1=周一，7=周日）
        int dayOfWeek = now.getDayOfWeek().getValue();
        // 计算到周一的天数差
        int daysToSubtract = dayOfWeek - 1;
        // 回到本周一的00:00:00
        return now.minusDays(daysToSubtract)
                  .withHour(0)
                  .withMinute(0)
                  .withSecond(0)
                  .withNano(0);
    }

    /**
     * 获取本月开始时间（1日00:00:00）
     * @return 本月1日的00:00:00时间
     */
    private LocalDateTime getThisMonthStart() {
        LocalDateTime now = LocalDateTime.now();
        // 回到本月1日的00:00:00
        return now.withDayOfMonth(1)
                  .withHour(0)
                  .withMinute(0)
                  .withSecond(0)
                  .withNano(0);
    }
}