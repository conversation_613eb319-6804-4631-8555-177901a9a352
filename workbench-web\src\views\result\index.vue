<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox, ElTooltip } from "element-plus";
import { Download } from "@element-plus/icons-vue";
import OptimizedResultTable from "./optimized-table.vue";

interface SearchResult {
  id?: number;
  originalId?: string;
  taskName?: string;
  rule?: string;

  // 网络基础信息
  ip?: string;
  port?: string;
  domain?: string;
  hostname?: string;
  transport?: string;
  org?: string;

  // 网络扩展信息
  asn?: number;
  isIpv6?: boolean;
  sysTag?: string;

  // 网站信息
  title?: string;
  url?: string;
  path?: string;
  host?: string;
  server?: string;
  xPoweredBy?: string;
  statusCode?: number;

  // 位置信息
  locationIsp?: string;
  locationScene?: string;
  locationCountry?: string;
  locationProvince?: string;
  locationCity?: string;

  // 保留的原有字段
  httpLoadUrl?: string;
  pageType?: string;

  // 技术栈信息
  techStack?: string;

  // ICP备案信息
  icpLicence?: string;
  icpUnit?: string;
  icpNature?: string;

  // 网站元信息
  metaKeywords?: string;
  faviconHash?: string;
  faviconUrl?: string;

  // 时间信息
  time?: string;
  createdAt?: string;
  inTimeRange?: boolean;

  // 内容信息
  content?: string;
  summary?: string;

  // 管理字段
  isRead?: number;
  note?: string;
  isExcluded?: number;
}

// 路由
const route = useRoute();

const results = ref<SearchResult[]>([]);
const dialogVisible = ref(false);
const detailVisible = ref(false);
const editResult = ref<SearchResult>({
  isRead: 0,
  isExcluded: 0
} as SearchResult);
const detailResult = ref<SearchResult>({});

// 搜索相关
const searchForm = ref({
  ip: "",
  domain: "",
  title: "",
  timeRange: [] as string[],
  isRead: "0", // '0'未读，'1'已读，''全部
  isExcluded: "0" // '0'未排除，'1'已排除，''全部
});

const pageSizeOptions = [10, 15, 20, 50, 100];
const pagination = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0
});

// 加载状态
const loading = ref(false);

// 导出状态
const exportLoading = ref(false);

// 选中的行
const selectedRows = ref<any[]>([]);

// 动态页面标题
const pageTitle = computed(() => {
  const ipFilter = searchForm.value.ip;
  if (ipFilter) {
    return `资产列表 - IP: ${ipFilter}`;
  }
  return '资产列表';
});

function formatDate(val?: string) {
  if (!val) return "";
  const d = new Date(val);
  if (isNaN(d.getTime())) return val;
  const pad = (n: number) => n.toString().padStart(2, "0");
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
}

async function fetchResults() {
  loading.value = true;
  try {
    // 构造查询参数
    const params = [];
    if (searchForm.value.ip)
      params.push(`ip=${encodeURIComponent(searchForm.value.ip)}`);
    if (searchForm.value.domain)
      params.push(`domain=${encodeURIComponent(searchForm.value.domain)}`);
    if (searchForm.value.title)
      params.push(`title=${encodeURIComponent(searchForm.value.title)}`);
    if (searchForm.value.timeRange && searchForm.value.timeRange.length === 2) {
      params.push(
        `startTime=${encodeURIComponent(searchForm.value.timeRange[0])}`
      );
      params.push(
        `endTime=${encodeURIComponent(searchForm.value.timeRange[1])}`
      );
    }
    if (searchForm.value.isRead !== "")
      params.push(
        `isRead=${searchForm.value.isRead === "" ? "" : Number(searchForm.value.isRead)}`
      );
    if (searchForm.value.isExcluded !== "")
      params.push(
        `isExcluded=${searchForm.value.isExcluded === "" ? "" : Number(searchForm.value.isExcluded)}`
      );
    params.push(`page=${pagination.value.currentPage - 1}`);
    params.push(`size=${pagination.value.pageSize}`);
    const query = params.length ? `?${params.join("&")}` : "";
    const resp = await fetch(`/api/search/results${query}`);
    const result = await resp.json();

    if (result.success && result.data) {
      // 适配新的PageResponse格式
      results.value = result.data.list || [];
      pagination.value.total = result.data.total || 0;
    } else {
      console.warn("获取搜索结果失败:", result.message);
      results.value = [];
      pagination.value.total = 0;
    }
  } catch (error) {
    console.error("获取搜索结果失败:", error);
    results.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
}

function handleEdit(row: SearchResult) {
  editResult.value = {
    ...row,
    isRead: Number(row.isRead),
    isExcluded: Number(row.isExcluded)
  };
  dialogVisible.value = true;
}

function handleAdd() {
  editResult.value = { isRead: 0, isExcluded: 0 } as SearchResult;
  dialogVisible.value = true;
}

async function handleDelete(row: SearchResult) {
  try {
    await ElMessageBox.confirm("确定要删除该条记录吗？", "提示", {
      type: "warning"
    });
    const response = await fetch(`/api/search/results/${row.id}`, {
      method: "DELETE"
    });
    const result = await response.json();

    if (result.success) {
      ElMessage.success(result.message || "删除成功");
      fetchResults();
    } else {
      ElMessage.error(result.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
      console.error("删除失败:", error);
    }
  }
}

async function handleSave() {
  try {
    if (editResult.value.id) {
      const response = await fetch(
        `/api/search/results/${editResult.value.id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            id: editResult.value.id,
            note: editResult.value.note,
            isRead: editResult.value.isRead,
            isExcluded: editResult.value.isExcluded
          })
        }
      );
      const result = await response.json();

      if (result.success) {
        ElMessage.success(result.message || "修改成功");
        // 只刷新该条数据
        const idx = results.value.findIndex(r => r.id === editResult.value.id);
        if (idx !== -1) {
          results.value[idx].note = editResult.value.note;
          results.value[idx].isRead = editResult.value.isRead;
          results.value[idx].isExcluded = editResult.value.isExcluded;
        }
      } else {
        ElMessage.error(result.message || "修改失败");
      }
    } else {
      const response = await fetch(`/api/search/results`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(editResult.value)
      });
      const result = await response.json();

      if (result.success) {
        ElMessage.success(result.message || "新增成功");
        fetchResults();
      } else {
        ElMessage.error(result.message || "新增失败");
      }
    }
    dialogVisible.value = false;
  } catch (error) {
    ElMessage.error("操作失败");
    console.error("保存失败:", error);
  }
}

function handleDetail(row: SearchResult) {
  detailResult.value = { ...row };
  detailVisible.value = true;
}

function handlePageChange(page) {
  pagination.value.currentPage = page;
  fetchResults();
}
function handleSizeChange(size) {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1;
  fetchResults();
}
function handleSearch() {
  pagination.value.currentPage = 1;
  fetchResults();
}

// 解析技术栈JSON数据
function parseTechStack(techStackJson: string) {
  try {
    if (!techStackJson) return [];
    const techArray = JSON.parse(techStackJson);
    return Array.isArray(techArray) ? techArray : [];
  } catch (error) {
    console.warn("解析技术栈数据失败:", error);
    return [];
  }
}

// 获取状态码对应的标签类型
function getStatusCodeType(statusCode: number) {
  if (statusCode >= 200 && statusCode < 300) return "success";
  if (statusCode >= 300 && statusCode < 400) return "warning";
  if (statusCode >= 400 && statusCode < 500) return "danger";
  if (statusCode >= 500) return "danger";
  return "info";
}

// 处理选择变化
function handleSelectionChange(selection: any[]) {
  selectedRows.value = selection;
}

// 优化组件需要的新方法
async function handleToggleRead(row: SearchResult) {
  editResult.value = { ...row };
  editResult.value.isRead = editResult.value.isRead ? 0 : 1;
  await handleSave();
  // 操作完成后刷新数据
  await fetchResults();
}

async function handleToggleExclude(row: SearchResult) {
  editResult.value = { ...row };
  editResult.value.isExcluded = editResult.value.isExcluded ? 0 : 1;
  await handleSave();
  // 操作完成后刷新数据
  await fetchResults();
}

function loadResults() {
  fetchResults();
}

// Excel导出功能
async function handleExport() {
  exportLoading.value = true;
  try {
    // 构造导出参数（使用当前搜索条件）
    const params = [];
    if (searchForm.value.ip)
      params.push(`ip=${encodeURIComponent(searchForm.value.ip)}`);
    if (searchForm.value.domain)
      params.push(`domain=${encodeURIComponent(searchForm.value.domain)}`);
    if (searchForm.value.title)
      params.push(`title=${encodeURIComponent(searchForm.value.title)}`);
    if (searchForm.value.isRead !== "")
      params.push(`isRead=${searchForm.value.isRead}`);
    if (searchForm.value.isExcluded !== "")
      params.push(`isExcluded=${searchForm.value.isExcluded}`);
    if (searchForm.value.timeRange && searchForm.value.timeRange.length === 2) {
      params.push(
        `startTime=${encodeURIComponent(searchForm.value.timeRange[0])}`
      );
      params.push(
        `endTime=${encodeURIComponent(searchForm.value.timeRange[1])}`
      );
    }

    const queryString = params.length > 0 ? `?${params.join("&")}` : "";
    const url = `/api/search/export${queryString}`;

    // 创建下载链接
    const link = document.createElement("a");
    link.href = url;
    link.download = `搜索结果_${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 批量操作函数
async function batchOperation(operation: string, value: number) {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请先选择要操作的记录");
    return;
  }

  try {
    const ids = selectedRows.value.map(row => row.id);
    const response = await fetch("/api/search/batch", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        ids,
        operation,
        value
      })
    });

    const result = await response.json();
    if (result.success) {
      ElMessage.success(`批量${operation}成功`);
      selectedRows.value = [];
      // 操作完成后刷新数据
      await fetchResults();
    } else {
      ElMessage.error(result.message || `批量${operation}失败`);
    }
  } catch (error) {
    console.error(`批量${operation}失败:`, error);
    ElMessage.error(`批量${operation}失败`);
  }
}

// 批量标记已读
function batchMarkRead() {
  batchOperation("markRead", 1);
}

// 批量标记未读
function batchMarkUnread() {
  batchOperation("markRead", 0);
}

// 批量排除
function batchExclude() {
  batchOperation("exclude", 1);
}

// 批量取消排除
function batchInclude() {
  batchOperation("exclude", 0);
}

// 批量删除
async function batchDelete() {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请先选择要删除的记录");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？此操作不可恢复！`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const ids = selectedRows.value.map(row => row.id);
    const response = await fetch("/api/search/batch-delete", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ ids })
    });

    const result = await response.json();
    if (result.success) {
      ElMessage.success("批量删除成功");
      selectedRows.value = [];
      // 操作完成后刷新数据
      await fetchResults();
    } else {
      ElMessage.error(result.message || "批量删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("批量删除失败:", error);
      ElMessage.error("批量删除失败");
    }
  }
}

onMounted(() => {
  // 检查是否从其他页面跳转过来并带有IP过滤参数
  const ipFromQuery = route.query.ip as string;
  const fromPage = route.query.from as string;

  if (ipFromQuery) {
    // 自动填充IP搜索条件
    searchForm.value.ip = ipFromQuery;

    // 根据来源页面显示不同的提示信息
    if (fromPage === 'ip-detail') {
      ElMessage.success(`已自动筛选IP: ${ipFromQuery} 的相关资产`);
    } else if (fromPage === 'ip-list') {
      ElMessage.success(`正在查看IP: ${ipFromQuery} 的所有资产记录`);
    } else {
      ElMessage.success(`已自动筛选IP: ${ipFromQuery} 的相关资产`);
    }
  }

  fetchResults();
});
</script>

<template>
  <el-card>
    <div class="flex justify-between mb-4">
      <span class="font-bold text-lg">{{ pageTitle }}</span>
    </div>
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="searchForm" class="mb-4">
      <div style="display: flex; flex-wrap: wrap; align-items: flex-end">
        <el-form-item label="IP">
          <el-input
            v-model="searchForm.ip"
            placeholder="IP模糊搜索"
            clearable
          />
        </el-form-item>
        <el-form-item label="域名">
          <el-input
            v-model="searchForm.domain"
            placeholder="域名模糊搜索"
            clearable
          />
        </el-form-item>
        <el-form-item label="标题">
          <el-input
            v-model="searchForm.title"
            placeholder="标题模糊搜索"
            clearable
          />
        </el-form-item>

        <el-form-item label="查询时间">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 320px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button
            @click="
              () => {
                searchForm.ip = '';
                searchForm.domain = '';
                searchForm.title = '';
                searchForm.timeRange = [];
                searchForm.isRead = '0';
                searchForm.isExcluded = '0';
                handleSearch();
              }
            "
            >重置</el-button
          >
          <el-button
            type="success"
            :loading="exportLoading"
            :disabled="!results.length"
            @click="handleExport"
          >
            <el-icon><Download /></el-icon>
            导出Excel
          </el-button>
        </el-form-item>
      </div>
      <div
        style="
          display: flex;
          flex-wrap: wrap;
          align-items: flex-end;
          margin-top: 4px;
        "
      >
        <el-form-item label="是否已读">
          <el-select
            v-model="searchForm.isRead"
            placeholder="请选择"
            style="width: 100px"
          >
            <el-option label="未读" value="0" />
            <el-option label="已读" value="1" />
            <el-option label="全部" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否排除">
          <el-select
            v-model="searchForm.isExcluded"
            placeholder="请选择"
            style="width: 100px"
          >
            <el-option label="未排除" value="0" />
            <el-option label="已排除" value="1" />
            <el-option label="全部" value="" />
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <!-- 使用优化后的结果表格组件 -->
    <OptimizedResultTable
      :results="results"
      :loading="loading"
      :pagination="pagination"
      :selected-rows="selectedRows"
      @selection-change="handleSelectionChange"
      @detail="handleDetail"
      @edit="handleEdit"
      @delete="handleDelete"
      @toggle-read="handleToggleRead"
      @toggle-exclude="handleToggleExclude"
      @batch-read="batchMarkRead"
      @batch-exclude="batchExclude"
      @batch-delete="batchDelete"
      @refresh="loadResults"
      @size-change="handleSizeChange"
      @page-change="handlePageChange"
    />

    <!-- 编辑备注弹窗 -->
    <el-dialog v-model="dialogVisible" title="修改" width="500px">
      <el-form :model="editResult" label-width="100px">
        <el-form-item label="备注"
          ><el-input v-model="editResult.note" type="textarea" :rows="3"
        /></el-form-item>
        <el-form-item label="是否已读">
          <el-switch
            v-model="editResult.isRead"
            :active-value="1"
            :inactive-value="0"
            active-text="已读"
            inactive-text="未读"
          />
        </el-form-item>
        <el-form-item label="是否排除">
          <el-switch
            v-model="editResult.isExcluded"
            :active-value="1"
            :inactive-value="0"
            active-text="已排除"
            inactive-text="未排除"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>
    <!-- 详情弹窗 -->
    <el-dialog v-model="detailVisible" title="详情" width="900px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">{{
          detailResult.id
        }}</el-descriptions-item>
        <el-descriptions-item label="原始ID">{{
          detailResult.originalId
        }}</el-descriptions-item>
        <el-descriptions-item label="任务名">{{
          detailResult.taskName
        }}</el-descriptions-item>
        <el-descriptions-item label="规则">{{
          detailResult.rule
        }}</el-descriptions-item>
        <el-descriptions-item label="IP">{{
          detailResult.ip
        }}</el-descriptions-item>
        <el-descriptions-item label="端口">{{
          detailResult.port
        }}</el-descriptions-item>
        <el-descriptions-item label="域名">{{
          detailResult.domain
        }}</el-descriptions-item>
        <el-descriptions-item label="主机名">{{
          detailResult.hostname
        }}</el-descriptions-item>
        <el-descriptions-item label="传输协议">{{
          detailResult.transport
        }}</el-descriptions-item>
        <el-descriptions-item label="组织">{{
          detailResult.org
        }}</el-descriptions-item>
        <el-descriptions-item label="ASN">{{
          detailResult.asn
        }}</el-descriptions-item>
        <el-descriptions-item label="IPv6支持">{{
          detailResult.isIpv6 ? "是" : "否"
        }}</el-descriptions-item>
        <el-descriptions-item label="系统标签">{{
          detailResult.sysTag
        }}</el-descriptions-item>
        <el-descriptions-item label="标题">{{
          detailResult.title
        }}</el-descriptions-item>
        <el-descriptions-item label="URL">{{
          detailResult.url
        }}</el-descriptions-item>
        <el-descriptions-item label="路径">{{
          detailResult.path
        }}</el-descriptions-item>
        <el-descriptions-item label="主机">{{
          detailResult.host
        }}</el-descriptions-item>
        <el-descriptions-item label="服务器">{{
          detailResult.server
        }}</el-descriptions-item>
        <el-descriptions-item label="X-Powered-By">{{
          detailResult.xPoweredBy
        }}</el-descriptions-item>
        <el-descriptions-item label="状态码">{{
          detailResult.statusCode
        }}</el-descriptions-item>
        <el-descriptions-item label="运营商">{{
          detailResult.locationIsp
        }}</el-descriptions-item>
        <el-descriptions-item label="场景">{{
          detailResult.locationScene
        }}</el-descriptions-item>
        <el-descriptions-item label="国家">{{
          detailResult.locationCountry
        }}</el-descriptions-item>
        <el-descriptions-item label="省份">{{
          detailResult.locationProvince
        }}</el-descriptions-item>
        <el-descriptions-item label="城市">{{
          detailResult.locationCity
        }}</el-descriptions-item>
        <el-descriptions-item label="页面类型">{{
          detailResult.pageType
        }}</el-descriptions-item>
        <el-descriptions-item label="HTTP加载URL">{{
          detailResult.httpLoadUrl
        }}</el-descriptions-item>
        <el-descriptions-item label="ICP备案号">{{
          detailResult.icpLicence
        }}</el-descriptions-item>
        <el-descriptions-item label="ICP备案单位">{{
          detailResult.icpUnit
        }}</el-descriptions-item>
        <el-descriptions-item label="ICP备案性质">{{
          detailResult.icpNature
        }}</el-descriptions-item>
        <el-descriptions-item label="网站关键词">{{
          detailResult.metaKeywords
        }}</el-descriptions-item>
        <el-descriptions-item label="Favicon哈希">{{
          detailResult.faviconHash
        }}</el-descriptions-item>
        <el-descriptions-item label="Favicon URL">{{
          detailResult.faviconUrl
        }}</el-descriptions-item>
        <el-descriptions-item label="数据时间">{{
          formatDate(detailResult.time)
        }}</el-descriptions-item>
        <el-descriptions-item label="技术栈" :span="2">
          <div v-if="detailResult.techStack">
            <div
              v-for="(tech, index) in parseTechStack(detailResult.techStack)"
              :key="index"
              class="tech-item"
            >
              <el-tag type="primary" size="small">{{ tech.name }}</el-tag>
              <span v-if="tech.vendor" class="tech-vendor">{{
                tech.vendor
              }}</span>
              <span v-if="tech.version" class="tech-version"
                >v{{ tech.version }}</span
              >
            </div>
          </div>
          <span v-else>无</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否已读">{{
          detailResult.isRead == 1 ? "已读" : "未读"
        }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{
          detailResult.note
        }}</el-descriptions-item>
        <el-descriptions-item label="是否排除">{{
          detailResult.isExcluded == 1 ? "已排除" : "未排除"
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          formatDate(detailResult.createdAt)
        }}</el-descriptions-item>
        <el-descriptions-item label="内容">{{
          detailResult.content
        }}</el-descriptions-item>
        <el-descriptions-item label="摘要">{{
          detailResult.summary
        }}</el-descriptions-item>
        <el-descriptions-item label="时间范围内">{{
          detailResult.inTimeRange ? "是" : "否"
        }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<style scoped>
.font-bold {
  font-weight: bold;
}

.el-descriptions__label {
  min-width: 120px;
  word-break: break-all;
}

.el-descriptions__content {
  word-break: break-all;
}

.action-btns {
  display: flex;
  gap: 0;
  justify-content: center;
}

.action-btns .el-button {
  padding-right: 10px;
  padding-left: 10px;
  margin: 0 4px;
}

.tech-item {
  display: inline-flex;
  align-items: center;
  margin: 2px 8px 2px 0;
  padding: 4px 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.tech-vendor {
  margin-left: 8px;
  color: #606266;
  font-size: 11px;
}

.tech-version {
  margin-left: 4px;
  color: #909399;
  font-size: 11px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tags-container .el-tag {
  margin: 0;
}

.batch-actions {
  margin-bottom: 16px;
}

.batch-buttons {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 详情对话框样式优化 */
:deep(.el-descriptions) {
  .el-descriptions__table {
    table-layout: fixed;
  }

  .el-descriptions__cell {
    width: 50% !important;
    word-break: break-all;
    word-wrap: break-word;
  }

  .el-descriptions__label {
    width: 120px !important;
    min-width: 120px;
    white-space: nowrap;
  }

  .el-descriptions__content {
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-wrap;
    max-width: calc(50% - 120px);
  }
}
</style>
