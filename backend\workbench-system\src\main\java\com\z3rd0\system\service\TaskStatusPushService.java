package com.z3rd0.system.service;

import com.z3rd0.common.model.Task;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 任务状态推送服务
 * 负责向前端实时推送任务状态变更、进度更新等信息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskStatusPushService {
    
    private final SimpMessagingTemplate messagingTemplate;
    
    // 存储任务的实时进度信息
    private final ConcurrentMap<Long, TaskProgress> taskProgressMap = new ConcurrentHashMap<>();
    
    /**
     * 推送任务状态变更
     * 
     * @param task 任务对象
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     */
    public void pushTaskStatusChange(Task task, String oldStatus, String newStatus) {
        try {
            Map<String, Object> message = new HashMap<>();
            message.put("type", "STATUS_CHANGE");
            message.put("taskId", task.getId());
            message.put("taskName", task.getName());
            message.put("oldStatus", oldStatus);
            message.put("newStatus", newStatus);
            message.put("timestamp", LocalDateTime.now().toString());
            message.put("errorMessage", task.getErrorMessage());
            
            // 推送到所有订阅任务状态的客户端
            messagingTemplate.convertAndSend("/topic/task/status", message);
            
            // 推送到特定任务的订阅者
            messagingTemplate.convertAndSend("/topic/task/" + task.getId() + "/status", message);
            
            log.debug("推送任务状态变更: 任务ID={}, 状态: {} -> {}", task.getId(), oldStatus, newStatus);
        } catch (Exception e) {
            log.error("推送任务状态变更失败: 任务ID={}, 错误={}", task.getId(), e.getMessage(), e);
        }
    }
    
    /**
     * 推送任务进度更新
     * 
     * @param taskId 任务ID
     * @param progress 进度信息
     */
    public void pushTaskProgress(Long taskId, TaskProgress progress) {
        try {
            // 更新进度缓存
            taskProgressMap.put(taskId, progress);
            
            Map<String, Object> message = new HashMap<>();
            message.put("type", "PROGRESS_UPDATE");
            message.put("taskId", taskId);
            message.put("progress", progress);
            message.put("timestamp", LocalDateTime.now().toString());
            
            // 推送到所有订阅任务进度的客户端
            messagingTemplate.convertAndSend("/topic/task/progress", message);
            
            // 推送到特定任务的订阅者
            messagingTemplate.convertAndSend("/topic/task/" + taskId + "/progress", message);
            
            log.debug("推送任务进度更新: 任务ID={}, 进度={}%", taskId, progress.getPercentage());
        } catch (Exception e) {
            log.error("推送任务进度更新失败: 任务ID={}, 错误={}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 推送任务执行日志
     * 
     * @param taskId 任务ID
     * @param logLevel 日志级别
     * @param message 日志消息
     */
    public void pushTaskLog(Long taskId, String logLevel, String message) {
        try {
            Map<String, Object> logMessage = new HashMap<>();
            logMessage.put("type", "LOG_MESSAGE");
            logMessage.put("taskId", taskId);
            logMessage.put("level", logLevel);
            logMessage.put("message", message);
            logMessage.put("timestamp", LocalDateTime.now().toString());
            
            // 推送到特定任务的日志订阅者
            messagingTemplate.convertAndSend("/topic/task/" + taskId + "/logs", logMessage);
            
            log.debug("推送任务日志: 任务ID={}, 级别={}, 消息={}", taskId, logLevel, message);
        } catch (Exception e) {
            log.error("推送任务日志失败: 任务ID={}, 错误={}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 推送系统通知
     * 
     * @param notificationType 通知类型
     * @param title 通知标题
     * @param content 通知内容
     * @param level 通知级别 (info, warning, error)
     */
    public void pushSystemNotification(String notificationType, String title, String content, String level) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("type", "SYSTEM_NOTIFICATION");
            notification.put("notificationType", notificationType);
            notification.put("title", title);
            notification.put("content", content);
            notification.put("level", level);
            notification.put("timestamp", LocalDateTime.now().toString());
            
            // 推送到所有订阅系统通知的客户端
            messagingTemplate.convertAndSend("/topic/system/notifications", notification);
            
            log.info("推送系统通知: 类型={}, 标题={}, 级别={}", notificationType, title, level);
        } catch (Exception e) {
            log.error("推送系统通知失败: 错误={}", e.getMessage(), e);
        }
    }
    
    /**
     * 推送资源监控信息
     * 
     * @param memoryUsageRatio 内存使用率
     * @param browserCount 浏览器数量
     * @param status 系统状态
     */
    public void pushResourceMonitor(double memoryUsageRatio, int browserCount, String status) {
        try {
            Map<String, Object> resourceInfo = new HashMap<>();
            resourceInfo.put("type", "RESOURCE_MONITOR");
            resourceInfo.put("memoryUsageRatio", memoryUsageRatio);
            resourceInfo.put("browserCount", browserCount);
            resourceInfo.put("status", status);
            resourceInfo.put("timestamp", LocalDateTime.now().toString());
            
            // 推送到所有订阅资源监控的客户端
            messagingTemplate.convertAndSend("/topic/system/resources", resourceInfo);
            
            log.debug("推送资源监控信息: 内存使用率={:.2f}%, 浏览器数量={}, 状态={}", 
                     memoryUsageRatio * 100, browserCount, status);
        } catch (Exception e) {
            log.error("推送资源监控信息失败: 错误={}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取任务当前进度
     * 
     * @param taskId 任务ID
     * @return 任务进度，如果不存在则返回null
     */
    public TaskProgress getTaskProgress(Long taskId) {
        return taskProgressMap.get(taskId);
    }
    
    /**
     * 清理已完成任务的进度信息
     * 
     * @param taskId 任务ID
     */
    public void clearTaskProgress(Long taskId) {
        taskProgressMap.remove(taskId);
        log.debug("清理任务进度信息: 任务ID={}", taskId);
    }
    
    /**
     * 任务进度信息
     */
    public static class TaskProgress {
        private final int totalSteps;
        private final int currentStep;
        private final String currentStepName;
        private final String description;
        private final LocalDateTime startTime;
        private final LocalDateTime lastUpdateTime;
        
        public TaskProgress(int totalSteps, int currentStep, String currentStepName, String description) {
            this.totalSteps = totalSteps;
            this.currentStep = currentStep;
            this.currentStepName = currentStepName;
            this.description = description;
            this.startTime = LocalDateTime.now();
            this.lastUpdateTime = LocalDateTime.now();
        }
        
        /**
         * 计算进度百分比
         */
        public double getPercentage() {
            if (totalSteps <= 0) return 0.0;
            return Math.min(100.0, (double) currentStep / totalSteps * 100.0);
        }
        
        /**
         * 是否已完成
         */
        public boolean isCompleted() {
            return currentStep >= totalSteps;
        }
        
        /**
         * 获取运行时长（秒）
         */
        public long getRuntimeSeconds() {
            return java.time.Duration.between(startTime, LocalDateTime.now()).getSeconds();
        }
        
        // Getters
        public int getTotalSteps() { return totalSteps; }
        public int getCurrentStep() { return currentStep; }
        public String getCurrentStepName() { return currentStepName; }
        public String getDescription() { return description; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
    }
}
