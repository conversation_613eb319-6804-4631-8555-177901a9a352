package com.z3rd0.workbench.event;

import java.time.LocalDateTime;

/**
 * 任务事件基类
 */
public abstract class TaskEvent {
    private final String taskName;
    private final LocalDateTime timestamp;
    
    public TaskEvent(String taskName) {
        this.taskName = taskName;
        this.timestamp = LocalDateTime.now();
    }
    
    public String getTaskName() {
        return taskName;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
} 