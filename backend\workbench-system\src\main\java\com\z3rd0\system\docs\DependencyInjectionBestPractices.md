# 依赖注入最佳实践指南

## 概述

本文档描述了Workbench项目中依赖注入的最佳实践，确保代码的可测试性、可维护性和一致性。

## 推荐的依赖注入方式

### 1. 构造器注入（推荐）

**使用@RequiredArgsConstructor**

```java
@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {
    
    private final TaskRepository taskRepository;
    private final TaskPublisher taskPublisher;
    
    // 业务方法...
}
```

**优点：**
- 确保依赖的不可变性（final字段）
- 便于单元测试（可以直接传入mock对象）
- 在对象创建时就确保所有依赖都已注入
- 避免循环依赖问题
- 代码简洁，减少样板代码

### 2. 手动构造器注入（特殊情况）

```java
@RestController
@RequestMapping("/api")
public class SystemController {
    
    private final TaskPublisher taskPublisher;
    private final SearchResultService searchResultService;
    
    public SystemController(TaskPublisher taskPublisher, 
                          SearchResultService searchResultService) {
        this.taskPublisher = taskPublisher;
        this.searchResultService = searchResultService;
    }
    
    // 控制器方法...
}
```

**使用场景：**
- 需要在构造器中进行额外的初始化逻辑
- 依赖注入需要特殊处理

## 不推荐的依赖注入方式

### 1. 字段注入（避免使用）

```java
// ❌ 不推荐
@Service
public class BadService {
    
    @Autowired
    private TaskRepository taskRepository;
    
    @Autowired
    private TaskPublisher taskPublisher;
}
```

**缺点：**
- 字段不能声明为final
- 难以进行单元测试
- 隐藏了类的依赖关系
- 可能导致空指针异常

### 2. Setter注入（特殊情况才使用）

```java
// ⚠️ 仅在可选依赖时使用
@Service
public class ConditionalService {
    
    private OptionalDependency optionalDependency;
    
    @Autowired(required = false)
    public void setOptionalDependency(OptionalDependency optionalDependency) {
        this.optionalDependency = optionalDependency;
    }
}
```

## 配置值注入

### 使用@Value注解

```java
@Component
public class TianYanChaUtils {
    
    @Value("${tianyancha.token}")
    private String token;
    
    @Value("${tianyancha.tycId}")
    private String tycId;
    
    @Value("#{'${companyName}'.split(',')}")
    private String[] companyNames;
}
```

### 使用@ConfigurationProperties（推荐）

```java
@ConfigurationProperties(prefix = "tianyancha")
@Component
@Data
public class TianYanChaProperties {
    
    private String token;
    private String tycId;
    private String version;
    private List<String> companyNames;
}
```

## Bean配置最佳实践

### 1. 明确Bean依赖关系

```java
@Configuration
public class RabbitMQConfig {
    
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 配置ObjectMapper
        return objectMapper;
    }
    
    @Bean
    public MessageConverter jsonMessageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }
    
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, 
                                       MessageConverter messageConverter) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter);
        return rabbitTemplate;
    }
}
```

### 2. 避免循环依赖

```java
// ❌ 避免循环依赖
@Service
public class ServiceA {
    private final ServiceB serviceB;
    
    public ServiceA(ServiceB serviceB) {
        this.serviceB = serviceB;
    }
}

@Service
public class ServiceB {
    private final ServiceA serviceA; // 循环依赖
    
    public ServiceB(ServiceA serviceA) {
        this.serviceA = serviceA;
    }
}
```

**解决方案：**
- 重新设计类的职责
- 使用事件驱动架构
- 引入中介者模式

## 单元测试支持

### 构造器注入的测试优势

```java
@ExtendWith(MockitoExtension.class)
class TaskServiceImplTest {
    
    @Mock
    private TaskRepository taskRepository;
    
    @Mock
    private TaskPublisher taskPublisher;
    
    private TaskServiceImpl taskService;
    
    @BeforeEach
    void setUp() {
        // 直接通过构造器创建，便于测试
        taskService = new TaskServiceImpl(taskRepository, taskPublisher);
    }
    
    @Test
    void testSaveTask() {
        // 测试逻辑...
    }
}
```

## 检查清单

在代码审查时，请检查以下项目：

- [ ] 是否使用了构造器注入而不是字段注入
- [ ] 依赖字段是否声明为final
- [ ] 是否使用了@RequiredArgsConstructor简化代码
- [ ] 配置类中的Bean依赖关系是否明确
- [ ] 是否避免了循环依赖
- [ ] 单元测试是否容易编写

## 迁移指南

### 从字段注入迁移到构造器注入

1. 移除@Autowired注解
2. 将字段声明为final
3. 添加@RequiredArgsConstructor注解
4. 更新单元测试

```java
// 迁移前
@Service
public class OldService {
    @Autowired
    private TaskRepository taskRepository;
}

// 迁移后
@Service
@RequiredArgsConstructor
public class NewService {
    private final TaskRepository taskRepository;
}
```

## 总结

遵循这些依赖注入最佳实践可以：

1. 提高代码的可测试性
2. 增强代码的可读性和可维护性
3. 减少运行时错误
4. 提供更好的IDE支持
5. 符合Spring框架的推荐做法

记住：**构造器注入 + @RequiredArgsConstructor = 最佳实践**
