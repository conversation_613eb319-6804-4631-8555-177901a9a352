/**
 * 服务状态检查器
 * 用于检查后端服务的可用性，并提供友好的用户提示
 */

import { ElNotification } from "element-plus";

export interface ServiceStatus {
  name: string;
  url: string;
  available: boolean;
  error?: string;
}

/**
 * 检查单个服务的状态
 */
async function checkService(name: string, url: string, timeout = 5000): Promise<ServiceStatus> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
      }
    });

    clearTimeout(timeoutId);

    return {
      name,
      url,
      available: response.ok,
      error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
    };
  } catch (error: any) {
    return {
      name,
      url,
      available: false,
      error: error.name === 'AbortError' ? '请求超时' : error.message
    };
  }
}

/**
 * 检查所有关键服务的状态
 */
export async function checkAllServices(): Promise<ServiceStatus[]> {
  const services = [
    { name: 'System API', url: '/api/system/status' },
    { name: 'Scanner API', url: '/api/scanner/status' },
    { name: 'Tasks API', url: '/api/tasks/execution-stats' },
    { name: 'Assets API', url: '/api/asset/stats' }
  ];

  const results = await Promise.all(
    services.map(service => checkService(service.name, service.url))
  );

  return results;
}

/**
 * 显示服务状态通知
 */
export function showServiceStatus(services: ServiceStatus[]): void {
  const unavailableServices = services.filter(s => !s.available);

  if (unavailableServices.length === 0) {
    // 所有服务都可用
    ElNotification({
      title: "服务状态",
      message: "所有后端服务运行正常",
      type: "success",
      duration: 3000
    });
    return;
  }

  if (unavailableServices.length === services.length) {
    // 所有服务都不可用
    ElNotification({
      title: "服务离线",
      message: "后端服务暂时不可用，应用将以离线模式运行。部分功能可能受限。",
      type: "warning",
      duration: 8000,
      showClose: true
    });
    return;
  }

  // 部分服务不可用
  const unavailableNames = unavailableServices.map(s => s.name).join(', ');
  ElNotification({
    title: "部分服务离线",
    message: `以下服务暂时不可用：${unavailableNames}。相关功能可能受限。`,
    type: "warning",
    duration: 6000,
    showClose: true
  });
}

/**
 * 初始化服务检查
 */
export async function initServiceCheck(): Promise<void> {
  try {
    console.log('正在检查后端服务状态...');
    const services = await checkAllServices();

    // 记录详细状态到控制台
    services.forEach(service => {
      if (service.available) {
        console.log(`✅ ${service.name}: 可用`);
      } else {
        console.warn(`❌ ${service.name}: 不可用 - ${service.error}`);
      }
    });

    // 显示用户友好的通知
    showServiceStatus(services);

    // 将服务状态存储到全局状态中，供其他组件使用
    (window as any).__SERVICE_STATUS__ = services;

  } catch (error) {
    console.error('服务状态检查失败:', error);
    ElNotification({
      title: "服务检查失败",
      message: "无法检查后端服务状态，请检查网络连接",
      type: "error",
      duration: 5000
    });
  }
}

/**
 * 获取当前服务状态
 */
export function getServiceStatus(): ServiceStatus[] {
  return (window as any).__SERVICE_STATUS__ || [];
}

/**
 * 检查特定服务是否可用
 */
export function isServiceAvailable(serviceName: string): boolean {
  const services = getServiceStatus();
  const service = services.find(s => s.name === serviceName);
  return service?.available ?? false;
}
