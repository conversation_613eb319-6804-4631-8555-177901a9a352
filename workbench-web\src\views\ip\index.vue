<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>IP资产管理</span>
          <div class="button-group">
            <el-button type="primary" @click="handleRefresh">
              <IconifyIconOffline icon="ep:refresh" class="mr-1" />
              刷新
            </el-button>
            <el-button type="success" @click="handleSync">
              <IconifyIconOffline icon="ep:connection" class="mr-1" />
              同步IP资产
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="searchForm" inline>
        <el-form-item label="IP地址">
          <el-input
            v-model="searchForm.ipPattern"
            placeholder="请输入IP地址"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="非活跃" value="INACTIVE" />
          </el-select>
        </el-form-item>
        <el-form-item label="国家">
          <el-input
            v-model="searchForm.country"
            placeholder="请输入国家"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="ISP">
          <el-input
            v-model="searchForm.isp"
            placeholder="请输入ISP"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="最小资产数">
          <el-input-number
            v-model="searchForm.minAssetCount"
            :min="1"
            placeholder="最小资产数"
            style="width: 120px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <IconifyIconOffline icon="ep:search" class="mr-1" />
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            title="总IP数量"
            :value="stats.totalIps || 0"
            :precision="0"
          >
            <template #suffix>
              <el-icon><Monitor /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            title="活跃IP"
            :value="stats.activeIps || 0"
            :precision="0"
          >
            <template #suffix>
              <el-icon style="color: #67c23a"><CircleCheck /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            title="IPv6地址"
            :value="stats.ipv6Count || 0"
            :precision="0"
          >
            <template #suffix>
              <el-icon style="color: #409eff"><Connection /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic
            title="总资产数"
            :value="stats.totalAssets || 0"
            :precision="0"
          >
            <template #suffix>
              <el-icon style="color: #e6a23c"><Files /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <!-- IP列表表格 -->
    <el-card class="box-card mt-4" shadow="never">
      <el-table
        v-loading="loading"
        :data="ipList"
        border
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="50" align="center" />

        <el-table-column prop="ipAddress" label="IP地址" min-width="120" fixed="left">
          <template #default="{ row }">
            <div class="ip-cell">
              <span class="ip-address">{{ row.ipAddress }}</span>
              <el-tag v-if="row.isIpv6" type="info" size="small" class="ipv6-tag">
                IPv6
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" min-width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'ACTIVE' ? 'success' : 'info'"
              size="small"
            >
              {{ row.status === 'ACTIVE' ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="assetCount" label="资产数量" min-width="90" align="center">
          <template #default="{ row }">
            <el-link
              type="primary"
              @click.stop="viewAssets(row)"
              :disabled="row.assetCount === 0"
            >
              {{ row.assetCount }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="地理位置" min-width="150" max-width="200">
          <template #default="{ row }">
            <div class="location-info">
              <div v-if="row.locationCountry">
                {{ row.locationCountry }}
                <span v-if="row.locationProvince">{{ row.locationProvince }}</span>
                <span v-if="row.locationCity">{{ row.locationCity }}</span>
              </div>
              <span v-else class="text-placeholder">未知</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="isp" label="ISP" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.isp">{{ row.isp }}</span>
            <span v-else class="text-placeholder">未知</span>
          </template>
        </el-table-column>

        <el-table-column prop="asn" label="ASN" min-width="80" align="center">
          <template #default="{ row }">
            <span v-if="row.asn">{{ row.asn }}</span>
            <span v-else class="text-placeholder">-</span>
          </template>
        </el-table-column>

        <el-table-column label="端口信息" min-width="100" max-width="150">
          <template #default="{ row }">
            <div v-if="row.commonPorts" class="ports-info">
              <el-tag
                v-for="port in parsePortsFromJson(row.commonPorts).slice(0, 3)"
                :key="port"
                size="small"
                class="port-tag"
              >
                {{ port }}
              </el-tag>
              <span v-if="parsePortsFromJson(row.commonPorts).length > 3">
                +{{ parsePortsFromJson(row.commonPorts).length - 3 }}
              </span>
            </div>
            <span v-else class="text-placeholder">无</span>
          </template>
        </el-table-column>

        <el-table-column prop="lastSeen" label="最后发现" min-width="110" align="center">
          <template #default="{ row }">
            <el-tooltip :content="formatDateTime(row.lastSeen)">
              <span class="time-text">{{ formatRelativeTime(row.lastSeen) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="notes" label="备注" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="notes-cell">
              <span
                v-if="!row.editingNotes"
                @click="startEditNotes(row)"
                class="notes-display"
                :class="{ 'notes-empty': !row.notes }"
              >
                {{ row.notes || '点击添加备注' }}
              </span>
              <el-input
                v-else
                v-model="row.tempNotes"
                size="small"
                @blur="saveNotes(row)"
                @keyup.enter="saveNotes(row)"
                @keyup.esc="cancelEditNotes(row)"
                placeholder="输入备注信息"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="140" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                size="small"
                type="primary"
                plain
                @click="handleAction('assets', row)"
                title="查看该IP的所有资产"
              >
                <el-icon><Files /></el-icon>
                资产
              </el-button>

              <el-button
                size="small"
                type="danger"
                plain
                @click="handleAction('delete', row)"
                title="删除IP记录"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { IconifyIconOffline } from "@/components/ReIcon";
import {
  Monitor,
  CircleCheck,
  Connection,
  Files,
  Delete
} from "@element-plus/icons-vue";

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const ipList = ref<any[]>([]);
const selectedIps = ref<any[]>([]);
const stats = ref<any>({});

const searchForm = reactive({
  ipPattern: "",
  status: "",
  country: "",
  isp: "",
  minAssetCount: null as number | null
});

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 方法定义
async function fetchIpList() {
  loading.value = true;
  try {
    const params = new URLSearchParams({
      page: (pagination.currentPage - 1).toString(),
      size: pagination.pageSize.toString(),
      sortBy: "lastSeen",
      sortDir: "desc"
    });

    // 添加搜索条件
    if (searchForm.ipPattern) params.append("ipPattern", searchForm.ipPattern);
    if (searchForm.status) params.append("status", searchForm.status);
    if (searchForm.country) params.append("country", searchForm.country);
    if (searchForm.isp) params.append("isp", searchForm.isp);
    if (searchForm.minAssetCount) params.append("minAssetCount", searchForm.minAssetCount.toString());

    const response = await fetch(`/api/ips?${params}`);
    const result = await response.json();

    if (result.success && result.data) {
      ipList.value = result.data.list || [];
      pagination.total = result.data.total || 0;
    } else {
      ElMessage.error(result.message || "获取IP列表失败");
      ipList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    ElMessage.error("获取IP列表失败");
    console.error("获取IP列表失败:", error);
  } finally {
    loading.value = false;
  }
}

async function fetchStats() {
  try {
    const response = await fetch("/api/ips/stats");
    const result = await response.json();

    if (result.success && result.data) {
      stats.value = result.data;
    }
  } catch (error) {
    console.error("获取统计信息失败:", error);
  }
}

function parsePortsFromJson(portsJson: string): string[] {
  if (!portsJson) return [];
  try {
    return JSON.parse(portsJson) as string[];
  } catch {
    return [];
  }
}

function formatDateTime(dateTime: string): string {
  if (!dateTime) return "-";
  return new Date(dateTime).toLocaleString("zh-CN");
}

function formatRelativeTime(dateTime: string): string {
  if (!dateTime) return "-";
  const now = new Date();
  const date = new Date(dateTime);
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return "今天";
  if (diffDays === 1) return "昨天";
  if (diffDays < 7) return `${diffDays}天前`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
  return `${Math.floor(diffDays / 30)}个月前`;
}

function handleSearch() {
  pagination.currentPage = 1;
  fetchIpList();
}

function resetSearch() {
  Object.assign(searchForm, {
    ipPattern: "",
    status: "",
    country: "",
    isp: "",
    minAssetCount: null
  });
  handleSearch();
}

function handleRefresh() {
  fetchIpList();
  fetchStats();
}

async function handleSync() {
  try {
    const response = await fetch("/api/ips/update-status", { method: "POST" });
    const result = await response.json();

    if (result.success) {
      ElMessage.success("IP资产同步完成");
      handleRefresh();
    } else {
      ElMessage.error(result.message || "同步失败");
    }
  } catch (error) {
    ElMessage.error("同步失败");
    console.error("同步失败:", error);
  }
}

function handleSelectionChange(selection: any[]) {
  selectedIps.value = selection;
}

// 增强的IP地址参数验证
function validateIpAddress(ipAddress: string | undefined | null): boolean {
  // 多重验证条件
  if (!ipAddress ||
      typeof ipAddress !== 'string' ||
      ipAddress.trim() === '' ||
      ipAddress === 'undefined' ||
      ipAddress === 'null' ||
      ipAddress.length === 0) {

    console.error('Invalid IP address for navigation:', ipAddress);
    ElMessage.error('IP地址无效，无法跳转到详情页面');
    return false;
  }

  // 基本的IP地址格式验证（可选）
  const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^[a-fA-F0-9:]+$/;
  if (!ipRegex.test(ipAddress.trim())) {
    console.warn('IP address format may be invalid:', ipAddress);
    // 不阻止跳转，只是警告，因为可能有特殊格式的IP
  }

  return true;
}



function handleRowClick(row: any) {
  // 点击行时跳转到资产列表页面查看该IP的资产
  viewAssets(row);
}

function viewAssets(row: any) {
  // 跳转到资产列表页面，并传递IP过滤参数
  if (!validateIpAddress(row.ipAddress)) {
    ElMessage.error("IP地址无效");
    return;
  }

  router.push({
    path: '/asset/index',
    query: {
      ip: row.ipAddress,
      from: 'ip-list'
    }
  });
}

function handleAction(command: string, row: any) {
  switch (command) {
    case "assets":
      // 查看资产：跳转到资产列表页面
      viewAssets(row);
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

// 备注编辑相关方法
function startEditNotes(row: any) {
  row.editingNotes = true;
  row.tempNotes = row.notes || '';
  // 使用nextTick确保DOM更新后再聚焦
  nextTick(() => {
    // 通过类名查找输入框并聚焦
    const inputs = document.querySelectorAll('.notes-cell .el-input__inner');
    const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
    if (lastInput) {
      lastInput.focus();
    }
  });
}

async function saveNotes(row: any) {
  if (row.tempNotes === row.notes) {
    // 内容没有变化，直接取消编辑
    cancelEditNotes(row);
    return;
  }

  try {
    const response = await fetch(`/api/ips/${row.id}/notes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ notes: row.tempNotes })
    });

    const result = await response.json();
    if (result.success) {
      row.notes = row.tempNotes;
      ElMessage.success('备注保存成功');
    } else {
      ElMessage.error(result.message || '保存失败');
    }
  } catch (error) {
    ElMessage.error('保存失败');
    console.error('保存备注失败:', error);
  } finally {
    row.editingNotes = false;
  }
}

function cancelEditNotes(row: any) {
  row.editingNotes = false;
  row.tempNotes = '';
}

async function handleDelete(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除IP资产 ${row.ipAddress} 吗？`,
      "确认删除",
      { type: "warning" }
    );

    const response = await fetch(`/api/ips/${row.id}`, { method: "DELETE" });
    const result = await response.json();

    if (result.success) {
      ElMessage.success("删除成功");
      fetchIpList();
      fetchStats();
    } else {
      ElMessage.error(result.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
      console.error("删除失败:", error);
    }
  }
}

function handleSizeChange(size: number) {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  fetchIpList();
}

function handlePageChange(page: number) {
  pagination.currentPage = page;
  fetchIpList();
}

// 生命周期
onMounted(() => {
  fetchIpList();
  fetchStats();
});
</script>

<style scoped>
.main {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-group {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin: 20px 0;
}

.stats-card {
  text-align: center;
}

.mt-4 {
  margin-top: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.ip-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ip-address {
  font-family: monospace;
  font-weight: 500;
}

.ipv6-tag {
  font-size: 10px;
}

.location-info {
  font-size: 12px;
}

.ports-info {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.port-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
}

.time-text {
  color: #909399;
  font-size: 12px;
  cursor: help;
}

.text-placeholder {
  color: #c0c4cc;
  font-style: italic;
}

.notes-cell {
  min-height: 24px;
  display: flex;
  align-items: center;
}

.notes-display {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 20px;
  display: flex;
  align-items: center;
  width: 100%;
}

.notes-display:hover {
  background-color: #f5f7fa;
}

.notes-empty {
  color: #c0c4cc;
  font-style: italic;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 56px;
  height: 28px;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons .el-button .el-icon {
  margin-right: 2px;
  font-size: 12px;
}

.action-buttons .el-button--primary.is-plain {
  color: #409eff;
  border-color: #409eff;
}

.action-buttons .el-button--primary.is-plain:hover {
  background-color: #409eff;
  color: white;
}

.action-buttons .el-button--danger.is-plain {
  color: #f56c6c;
  border-color: #f56c6c;
}

.action-buttons .el-button--danger.is-plain:hover {
  background-color: #f56c6c;
  color: white;
}
</style>
