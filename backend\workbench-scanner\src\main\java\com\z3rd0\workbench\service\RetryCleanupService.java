package com.z3rd0.workbench.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 重试清理服务
 * 定期清理过期的重试历史记录，防止内存泄漏
 */
@Service
public class RetryCleanupService {
    
    private static final Logger logger = LoggerFactory.getLogger(RetryCleanupService.class);
    
    @Autowired
    private RetryStrategyManager retryStrategyManager;
    
    /**
     * 每小时清理一次过期的重试历史
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupExpiredRetryHistories() {
        try {
            logger.debug("开始清理过期的重试历史记录");
            retryStrategyManager.cleanupExpiredHistories();
            logger.debug("重试历史记录清理完成");
        } catch (Exception e) {
            logger.error("清理重试历史记录时发生异常: {}", e.getMessage(), e);
        }
    }
}
