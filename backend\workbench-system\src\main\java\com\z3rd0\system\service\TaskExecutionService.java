package com.z3rd0.system.service;

import com.z3rd0.common.model.TaskExecutionDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 任务执行详情服务接口
 * 提供任务执行详情的查询和统计功能
 */
public interface TaskExecutionService {
    
    /**
     * 根据条件分页查询任务执行详情
     * @param taskName 任务名称（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param pageable 分页参数
     * @return 执行详情分页结果
     */
    Page<TaskExecutionDetail> findByConditions(String taskName, String startDate, String endDate, Pageable pageable);
    
    /**
     * 根据条件查询任务执行详情列表
     * @param taskName 任务名称（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 执行详情列表
     */
    List<TaskExecutionDetail> findByConditions(String taskName, String startDate, String endDate);
    
    /**
     * 获取任务执行统计信息
     * @param taskName 任务名称（可选）
     * @return 统计信息
     */
    Map<String, Object> getExecutionStats(String taskName);
    
    /**
     * 根据ID查找执行详情
     * @param id 执行详情ID
     * @return 执行详情（可能不存在）
     */
    Optional<TaskExecutionDetail> findById(Long id);
    
    /**
     * 根据任务ID查找执行详情
     * @param taskId 任务ID
     * @return 执行详情（可能不存在）
     */
    Optional<TaskExecutionDetail> findByTaskId(Long taskId);
    
    /**
     * 获取所有不同的任务名称
     * @return 任务名称列表
     */
    List<String> getAllTaskNames();
    
    /**
     * 获取指定任务的最新执行详情
     * @param taskName 任务名称
     * @return 最新执行详情（可能不存在）
     */
    Optional<TaskExecutionDetail> getLatestExecution(String taskName);
    
    /**
     * 获取最近的执行详情
     * @param days 最近天数
     * @return 执行详情列表
     */
    List<TaskExecutionDetail> getRecentExecutions(int days);

    /**
     * 根据执行状态统计执行记录数
     * @param executionStatus 执行状态
     * @return 执行记录数
     */
    Long countByExecutionStatus(String executionStatus);
}
